import { Modu<PERSON> } from '@nestjs/common';
import { AdminApiClient, TaskApiClient } from 'src/shared/clients';
import { AttachmentApiClient } from 'src/shared/clients/attachment-api.client';
import { DatabaseHelper, SequlizeOperator } from 'src/shared/helpers';
import { SharedPermissionService } from 'src/shared/services';
import { AttachmentController } from './controllers/attachment.controller';
import { AttachmentService } from './services/attachment.service';

const repositories = [
];

@Module({
	controllers: [AttachmentController],
	providers: [
		...repositories,
		SharedPermissionService,
		AttachmentService,
		DatabaseHelper,
		AdminApiClient,
		AttachmentApiClient,
		SequlizeOperator,
		TaskApiClient
	],
})
export class AttachmentModule { }
