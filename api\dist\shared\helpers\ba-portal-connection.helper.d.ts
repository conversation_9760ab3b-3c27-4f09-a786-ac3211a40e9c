import { Sequelize } from 'sequelize-typescript';
import { ConfigService } from 'src/config/config.service';
export declare class BAPortalConnectionHelper {
    private readonly configService;
    private readonly logger;
    private connections;
    constructor(configService: ConfigService);
    private initializeConnections;
    getConnection(name?: string): Sequelize | null;
    executeQuery(query: string, connectionName?: string): Promise<any[]>;
}
