import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsArray, IsNotEmpty, IsNumber, IsOptional } from 'class-validator';

export class RefreshKpiDto {
    @ApiProperty({ 
        name: 'model_id', 
        type: Number, 
        description: 'Model ID to refresh all scenarios for',
        example: 1
    })
    @Expose({ name: 'model_id' })
    @IsNotEmpty()
    @IsNumber()
    public model_id: number;

    @ApiProperty({ 
        name: 'scenario_ids', 
        type: [Number], 
        description: 'Optional array of specific scenario IDs to refresh. If not provided, all scenarios for the model will be refreshed.',
        example: [1, 2, 3],
        required: false
    })
    @Expose({ name: 'scenario_ids' })
    @IsOptional()
    @IsArray()
    public scenario_ids?: number[];
}
