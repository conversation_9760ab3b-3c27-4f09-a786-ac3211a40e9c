"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TcSubmitModule = void 0;
const common_1 = require("@nestjs/common");
const repositories_1 = require("./repositories");
const services_1 = require("./services");
const controllers_1 = require("./controllers");
const services_2 = require("../tc-config/services");
const kpi_calculator_service_1 = require("../tc-config/services/kpi-calculator.service");
const repositories_2 = require("../tc-config/repositories");
const helpers_1 = require("../shared/helpers");
const repositories = [
    repositories_1.ModelRepository,
    repositories_1.ModelScenarioRepository,
    repositories_1.ModelScenarioDataRepository,
    repositories_2.BuSetupRepository,
    repositories_2.KpiConfigRepository,
    repositories_2.KpiDataRepository
];
let TcSubmitModule = class TcSubmitModule {
};
TcSubmitModule = __decorate([
    (0, common_1.Module)({
        providers: [
            services_1.TcSubmitService,
            services_2.TcConfigService,
            kpi_calculator_service_1.KpiCalculatorService,
            helpers_1.SequlizeOperator,
            ...repositories
        ],
        controllers: [controllers_1.TcSubmitController],
    })
], TcSubmitModule);
exports.TcSubmitModule = TcSubmitModule;
//# sourceMappingURL=tcsubmit.module.js.map