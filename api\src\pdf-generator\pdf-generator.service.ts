import { Injectable } from '@nestjs/common';
import * as Handlebars from 'handlebars';
import * as puppeteer from 'puppeteer';
import * as fs from 'fs';
import path from 'path';
import { currencyFormatter, formatDateString } from 'src/shared/helpers';

@Injectable()
export class PdfGeneratorService {
    public async generatePdf(data: any, templateName: string): Promise<Buffer> {
        const templatePath = path.join(__dirname, `templates/${templateName}.hbs`);
        this.setCustomHandlebarsHelpers();
        const template = Handlebars.compile(fs.readFileSync(templatePath, 'utf8'));
        const html = template(data);

        const browser = await puppeteer.launch({
            headless: true,
            args: ['--font-render-hinting=none', '--no-sandbox', '--disabled-setupid-sandbox']
        });
        const page = await browser.newPage();
        await page.setContent(html);
        const pdf = await page.pdf({ format: 'A4' });
        await browser.close();
        return pdf;
    }

    /**
     * Set custom handlebars helpers
     */
    private setCustomHandlebarsHelpers() {
        Handlebars.registerHelper('eq', (value1, value2) => {
            return value1 === value2;
        });

        Handlebars.registerHelper('neq', (value1, value2) => {
            return value1 !== value2;
        });

        Handlebars.registerHelper('or', (value1, value2) => {
            return value1 || value2;
        });


        Handlebars.registerHelper('and', (value1, value2) => {
            return value1 && value2;
        });

        Handlebars.registerHelper('contains', (str, substr) => {
            if (str.indexOf(substr) !== -1) {
                return true;
            } else {
                return false;
            }
        });

        Handlebars.registerHelper('formattedCurrency', (amount, currency) => {
            return currencyFormatter(amount, currency);
        });

        Handlebars.registerHelper('formattedDate', (date) => {
            return formatDateString(date);
        });
    }
}
