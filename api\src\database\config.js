const dotenv = require('dotenv');
const fs = require('fs');
const env = dotenv.parse(fs.readFileSync('.env'));

module.exports = {
	[env['NODE_ENV']]: {
		dialect: env['DB_DIALECT'],
		host: env['DB_HOST'],
		port: +env['DB_PORT'],
		username: env['DB_USER'],
		password: env['DB_PASS'],
		database: env['DB_NAME'],
		schema: env['DB_SCHEMA'],
		...((env['DB_ENABLE_SSL'].toLowerCase() == 'true' ? true : false) && {
			ssl: true,
			dialectOptions: {
				ssl: {
					require: true,
				},
			},
		}),
	},
};
