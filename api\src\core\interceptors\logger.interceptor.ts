import {
	<PERSON><PERSON><PERSON><PERSON>,
	ExecutionContext,
	HttpException,
	HttpStatus,
	Injectable,
	NestInterceptor,
} from '@nestjs/common';
import { Response } from 'express';
import { EMPTY, Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import * as platform from 'platform';
import { LoggerService } from '../services';

/**
 * Interceptor that logs input/output requests
 */
@Injectable()
export class LoggingInterceptor implements NestInterceptor {
	constructor(private readonly logger: LoggerService) {}
	private readonly HEALTH_CHECK_URL = '/api/health';
	private now: number;

	/**
	 * Intercept method, logs after the request being processed
	 * @param context details about the current request
	 * @param call implements the handle method that returns an Observable
	 */
	public intercept(context: ExecutionContext, call: CallHandler): Observable<unknown> {
		this.now = Date.now();
		const url = context.switchToHttp().getRequest().url;
		if (url !== this.HEALTH_CHECK_URL) {
			return call.handle().pipe(
				tap({
					next: (_: unknown): void => {
						this.logNext(context);
					},
					error: (err: Error): void => {
						this.logError(err, context);
					},
				}),
			);
		}
		return EMPTY;
	}

	/**
	 * Logs the request response in success cases
	 * @param body body returned
	 * @param context details about the current request
	 */
	private logNext(context: ExecutionContext): void {
		const req = context.switchToHttp().getRequest();
		const res: Response = context.switchToHttp().getResponse();
		const { method, url } = req;
		const { statusCode } = res;
		const message: string = `Response - ${statusCode} - ${method} - ${url}`;

		const logObject = this.getLogObject(req, statusCode);
		this.logger.log({ message, ...logObject });
	}

	/**
	 * Logs the request response in Error cases
	 * @param error Error object
	 * @param context details about the current request
	 */
	private logError(error: Error, context: ExecutionContext): void {
		const req = context.switchToHttp().getRequest();
		const { method, url } = req;

		if (error instanceof HttpException) {
			const statusCode: number = error.getStatus();
			const message: string = `Response - ${statusCode} - ${method} - ${url}`;
			const logObject = this.getLogObject(req, statusCode);

			if (statusCode >= HttpStatus.INTERNAL_SERVER_ERROR) {
				this.logger.error({ message, ...logObject, error }, error.stack);
			} else {
				this.logger.warn({ message, ...logObject, error });
			}
		} else {
			const statusCode: number = 500;
			const logObject = this.getLogObject(req, statusCode);
			this.logger.error(
				{ ...logObject, message: `Response - ${statusCode} - ${method} - ${url}` },
				error.stack,
			);
		}
	}

	/**
	 * Construct an object for logger
	 * @param request client request object
	 * @param response details about the current response
	 * @param statusCode of response
	 */
	private getLogObject(request, statusCode) {
		const { method, url, body, headers, connection, user, query, params } = request;
		const clientDeviceInfo = platform.parse(headers['user-agent']);
		const { name, version, os, description } = clientDeviceInfo;

		const useragent_details = {
			device: {
				family: description,
			},
			os: { ...os },
			browser: {
				family: name,
				version,
			},
		};

		const network = {
			client: {
				ip: this.getIpAddress(headers, connection),
			},
		};

		const http = {
			method,
			url_details: { path: this.getPathFromUrl(url) },
			status_code: statusCode,
			useragent_details,
		};

		const auth =
			user && user.id ? { protectedUrl: true, userId: user.id } : { protectedUrl: false };
		delete headers.authorization;

		return {
			body,
			query,
			params,
			http,
			network,
			responseTime: `${Date.now() - this.now}ms`,
			auth,
			headers,
		};
	}

	private getIpAddress(headers, connection): string {
		return headers['x-forwarded-for'] || connection.remoteAddress;
	}

	private getPathFromUrl(url: string): string {
		return url.toLowerCase().split(/[?#]/)[0];
	}
}
