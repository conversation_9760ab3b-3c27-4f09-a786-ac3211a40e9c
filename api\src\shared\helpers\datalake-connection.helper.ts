import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from 'src/config/config.service';
import axios, { AxiosInstance } from 'axios';

interface DatabricksConnection {
	httpClient: AxiosInstance;
	config: any;
}

@Injectable()
export class DatalakeConnectionHelper {
	private readonly logger = new Logger(DatalakeConnectionHelper.name);
	private connections: Map<string, DatabricksConnection> = new Map();

	constructor(private readonly configService: ConfigService) {
		this.initializeConnections();
	}

	/**
	 * Initialize connections to Databricks
	 */
	private initializeConnections(): void {
		const { sourceConfig } = this.configService.getAppConfig();
		if (!sourceConfig || !sourceConfig.databricks) {
			this.logger.warn('No Databricks configuration found');
			return;
		}

		try {
			const { jdbcUrl, username, password, httpPath } = sourceConfig.databricks;

			// Extract host from JDBC URL for HTTP API
			const hostMatch = jdbcUrl.match(/jdbc:databricks:\/\/([^:\/]+)/);
			const host = hostMatch ? hostMatch[1] : '';

			if (!host) {
				throw new Error('Invalid JDBC URL format');
			}

			// Create HTTP client for Databricks SQL API
			const httpClient = axios.create({
				baseURL: `https://${host}`,
				timeout: 300000, // 5 minutes
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Basic ${Buffer.from(`${username}:${password}`).toString('base64')}`,
				},
			});

			const connection: DatabricksConnection = {
				httpClient,
				config: {
					host,
					username,
					password,
					httpPath,
					jdbcUrl,
				},
			};

			// Test the connection by making a simple query
			this.testConnection(connection)
				.then(() => {
					this.logger.log(`Successfully connected to Databricks: ${host}`);
					this.connections.set('default', connection);
				})
				.catch((error: any) => {
					this.logger.error(`Failed to connect to Databricks: ${error.message}`);
				});
		} catch (error: any) {
			this.logger.error(`Error initializing Databricks connection: ${error.message}`);
		}
	}

	/**
	 * Test Databricks connection
	 */
	private async testConnection(connection: DatabricksConnection): Promise<void> {
		try {
			// Use Databricks SQL API to test connection
			const response = await connection.httpClient.post('/api/2.0/sql/statements', {
				statement: 'SELECT 1 as test',
				warehouse_id: connection.config.httpPath?.split('/').pop(),
				wait_timeout: '10s',
			});

			if (response.status !== 200) {
				throw new Error(`Connection test failed with status: ${response.status}`);
			}
		} catch (error: any) {
			throw new Error(`Connection test failed: ${error.message}`);
		}
	}

	/**
	 * Get a connection to Databricks
	 * @param name Connection name (default: 'default')
	 * @returns Databricks connection instance
	 */
	public getConnection(name: string = 'default'): DatabricksConnection | null {
		if (!this.connections.has(name)) {
			this.logger.warn(`Databricks connection '${name}' not found`);
			return null;
		}
		return this.connections.get(name) || null;
	}

	/**
	 * Execute a query on Databricks using SQL API
	 * @param query SQL query to execute
	 * @param connectionName Connection name (default: 'default')
	 * @returns Query results
	 */
	public async executeQuery(
		query: string,
		connectionName: string = 'default',
	): Promise<any[]> {
		const connection = this.getConnection(connectionName);
		if (!connection) {
			throw new Error(`Databricks connection '${connectionName}' not found`);
		}

		try {
			// Extract warehouse ID from httpPath
			const warehouseId = connection.config.httpPath?.split('/').pop();
			if (!warehouseId) {
				throw new Error('Warehouse ID not found in httpPath');
			}

			// Execute query using Databricks SQL API
			const response = await connection.httpClient.post('/api/2.0/sql/statements', {
				statement: query,
				warehouse_id: warehouseId,
				wait_timeout: '300s', // 5 minutes
				on_wait_timeout: 'CANCEL',
			});

			if (response.status !== 200) {
				throw new Error(`Query execution failed with status: ${response.status}`);
			}

			// Extract results from response
			const result = response.data;
			if (result.status?.state === 'SUCCEEDED') {
				return result.result?.data_array || [];
			} else if (result.status?.state === 'FAILED') {
				throw new Error(`Query failed: ${result.status.error?.message || 'Unknown error'}`);
			} else {
				throw new Error(`Query in unexpected state: ${result.status?.state}`);
			}
		} catch (error: any) {
			this.logger.error(`Error executing query on Databricks: ${error.message}`);
			throw error;
		}
	}
}
