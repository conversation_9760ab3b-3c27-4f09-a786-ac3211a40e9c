import { Injectable, Logger } from '@nestjs/common';
import { Dialect, QueryTypes } from 'sequelize';
import { Sequelize } from 'sequelize-typescript';
import { ConfigService } from 'src/config/config.service';

@Injectable()
export class DatalakeConnectionHelper {
	private readonly logger = new Logger(DatalakeConnectionHelper.name);
	private connections: Map<string, Sequelize> = new Map();

	constructor(private readonly configService: ConfigService) {
		this.initializeConnections();
	}

	/**
	 * Initialize connections to Azure Synapse
	 */
	private initializeConnections(): void {
		const { sourceConfig } = this.configService.getAppConfig();
		if (!sourceConfig || !sourceConfig.synapse) {
			this.logger.warn('No Azure Synapse configuration found');
			return;
		}

		try {
			const { database, username, password, port, host, dialect } = sourceConfig.synapse;

			const sequelize = new Sequelize(database, username, password, {
				dialect: dialect as Dialect,
				host,
				port,
				logging: false,
				dialectOptions: {
					ssl: {
						require: true,
					},
          options: {
            requestTimeout: 300000 // 5 minutes in milliseconds
          }
				},
				ssl: true,
				pool: {
					max: 5,
					min: 0,
					acquire: 30000,
					idle: 10000,
				},
			});

			// Test the connection
			sequelize
				.authenticate()
				.then(() => {
					this.logger.log(`Successfully connected to Azure Synapse: ${database}`);
					this.connections.set('default', sequelize);
				})
				.catch(error => {
					this.logger.error(`Failed to connect to Azure Synapse: ${error.message}`);
				});
		} catch (error) {
			this.logger.error(`Error initializing Azure Synapse connection: ${error.message}`);
		}
	}

	/**
	 * Get a connection to Azure Synapse
	 * @param name Connection name (default: 'default')
	 * @returns Sequelize connection instance
	 */
	public getConnection(name: string = 'default'): Sequelize | null {
		if (!this.connections.has(name)) {
			this.logger.warn(`Azure Synapse connection '${name}' not found`);
			return null;
		}
		return this.connections.get(name);
	}

	/**
	 * Execute a query on Azure Synapse
	 * @param query SQL query to execute
	 * @param connectionName Connection name (default: 'default')
	 * @returns Query results
	 */
	public async executeQuery(
		query: string,
		connectionName: string = 'default',
	): Promise<any[]> {
		const connection = this.getConnection(connectionName);
		if (!connection) {
			throw new Error(`Azure Synapse connection '${connectionName}' not found`);
		}

		try {
			const results = await connection.query(query, {
        type: QueryTypes.SELECT,
        raw: true,
    });
			return results as any[];
		} catch (error) {
			this.logger.error(`Error executing query on Azure Synapse: ${error.message}`);
			throw error;
		}
	}
}
