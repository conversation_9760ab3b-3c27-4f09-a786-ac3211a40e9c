import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from 'src/config/config.service';
import axios, { AxiosInstance } from 'axios';

interface DatabricksConnection {
	jdbcUrl: string;
	username: string;
	password: string;
	httpClient: AxiosInstance;
	host: string;
	database: string;
	httpPath: string;
	catalog?: string;
	schema?: string;
	isConnected: boolean;
}

@Injectable()
export class DatalakeConnectionHelper {
	private readonly logger = new Logger(DatalakeConnectionHelper.name);
	private connections: Map<string, DatabricksConnection> = new Map();

	constructor(private readonly configService: ConfigService) {
		this.initializeConnections();
	}

	/**
	 * Initialize connections to Databricks
	 */
	private initializeConnections(): void {
		const { sourceConfig } = this.configService.getAppConfig();
		if (!sourceConfig || !sourceConfig.databricks) {
			this.logger.warn('No Databricks configuration found');
			return;
		}

		try {
			const { jdbcUrl, username, password, database, schema } = sourceConfig.databricks;

			// Parse JDBC URL to extract host, database, and httpPath
			const jdbcMatch = jdbcUrl.match(/jdbc:databricks:\/\/([^:\/]+):[^\/]*\/([^;?]+).*httpPath=([^;&]+)/);
			if (!jdbcMatch) {
				throw new Error('Invalid JDBC URL format. Expected format: ******************************************************************************');
			}

			const host = jdbcMatch[1];
			const httpPath = decodeURIComponent(jdbcMatch[3]);

			this.logger.log(`Connecting to Databricks host: ${host}, database: ${database}`);

			// Create HTTP client for Databricks SQL API (simulating JDBC behavior)
			// Handle both username/password and token authentication
			let authHeader: string;
			if (username.toLowerCase() === 'token') {
				// Token-based authentication
				authHeader = `Bearer ${password}`;
			} else {
				// Username/password authentication
				authHeader = `Basic ${Buffer.from(`${username}:${password}`).toString('base64')}`;
			}

			const httpClient = axios.create({
				baseURL: `https://${host}`,
				timeout: 300000, // 5 minutes
				headers: {
					'Content-Type': 'application/json',
					'Authorization': authHeader,
				},
			});

			const connection: DatabricksConnection = {
				jdbcUrl,
				username,
				password,
				httpClient,
				host,
				database,
				httpPath,
				catalog,
				schema,
				isConnected: false,
			};

			// Test the connection
			this.testConnection(connection)
				.then(() => {
					connection.isConnected = true;
					this.logger.log(`Successfully connected to Databricks via JDBC: ${host}`);
					this.connections.set('default', connection);
				})
				.catch((error: any) => {
					this.logger.error(`Failed to connect to Databricks: ${error.message}`);
				});
		} catch (error: any) {
			this.logger.error(`Error initializing Databricks connection: ${error.message}`);
		}
	}

	/**
	 * Test Databricks connection
	 */
	private async testConnection(connection: DatabricksConnection): Promise<void> {
		try {
			// Extract warehouse ID from httpPath
			const warehouseId = connection.httpPath.split('/').pop();
			if (!warehouseId) {
				throw new Error('Warehouse ID not found in httpPath');
			}

			this.logger.log(`Testing connection to warehouse: ${warehouseId}`);

			// Test connection by executing a simple query
			const requestBody = {
				statement: 'SELECT 1 as test',
				warehouse_id: warehouseId,
				wait_timeout: '30s', // Within the 5-50 second range
				format: 'JSON_ARRAY',
				disposition: 'INLINE'
			};

			const response = await connection.httpClient.post('/api/2.0/sql/statements', requestBody);

			if (response.status !== 200) {
				this.logger.error(`Connection test HTTP status: ${response.status}`);
				this.logger.error(`Response: ${JSON.stringify(response.data, null, 2)}`);
				throw new Error(`Connection test failed with status: ${response.status}`);
			}

			const result = response.data;
			this.logger.log(`Connection test result: ${result.status?.state}`);

			if (result.status?.state === 'FAILED') {
				const errorMsg = result.status.error?.message || 'Unknown error';
				this.logger.error(`Connection test failed: ${errorMsg}`);
				throw new Error(`Connection test failed: ${errorMsg}`);
			}

			if (result.status?.state === 'SUCCEEDED') {
				this.logger.log('Connection test successful');
			}
		} catch (error: any) {
			if (error.response) {
				this.logger.error(`HTTP Error ${error.response.status}: ${error.response.statusText}`);
				this.logger.error(`Response data: ${JSON.stringify(error.response.data, null, 2)}`);
			}
			throw new Error(`Connection test failed: ${error.message}`);
		}
	}

	/**
	 * Get a connection to Databricks
	 * @param name Connection name (default: 'default')
	 * @returns Databricks connection instance
	 */
	public getConnection(name: string = 'default'): DatabricksConnection | null {
		if (!this.connections.has(name)) {
			this.logger.warn(`Databricks connection '${name}' not found`);
			return null;
		}
		return this.connections.get(name) || null;
	}

	/**
	 * Execute a query on Databricks using JDBC-style connection
	 * @param query SQL query to execute
	 * @param connectionName Connection name (default: 'default')
	 * @returns Query results
	 */
	public async executeQuery(
		query: string,
		connectionName: string = 'default',
	): Promise<any[]> {
		const connection = this.getConnection(connectionName);
		if (!connection) {
			throw new Error(`Databricks connection '${connectionName}' not found`);
		}

		if (!connection.isConnected) {
			throw new Error(`Databricks connection '${connectionName}' is not connected`);
		}

		try {
			// Extract warehouse ID from httpPath
			const warehouseId = connection.httpPath.split('/').pop();
			if (!warehouseId) {
				throw new Error('Warehouse ID not found in httpPath');
			}

			this.logger.log(`Executing query on warehouse: ${warehouseId}, catalog: ${connection.catalog}, schema: ${connection.schema}`);
			this.logger.log(`Query: ${query.substring(0, 100)}...`);

			// Set the catalog and schema if specified
			let finalQuery = query;
			const setupCommands: string[] = [];

			if (connection.catalog) {
				setupCommands.push(`USE CATALOG ${connection.catalog}`);
				this.logger.log(`Using catalog: ${connection.catalog}`);
			}

			if (connection.schema) {
				setupCommands.push(`USE SCHEMA ${connection.schema}`);
				this.logger.log(`Using schema: ${connection.schema}`);
			}

			if (setupCommands.length > 0) {
				finalQuery = setupCommands.join(';\n') + ';\n' + query;
			}

			// Execute query using Databricks SQL API
			const requestBody = {
				statement: finalQuery,
				warehouse_id: warehouseId,
				wait_timeout: '50s', // Maximum allowed is 50 seconds
				on_wait_timeout: 'CANCEL',
				format: 'JSON_ARRAY',
				disposition: 'INLINE'
			};

			this.logger.log(`Request body: ${JSON.stringify(requestBody, null, 2)}`);

			const response = await connection.httpClient.post('/api/2.0/sql/statements', requestBody);

			if (response.status !== 200) {
				this.logger.error(`HTTP Status: ${response.status}`);
				this.logger.error(`Response: ${JSON.stringify(response.data, null, 2)}`);
				throw new Error(`Query execution failed with status: ${response.status}`);
			}

			// Extract results from response
			const result = response.data;
			this.logger.log(`Query result status: ${result.status?.state}`);

			if (result.status?.state === 'SUCCEEDED') {
				const data = result.result?.data_array || [];
				this.logger.log(`Query returned ${data.length} rows`);
				return data;
			} else if (result.status?.state === 'FAILED') {
				const errorMsg = result.status.error?.message || 'Unknown error';
				this.logger.error(`Query failed: ${errorMsg}`);
				throw new Error(`Query failed: ${errorMsg}`);
			} else if (result.status?.state === 'PENDING' || result.status?.state === 'RUNNING') {
				// For long-running queries, we might need to poll for results
				this.logger.warn(`Query is still ${result.status.state}, but wait_timeout should handle this`);
				throw new Error(`Query timeout or still running: ${result.status?.state}`);
			} else {
				this.logger.error(`Unexpected query state: ${result.status?.state}`);
				throw new Error(`Query in unexpected state: ${result.status?.state}`);
			}
		} catch (error: any) {
			if (error.response) {
				this.logger.error(`HTTP Error ${error.response.status}: ${error.response.statusText}`);
				this.logger.error(`Response data: ${JSON.stringify(error.response.data, null, 2)}`);
			}
			this.logger.error(`Error executing query on Databricks: ${error.message}`);
			throw error;
		}
	}
}
