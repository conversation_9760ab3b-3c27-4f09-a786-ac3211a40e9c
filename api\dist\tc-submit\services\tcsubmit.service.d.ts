import { CurrentContext } from 'src/shared/types';
import { ModelRepository, ModelScenarioDataRepository, ModelScenarioRepository } from '../repositories';
import { ModelChartDto, ModelDto, ModelListingDto } from '../dtos';
import { TcConfigService } from 'src/tc-config/services';
import { KpiCalculatorService } from 'src/tc-config/services/kpi-calculator.service';
export declare class TcSubmitService {
    private readonly modelRepository;
    private readonly modelScenarioRepository;
    private readonly modelScenarioDataRepository;
    private readonly tcConfigService;
    private readonly kpiCalculatorService;
    constructor(modelRepository: ModelRepository, modelScenarioRepository: ModelScenarioRepository, modelScenarioDataRepository: ModelScenarioDataRepository, tcConfigService: TcConfigService, kpiCalculatorService: KpiCalculatorService);
    getModelById(modelId: number): Promise<any>;
    createOrGetModelWithAllScenario(currentContext: CurrentContext, modelDto: ModelDto): Promise<any>;
    getModelScenariosById(modelId: number): Promise<any>;
    saveModelChartKpi(modelDto: ModelChartDto, currentContext: CurrentContext): Promise<boolean>;
    saveModelTabularKpi(modelDto: ModelChartDto, currentContext: CurrentContext): Promise<boolean>;
    updateKpiValues(currentContext: CurrentContext, modelId: number, scenarioId: number, kpiValues: Record<string, any>): Promise<any>;
    createNewScenario(currentContext: CurrentContext, modelId: number): Promise<any>;
    publishModel(currentContext: CurrentContext, modelId: number): Promise<any>;
    refreshKpiValues(currentContext: CurrentContext, modelId: number, scenarioIds?: number[]): Promise<any>;
    calculateKpiValues(currentContext: CurrentContext, modelId: number, scenarioId: number, kpiCodes: string[]): Promise<any>;
    getLatestActiveModel(currentContext: CurrentContext): Promise<any>;
    getAllModels(currentContext: CurrentContext, modelDto: ModelListingDto): Promise<any>;
    refreshModel(currentContext: CurrentContext, modelId: number): Promise<any>;
    updateModelFetchMechanism(currentContext: CurrentContext, modelId: number, fetchMechanism: string, startDate?: Date, endDate?: Date): Promise<any>;
}
