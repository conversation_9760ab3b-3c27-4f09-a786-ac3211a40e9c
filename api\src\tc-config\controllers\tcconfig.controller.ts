import { Body, Controller, Get, Param, Post, Query, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiQuery, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import { TcConfigService } from '../services/tcconfig.service';
import { KpiConfigCodeDto, KpiConfigResponseDto, KpiDataResponseDto, TagKpiConfigDto } from '../dtos';
import { RequestContext } from 'src/shared/types';

@ApiTags('Terminal Capacity Configuration')
@ApiBearerAuth()
@UseGuards(AuthGuard('oauth-bearer'))
@Controller('tc-config')
export class TcConfigController {
    constructor(private readonly tcConfigService: TcConfigService) {}

    @Get('kpi-configs-by-tags')
    @ApiQuery({ name: 'tags', type: [String], isArray: true })
    async getKpiConfigsByTags(
        @Query('tags') tags: string[]
    ): Promise<KpiConfigResponseDto[]> {
        return this.tcConfigService.getKpiConfigsByTags(tags);
    }


    @Get('kpi-configs/:model_id')
    async getAllKpiConfigs(@Param('model_id') model_id: number): Promise<KpiConfigResponseDto[]> {
        return this.tcConfigService.getAllKpiConfigs(model_id);
    }

    @Get('get-kpis-by-tags')
    @UseGuards(AuthGuard('oauth-bearer'))
    async calculateKpisByTags(
        @Req() req: RequestContext,
        @Body() request: TagKpiConfigDto
    ): Promise<Record<string, any>> {
        let inputValues: Record<string, any> = {}
        return this.tcConfigService.getKpisByTags(
            request.entityId,
            request.tags,
            request.year,
            inputValues
        );
    }

    // @Get('get-kpis-by-system-codes')
    // async getKpiConfigByCode(
    //     @Req() req: RequestContext,
    //     @Body() request: KpiConfigCodeDto
    // ): Promise<Record<string, any>> {
    //     var input_val: Record<string, any> = {}
    //     return this.tcConfigService.getKpiConfigByCode(request.business_entity_id, request.system_codes, request.year, input_val);
    // }
}
