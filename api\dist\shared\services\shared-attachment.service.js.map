{"version": 3, "file": "shared-attachment.service.js", "sourceRoot": "", "sources": ["../../../src/shared/services/shared-attachment.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,mCAAkC;AAClC,4EAA+E;AAC/E,6EAAwE;AAExE,wCAAmD;AACnD,wDAA4D;AASrD,IAAM,uBAAuB,GAA7B,MAAM,uBAAuB;IACnC,YAA6B,mBAAwC;QAAxC,wBAAmB,GAAnB,mBAAmB,CAAqB;IAAI,CAAC;IAO7D,iBAAiB,CAC7B,WAAyB,EACzB,QAAgB,EAChB,UAAkB,EAClB,OAAe,EACf,MAAc;;YAEd,MAAM,gBAAgB,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,cAA0B,EAAE,EAAE;gBACvE,cAAc,CAAC,WAAW,GAAG,cAAc,CAAC,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5E,OAAO;oBACN,SAAS,EAAE,QAAQ;oBACnB,WAAW,EAAE,UAAU;oBACvB,mBAAmB,EAAE,IAAA,gCAAkB,EAAC,OAAO,EAAE,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC;oBACzE,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,QAAQ,CAAC;oBAC5D,UAAU,EAAE,MAAM,CAAC,WAAW,EAAE;oBAChC,eAAe,EAAE,cAAc,CAAC,eAAe;oBAC/C,uBAAuB,EAAE,cAAc,CAAC,uBAAuB;oBAC/D,uBAAuB,EAAE,cAAc,CAAC,IAAI;oBAC5C,WAAW,EAAE,cAAc,CAAC,WAAW;oBACvC,WAAW,EAAE,QAAQ,CAAC,QAAQ,EAAE;oBAChC,WAAW,EAAE,cAAc,CAAC,SAAS;oBACrC,WAAW,EAAE,cAAc,CAAC,IAAI;iBACR,CAAC;YAC3B,CAAC,CAAC,CAAC;YAEH,IAAI,WAAW,CAAC,MAAM,EAAE;gBACvB,OAAO,IAAI,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,CAAC;aACrE;QACF,CAAC;KAAA;IAOY,oBAAoB,CAAC,WAAyB;;YAC1D,WAAW,CAAC,OAAO,CAAC,CAAO,cAA0B,EAAE,EAAE;gBACxD,MAAM,WAAW,GAAG;oBACnB,EAAE,EAAE,IAAA,iBAAQ,EAAC,cAAc,CAAC,EAAE,CAAC;oBAC/B,WAAW,EAAE,cAAc,CAAC,WAAW;iBACZ,CAAC;gBAC7B,MAAM,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;YAC/D,CAAC,CAAA,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC;QACb,CAAC;KAAA;IAOY,oBAAoB,CAAC,WAAoC;;YACrE,WAAW,CAAC,OAAO,CAAC,CAAO,cAAqC,EAAE,EAAE;gBACnE,MAAM,IAAI,CAAC,mBAAmB,CAAC,wBAAwB,CACtD,cAAc,CAAC,OAAO,CACtB,CAAC;YACH,CAAC,CAAA,CAAC,CAAC;YACH,OAAO,IAAI,CAAC;QACb,CAAC;KAAA;IAYY,2BAA2B,CACvC,mBAAiC,EACjC,QAAgB,EAChB,UAAkC,EAClC,YAAoB,EACpB,MAAc;;YAEd,IAAI,oBAAoB,GAAG,EAAE,CAAC;YAC9B,MAAM,oBAAoB,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC,aAAyB,EAAE,EAAE;gBACrF,OAAO,aAAa,CAAC,KAAK,CAAC;YAC5B,CAAC,CAAC,CAAC;YACH,MAAM,qBAAqB,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC,aAAyB,EAAE,EAAE;gBACtF,OAAO,CAAC,aAAa,CAAC,KAAK,IAAI,aAAa,CAAC,QAAQ,CAAC;YACvD,CAAC,CAAC,CAAC;YAEH,IAAI,oBAAoB,CAAC,MAAM,EAAE;gBAChC,MAAM,IAAI,CAAC,iBAAiB,CAC3B,oBAAoB,EACpB,QAAQ,EACR,UAAU,EACV,YAAY,EACZ,MAAM,CACN,CAAC;aACF;YAED,IAAI,qBAAqB,CAAC,MAAM,EAAE;gBACjC,MAAM,IAAI,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,CAAC;aACvD;YAED,IAAI,oBAAoB,CAAC,MAAM,EAAE;gBAChC,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;gBACpG,oBAAoB,GAAG,IAAA,+BAAqB,EAAC,+CAAqB,EAAE,oBAAoB,CAAC,CAAC;gBAC1F,oBAAoB,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;oBACzC,UAAU,CAAC,KAAK,GAAG,KAAK,CAAC;oBACzB,UAAU,CAAC,QAAQ,GAAG,KAAK,CAAC;gBAC7B,CAAC,CAAC,CAAC;aACH;YAED,OAAO,oBAAoB,CAAC;QAC7B,CAAC;KAAA;IAKY,iCAAiC,CAAC,mBAA4C;;YAC1F,MAAM,uBAAuB,GAAG,mBAAmB,CAAC,MAAM,CACzD,CAAC,aAAoC,EAAE,EAAE;gBACxC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC;YAC7B,CAAC,CACD,CAAC;YACF,IAAI,uBAAuB,CAAC,MAAM,EAAE;gBACnC,MAAM,IAAI,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,CAAC;aACzD;YACD,OAAO,IAAI,CAAC;QACb,CAAC;KAAA;CACD,CAAA;AAtIY,uBAAuB;IADnC,IAAA,mBAAU,GAAE;qCAEsC,2CAAmB;GADzD,uBAAuB,CAsInC;AAtIY,0DAAuB"}