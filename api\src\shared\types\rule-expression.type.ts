export type Operator = "equal" | "notEqual" | "in" | "notIn" | "lessThan" | "lessThanInclusive" | "greaterThan" | "greaterThanInclusive";

export interface RuleExpression {
    fact: string;
    value: any;
    operator: Operator;
    path?: string;
}

export interface RuleExpressionCondition {
    all?: RuleExpression[];
    any?: RuleExpressionCondition[];
    fact?: string;
    value?: any;
    path?: string;
    operator?: Operator;
}
