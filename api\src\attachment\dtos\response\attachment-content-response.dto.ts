import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class BufferData {
	type: string;
	data: Buffer;
}

export class AttachmentContentResponseDto {
	@ApiProperty({ type: Number })
	@Expose()
	public id: number;

	@ApiProperty({ type: String })
	@Expose()
	attachment_name?: string;

	@ApiProperty({ type: Number })
	@Expose()
	entity_id: number;

	@ApiProperty({ type: String })
	@Expose()
	meta_data_1?: string;

	@ApiProperty({ type: String })
	@Expose()
	meta_data_2?: string;

	@ApiProperty({ type: String })
	@Expose()
	meta_data_3?: string;

	@ApiProperty({ type: String })
	@Expose()
	description?: string;

	@ApiProperty({ type: Object })
	@Expose()
	additional_info?: object;

	@ApiProperty({ type: String })
	@Expose()
	file_id?: string;

	@ApiProperty({ type: BufferData })
	@Expose()
	contents: BufferData;

	@ApiProperty({ type: String })
	@Expose()
	attachment_content_type: string;

	@ApiProperty({ type: String })
	@Expose()
	created_by: string;

	@ApiProperty({ type: Date })
	@Expose()
	created_on: Date;
}
