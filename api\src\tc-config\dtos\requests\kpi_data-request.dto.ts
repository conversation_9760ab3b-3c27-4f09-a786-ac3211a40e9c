import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsOptional } from 'class-validator';

export class KpiDataRequestDto {
	@ApiProperty({ type: String })
	@IsNotEmpty()
	kpi_code: string;

	@ApiProperty({ type: Number })
	@IsOptional()
	public kpi_day: number | null;

	@ApiProperty({ type: Number })
    @IsOptional()
	public kpi_month: number;

	@ApiProperty({ type: Number })
    @IsOptional()
	public kpi_year: number;

	@ApiProperty({ type: Number })
    @IsOptional()
	public kpi_value: number;
}
