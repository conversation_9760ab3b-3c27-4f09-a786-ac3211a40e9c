"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchedulerModule = void 0;
const common_1 = require("@nestjs/common");
const services_1 = require("./services");
const controllers_1 = require("./controllers");
const repositories_1 = require("../queue/repositories");
const repositories_2 = require("./repositories");
const clients_1 = require("../shared/clients");
const helpers_1 = require("../shared/helpers");
const services_2 = require("../shared/services");
const repositories_3 = require("../tc-config/repositories");
const config_module_1 = require("../config/config.module");
let SchedulerModule = class SchedulerModule {
};
SchedulerModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_module_1.ConfigModule,
        ],
        controllers: [controllers_1.SchedulerController],
        providers: [
            services_1.SchedulerService,
            repositories_2.SyncLogRepository,
            repositories_1.QueueLogRepository,
            clients_1.AdminApiClient,
            clients_1.MSGraphApiClient,
            clients_1.NotificationApiClient,
            services_2.SharedNotificationService,
            repositories_3.KpiDataRepository,
            helpers_1.SequlizeOperator,
            repositories_3.BuSetupRepository
        ],
        exports: [
            services_1.SchedulerService,
        ]
    })
], SchedulerModule);
exports.SchedulerModule = SchedulerModule;
//# sourceMappingURL=scheduler.module.js.map