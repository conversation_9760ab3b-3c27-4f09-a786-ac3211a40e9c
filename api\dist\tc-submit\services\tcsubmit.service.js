"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __asyncValues = (this && this.__asyncValues) || function (o) {
    if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
    var m = o[Symbol.asyncIterator], i;
    return m ? m.call(o) : (o = typeof __values === "function" ? __values(o) : o[Symbol.iterator](), i = {}, verb("next"), verb("throw"), verb("return"), i[Symbol.asyncIterator] = function () { return this; }, i);
    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }
    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TcSubmitService = void 0;
const common_1 = require("@nestjs/common");
const repositories_1 = require("../repositories");
const models_1 = require("../models");
const services_1 = require("../../tc-config/services");
const kpi_calculator_service_1 = require("../../tc-config/services/kpi-calculator.service");
const constants_1 = require("../../shared/constants");
const sequelize_1 = require("sequelize");
let TcSubmitService = class TcSubmitService {
    constructor(modelRepository, modelScenarioRepository, modelScenarioDataRepository, tcConfigService, kpiCalculatorService) {
        this.modelRepository = modelRepository;
        this.modelScenarioRepository = modelScenarioRepository;
        this.modelScenarioDataRepository = modelScenarioDataRepository;
        this.tcConfigService = tcConfigService;
        this.kpiCalculatorService = kpiCalculatorService;
    }
    getModelById(modelId) {
        return __awaiter(this, void 0, void 0, function* () {
            const model = yield this.modelRepository.findById(modelId);
            return model;
        });
    }
    createOrGetModelWithAllScenario(currentContext, modelDto) {
        return __awaiter(this, void 0, void 0, function* () {
            const existingModel = yield this.modelRepository.findOne({
                where: {
                    entity_id: modelDto.business_entity_id,
                    createdBy: currentContext.user.username.toLowerCase(),
                    workflow_status: { [sequelize_1.Op.ne]: constants_1.WORKFLOW_STATUS.PUBLISHED },
                },
            });
            let newModel = new models_1.Model();
            if (existingModel) {
                const scenarios = yield this.modelScenarioRepository.findAll({
                    where: { model_id: existingModel.id },
                    order: [['scenario_number', 'ASC']],
                });
                if (scenarios && scenarios.length > 0) {
                    return scenarios.map(scenario => ({
                        model_id: scenario.model_id,
                        scenario_id: scenario.id,
                        scenario_number: scenario.scenario_number,
                        is_current_state: scenario.is_current_state,
                        kpi_data: scenario.kpi_data,
                    }));
                }
                else {
                    return {
                        message: 'Model exists but no scenarios found',
                        model_id: existingModel.id,
                    };
                }
            }
            else {
                var kpi_data = yield this.tcConfigService.getAllKpisAndValues(modelDto.business_entity_id);
                newModel.entity_id = modelDto.business_entity_id;
                newModel.entity_code = modelDto.business_entity_code;
                newModel.title = modelDto.title;
                newModel.summary_tabular_kpis = null;
                newModel.summary_chart_kpis = null;
                newModel.user_status = constants_1.USER_STATUS.DRAFT;
                newModel.workflow_status = constants_1.WORKFLOW_STATUS.DRAFT;
                newModel.override_fetch_mechanism_type = 'default';
                newModel = yield this.modelRepository.save(newModel, currentContext);
            }
            let newScenario = new models_1.ModelScenario();
            newScenario.model_id = newModel.id;
            newScenario.is_current_state = true;
            newScenario.scenario_number = 0;
            newScenario.kpi_data = kpi_data;
            newScenario = yield this.modelScenarioRepository.save(newScenario, currentContext);
            return [
                {
                    model_id: newScenario.model_id,
                    scenario_id: newScenario.id,
                    scenario_number: newScenario.scenario_number,
                    is_current_state: true,
                    kpi_data: newScenario.kpi_data,
                },
            ];
        });
    }
    getModelScenariosById(modelId) {
        return __awaiter(this, void 0, void 0, function* () {
            const model = yield this.modelRepository.findById(modelId);
            if (model) {
                const scenarios = yield this.modelScenarioRepository.findAll({
                    where: { model_id: model.id },
                    order: [['scenario_number', 'ASC']],
                });
                if (scenarios && scenarios.length > 0) {
                    return scenarios.map(scenario => ({
                        model_id: scenario.model_id,
                        scenario_id: scenario.id,
                        scenario_number: scenario.scenario_number,
                        is_current_state: scenario.is_current_state,
                        kpi_data: scenario.kpi_data,
                    }));
                }
                else {
                    return {
                        message: 'Model exists but no scenarios found',
                        model_id: model.id,
                    };
                }
            }
        });
    }
    saveModelChartKpi(modelDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const model = yield this.modelRepository.findById(modelDto.model_id);
            if (!model) {
                throw new Error(`Model with ID ${modelDto.model_id} not found`);
            }
            model.summary_chart_kpis = modelDto.kpi_codes;
            yield this.modelRepository.updateModel(model.id, model, currentContext);
            return true;
        });
    }
    saveModelTabularKpi(modelDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const model = yield this.modelRepository.findOne({
                where: { id: modelDto.model_id },
            });
            if (!model) {
                throw new Error(`Model with ID ${modelDto.model_id} not found`);
            }
            model.summary_tabular_kpis = modelDto.kpi_codes;
            yield this.modelRepository.updateModel(model.id, model, currentContext);
            return true;
        });
    }
    updateKpiValues(currentContext, modelId, scenarioId, kpiValues) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const model = yield this.modelRepository.findOne({
                    where: { id: modelId },
                });
                if (!model) {
                    throw new Error(`Model with ID ${modelId} not found`);
                }
                const scenario = yield this.modelScenarioRepository.getById(scenarioId);
                if (!scenario) {
                    throw new Error(`Scenario with ID ${scenarioId} for model ${modelId} not found`);
                }
                const businessEntityId = model.entity_id;
                let currentKpiData = scenario.kpi_data || {};
                if (typeof currentKpiData === 'string') {
                    currentKpiData = JSON.parse(currentKpiData);
                }
                const mergedKpiValues = Object.assign(Object.assign({}, currentKpiData), kpiValues);
                const recalculatedValues = yield this.tcConfigService.getAllKpisAndValues(businessEntityId, mergedKpiValues);
                scenario.kpi_data = recalculatedValues;
                yield this.modelScenarioRepository.updateScenario(scenario.id, scenario, currentContext);
                return {
                    model_id: modelId,
                    scenario_id: scenarioId,
                    kpi_values: recalculatedValues,
                };
            }
            catch (error) {
                console.error('Error updating KPI values:', error);
                throw error;
            }
        });
    }
    createNewScenario(currentContext, modelId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const model = yield this.modelRepository.findOne({
                    where: { id: modelId },
                });
                if (!model) {
                    throw new Error(`Model with ID ${modelId} not found`);
                }
                const currentStateScenario = yield this.modelScenarioRepository.findOne({
                    where: {
                        model_id: modelId,
                        is_current_state: true,
                    },
                });
                if (!currentStateScenario) {
                    throw new Error(`Current state scenario for model ${modelId} not found`);
                }
                const scenarios = yield this.modelScenarioRepository.findAll({
                    where: { model_id: modelId },
                    order: [['scenario_number', 'DESC']],
                    limit: 1,
                });
                let highestScenarioNumber = 0;
                if (scenarios && scenarios.length > 0) {
                    highestScenarioNumber = scenarios[0].scenario_number;
                }
                let newScenario = new models_1.ModelScenario();
                newScenario.model_id = modelId;
                newScenario.is_current_state = false;
                newScenario.scenario_number = highestScenarioNumber + 1;
                newScenario.kpi_data = currentStateScenario.kpi_data;
                newScenario = yield this.modelScenarioRepository.save(newScenario, currentContext);
                return {
                    model_id: newScenario.model_id,
                    scenario_id: newScenario.id,
                    scenario_number: newScenario.scenario_number,
                    is_current_state: newScenario.is_current_state,
                    kpi_data: newScenario.kpi_data,
                };
            }
            catch (error) {
                console.error('Error creating new scenario:', error);
                throw error;
            }
        });
    }
    publishModel(currentContext, modelId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const model = yield this.modelRepository.findOne({
                    where: { id: modelId },
                });
                if (!model) {
                    throw new Error(`Model with ID ${modelId} not found`);
                }
                if (model.workflow_status !== constants_1.WORKFLOW_STATUS.DRAFT) {
                    throw new Error(`Model with ID ${modelId} should be on draft status to publish the model`);
                }
                const scenario = yield this.modelScenarioRepository.findOne({
                    where: { is_current_state: true, model_id: modelId },
                });
                if (!scenario) {
                    throw new Error(`Current state Scenario for model ${modelId} not found`);
                }
                let kpiData = scenario.kpi_data;
                if (typeof kpiData === 'string') {
                    kpiData = JSON.parse(kpiData);
                }
                const invalidKpis = [];
                for (const [kpiCode, value] of Object.entries(kpiData)) {
                    if (value === -1) {
                        invalidKpis.push(kpiCode);
                    }
                }
                if (invalidKpis.length > 0) {
                    return {
                        success: false,
                        message: 'Cannot publish model with invalid KPI values',
                        invalidKpis: invalidKpis,
                    };
                }
                model.user_status = constants_1.USER_STATUS.PUBLISHED;
                model.workflow_status = constants_1.WORKFLOW_STATUS.PUBLISHED;
                yield this.modelRepository.updateModel(model.id, model, currentContext);
                return {
                    success: true,
                    message: 'Model published successfully',
                    model_id: modelId,
                    workflow_status: constants_1.WORKFLOW_STATUS.PUBLISHED,
                };
            }
            catch (error) {
                console.error('Error publishing model:', error);
                throw error;
            }
        });
    }
    refreshKpiValues(currentContext, modelId, scenarioIds) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const model = yield this.modelRepository.findOne({
                    where: { id: modelId },
                });
                if (!model) {
                    return {
                        success: false,
                        message: `Model with ID ${modelId} not found`,
                    };
                }
                let scenarios = [];
                if (!scenarioIds || scenarioIds.length === 0) {
                    scenarios = yield this.modelScenarioRepository.findAll({
                        where: { model_id: modelId },
                        order: [['scenario_number', 'ASC']],
                    });
                    if (!scenarios || scenarios.length === 0) {
                        return {
                            success: false,
                            message: `No scenarios found for model ${modelId}`,
                        };
                    }
                }
                else {
                    scenarios = yield this.modelScenarioRepository.findAll({
                        where: {
                            id: scenarioIds,
                            model_id: modelId,
                        },
                    });
                }
                const results = [];
                const businessEntityId = model.entity_id;
                for (const scenario of scenarios) {
                    try {
                        let currentKpiData = scenario.kpi_data || {};
                        if (typeof currentKpiData === 'string') {
                            currentKpiData = JSON.parse(currentKpiData);
                        }
                        const recalculatedValues = yield this.tcConfigService.getAllKpisAndValues(businessEntityId, currentKpiData);
                        scenario.kpi_data = recalculatedValues;
                        yield this.modelScenarioRepository.updateScenario(scenario.id, scenario, currentContext);
                        results.push({
                            scenario_id: scenario.id,
                            scenario_number: scenario.scenario_number,
                            is_current_state: scenario.is_current_state,
                            success: true,
                            model_id: modelId,
                            kpi_values: recalculatedValues,
                        });
                    }
                    catch (error) {
                        results.push({
                            scenario_id: scenario.id,
                            scenario_number: scenario.scenario_number,
                            success: false,
                            message: `Error refreshing scenario ${scenario.id}: ${error.message}`,
                        });
                    }
                }
                return {
                    model_id: modelId,
                    results: results,
                };
            }
            catch (error) {
                console.error('Error refreshing KPI values:', error);
                throw error;
            }
        });
    }
    calculateKpiValues(currentContext, modelId, scenarioId, kpiCodes) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const model = yield this.modelRepository.findById(modelId);
                if (!model) {
                    throw new Error(`Model with ID ${modelId} not found`);
                }
                const scenario = yield this.modelScenarioRepository.getById(scenarioId);
                if (!scenario) {
                    throw new Error(`Scenario with ID ${scenarioId} for model ${modelId} not found`);
                }
                const businessEntityId = model.entity_id;
                let currentKpiData = scenario.kpi_data || {};
                if (typeof currentKpiData === 'string') {
                    currentKpiData = JSON.parse(currentKpiData);
                }
                const kpiData = yield this.kpiCalculatorService.getKpiDataBySystemCodes(businessEntityId, kpiCodes, currentKpiData, model.override_fetch_mechanism_type, model.pull_from_date, model.pull_to_date);
                const result = {
                    model_id: modelId,
                    scenario_id: scenarioId,
                    kpi_values: {},
                };
                for (const kpiItem of kpiData) {
                    result.kpi_values[kpiItem.kpi_code] = kpiItem.current_value;
                }
                for (const kpiItem of kpiData) {
                    if (kpiItem.dependency_values) {
                        Object.entries(kpiItem.dependency_values).forEach(([key, value]) => {
                            if (!result.kpi_values[key]) {
                                result.kpi_values[key] = value;
                            }
                        });
                    }
                }
                return result;
            }
            catch (error) {
                throw new Error(`Failed to calculate KPI values: ${error.message}`);
            }
        });
    }
    getLatestActiveModel(currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const model = yield this.modelRepository.findOne({
                where: {
                    createdBy: currentContext.user.username.toLowerCase(),
                    workflow_status: { [sequelize_1.Op.ne]: constants_1.WORKFLOW_STATUS.PUBLISHED },
                },
                order: [['id', 'DSC']],
            });
            if (!model) {
                return [];
            }
            const scenarios = yield this.modelScenarioRepository.findAll({
                where: { model_id: model.id },
                order: [['scenario_number', 'ASC']],
            });
            if (scenarios && scenarios.length > 0) {
                return scenarios.map(scenario => ({
                    model_id: scenario.model_id,
                    scenario_id: scenario.id,
                    scenario_number: scenario.scenario_number,
                    is_current_state: scenario.is_current_state,
                    kpi_data: scenario.kpi_data,
                }));
            }
            else {
                return [];
            }
        });
    }
    getAllModels(currentContext, modelDto) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const allModels = yield this.modelRepository.findAll({
                    where: {
                        entity_code: modelDto.business_entity_code,
                    },
                    order: [['id', 'DESC']],
                });
                if (!allModels || allModels.length === 0) {
                    return [];
                }
                const result = [];
                for (const model of allModels) {
                    result.push({
                        model_id: model.id,
                        entity_id: model.entity_id,
                        entity_code: model.entity_code,
                        title: model.title,
                        workflow_status: model.workflow_status,
                        created_by: model.createdBy,
                        created_on: model.createdOn,
                    });
                }
                return result;
            }
            catch (error) {
                console.error('Error getting all models:', error);
                throw error;
            }
        });
    }
    refreshModel(currentContext, modelId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const model = yield this.modelRepository.findOne({
                    where: { id: modelId },
                });
                if (!model) {
                    throw new Error(`Model with ID ${modelId} not found`);
                }
                const scenarios = yield this.modelScenarioRepository.findAll({
                    where: { model_id: modelId },
                    order: [['scenario_number', 'ASC']],
                });
                if (!scenarios || scenarios.length === 0) {
                    throw new Error(`No scenarios found for model ${modelId}`);
                }
                const businessEntityId = model.entity_id;
                const results = [];
                for (const scenario of scenarios) {
                    try {
                        let currentKpiData = scenario.kpi_data || {};
                        if (typeof currentKpiData === 'string') {
                            currentKpiData = JSON.parse(currentKpiData);
                        }
                        const refreshedKpiData = yield this.tcConfigService.getAllKpisAndValues(businessEntityId, currentKpiData);
                        scenario.kpi_data = refreshedKpiData;
                        yield this.modelScenarioRepository.updateScenario(scenario.id, scenario, currentContext);
                        results.push({
                            scenario_id: scenario.id,
                            scenario_number: scenario.scenario_number,
                            is_current_state: scenario.is_current_state,
                            success: true,
                            message: 'Scenario refreshed successfully',
                        });
                    }
                    catch (error) {
                        results.push({
                            scenario_id: scenario.id,
                            scenario_number: scenario.scenario_number,
                            success: false,
                            message: `Error refreshing scenario ${scenario.id}: ${error.message}`,
                        });
                    }
                }
                return {
                    success: true,
                    message: 'Model refreshed successfully',
                    model_id: modelId,
                    results: results,
                };
            }
            catch (error) {
                console.error('Error refreshing model:', error);
                throw error;
            }
        });
    }
    updateModelFetchMechanism(currentContext, modelId, fetchMechanism, startDate, endDate) {
        var _a, e_1, _b, _c;
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const model = yield this.modelRepository.findOne({
                    where: { id: modelId },
                });
                if (!model) {
                    throw new Error(`Model with ID ${modelId} not found`);
                }
                const scenarios = yield this.modelScenarioRepository.findAll({
                    where: { model_id: modelId },
                    order: [['scenario_number', 'ASC']],
                });
                if (!scenarios || scenarios.length === 0) {
                    throw new Error(`No scenarios found for model ${modelId}`);
                }
                const businessEntityId = model.entity_id;
                const results = [];
                try {
                    for (var _d = true, scenarios_1 = __asyncValues(scenarios), scenarios_1_1; scenarios_1_1 = yield scenarios_1.next(), _a = scenarios_1_1.done, !_a;) {
                        _c = scenarios_1_1.value;
                        _d = false;
                        try {
                            const scenario = _c;
                            try {
                                let currentKpiData = scenario.kpi_data || {};
                                if (typeof currentKpiData === 'string') {
                                    currentKpiData = JSON.parse(currentKpiData);
                                }
                                const refreshedKpiData = yield this.tcConfigService.getAllKpisAndValues(businessEntityId, currentKpiData, fetchMechanism, startDate, endDate);
                                scenario.kpi_data = refreshedKpiData;
                                yield this.modelScenarioRepository.updateScenario(scenario.id, scenario, currentContext);
                                results.push({
                                    scenario_id: scenario.id,
                                    scenario_number: scenario.scenario_number,
                                    success: true,
                                    message: 'Scenario updated successfully',
                                });
                            }
                            catch (error) {
                                results.push({
                                    scenario_id: scenario.id,
                                    scenario_number: scenario.scenario_number,
                                    success: false,
                                    message: `Error updating scenario: ${error.message}`,
                                });
                            }
                        }
                        finally {
                            _d = true;
                        }
                    }
                }
                catch (e_1_1) { e_1 = { error: e_1_1 }; }
                finally {
                    try {
                        if (!_d && !_a && (_b = scenarios_1.return)) yield _b.call(scenarios_1);
                    }
                    finally { if (e_1) throw e_1.error; }
                }
                model.override_fetch_mechanism_type = fetchMechanism;
                if (fetchMechanism === 'custom') {
                    if (!startDate || !endDate) {
                        throw new Error('Start date and end date are required for custom fetch mechanism');
                    }
                    model.pull_from_date = startDate;
                    model.pull_to_date = endDate;
                }
                else {
                    model.pull_from_date = null;
                    model.pull_to_date = null;
                }
                yield this.modelRepository.updateModel(model.id, model, currentContext);
                return {
                    success: true,
                    message: 'Model fetch mechanism updated successfully',
                    model_id: modelId,
                    fetch_mechanism: fetchMechanism,
                    results: results,
                };
            }
            catch (error) {
                console.error('Error updating model fetch mechanism:', error);
                throw error;
            }
        });
    }
};
TcSubmitService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [repositories_1.ModelRepository,
        repositories_1.ModelScenarioRepository,
        repositories_1.ModelScenarioDataRepository,
        services_1.TcConfigService,
        kpi_calculator_service_1.KpiCalculatorService])
], TcSubmitService);
exports.TcSubmitService = TcSubmitService;
//# sourceMappingURL=tcsubmit.service.js.map