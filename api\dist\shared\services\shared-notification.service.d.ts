import { AdminApiClient, NotificationApiClient } from '../clients';
export declare class SharedNotificationService {
    private readonly adminApiClient;
    private readonly notificationApiClient;
    constructor(adminApiClient: AdminApiClient, notificationApiClient: NotificationApiClient);
    sendNotification(entityId: number, entityType: string, recipients: {
        to: string[];
        cc?: string[];
        bcc?: string[];
    }, templateName: string, placeholderValues: {
        [key: string]: string;
    }, isApprovalEmail?: boolean, approvalTaskId?: number): Promise<void>;
}
