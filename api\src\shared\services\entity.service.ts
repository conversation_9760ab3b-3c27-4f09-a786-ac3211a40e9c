import { Injectable } from '@nestjs/common';
import { AdminApiClient } from '../clients';

@Injectable()
export class EntityService {
	constructor(private readonly adminApiClient: AdminApiClient) { }

	/**
	 *
	 * @param entityId
	 * @param items
	 * @returns
	 */
	public async filterItemsByEntityInclusionAndExclusion<
		T extends { includedEntityIds: number[]; excludedEntityIds: number[] },
	>(entityId: number, items: T[]): Promise<T[]> {
		const entityParents = await this.adminApiClient.getParentIdsOfEntity(entityId);

		return items.filter(item => {
			const { includedEntityIds, excludedEntityIds } = item;
			//Return false if one of the parent of the given entity persent in the excluded list.
			const isExcluded = excludedEntityIds.some(id => entityParents.includes(id));
			if (isExcluded) {
				return false;
			}
			return includedEntityIds.some(id => entityParents.includes(id));
		});
	}
}
