import { Injectable } from '@nestjs/common';
import { literal, Op } from 'sequelize';
import { QUEUE_LOG_ACTION } from 'src/shared/enums';
import { convertRuleExpressionToSqlQuery } from 'src/shared/helpers';
import { BaseRepository } from 'src/shared/repositories';
import { CurrentContext } from 'src/shared/types';
import { QueueLog } from '../models';

@Injectable()
export class QueueLogRepository extends BaseRepository<QueueLog> {
    constructor() {
        super(QueueLog);
    }

    public createQueueLogEntry(
        payload,
        currentContext: CurrentContext,
    ): Promise<QueueLog | null> {
        const entity = new QueueLog(payload);
        return this.save(entity, currentContext);
    }

    public getUnprocessedQueueLogs(): Promise<QueueLog[]> {
        return this.findAll({ where: { processed: false } })
    }

    public getQueueLogsByLogConditionAfterLastRunAt(
        entityIds: number[],
        actions: QUEUE_LOG_ACTION[],
        finalApproval: boolean,
        rule,
        lastRunAt
    ): Promise<QueueLog[]> {
        const condition: any = [
            { finalApproval: finalApproval }
        ];

        if(rule) {
            condition.push(literal(convertRuleExpressionToSqlQuery(rule, 'data')));
        }

        if (actions?.length) {
            condition.push({ action: { [Op.in]: actions } });
        }

        if (entityIds?.length) {
            condition.push({ entityId: { [Op.in]: entityIds } });
        }

        if (lastRunAt) {
            condition.push({ createdAt: { [Op.gt]: lastRunAt } });
        }

        return this.findAll({
            where: {
                [Op.and]: condition
            }
        });
    }

    public async markQueueLogAsProcessed(id: number, currentContext: CurrentContext): Promise<void> {
        await this.update({ processed: true }, currentContext, { where: { id } })
    }
}