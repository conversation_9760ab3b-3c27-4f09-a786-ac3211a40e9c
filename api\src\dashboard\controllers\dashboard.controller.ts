import { Controller, Get, Query, Req, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Permissions } from 'src/core/decorators';
import { PermissionsGuard } from 'src/core/guards';
import { PERMISSIONS } from 'src/shared/enums';
import { RequestContext } from 'src/shared/types';
import { RequestTypeWiseAfeResponseDto } from '../dtos/response/request-type-wise-afe-response.dto';
import { DashboardService } from '../services';

@ApiTags('Dashboard APIs')
@ApiBearerAuth()
@UseGuards(AuthGuard('oauth-bearer'), PermissionsGuard)
@Controller('dashboard')
export class DashboardController {

    constructor(
		private dashboardService: DashboardService,
	) { }

	/**
	 * Return request type wise count - Capex, opex, etc
	 */
	 @Permissions(PERMISSIONS.TCT_SUBMIT)
	 @ApiQuery({
		 name: 'year',
		 type: Number,
		 description: 'Year to fetch the detail.',
		 required: true,
	 })
	 @ApiQuery({
		name: 'entityId',
		type: Number,
		description: 'Business entity id to fetch the detail.',
		required: false,
	})
	 @ApiResponse({
		 status: 200,
		 description: 'Return monthly request type wise count - Capex, opex, etc',
		 type: [RequestTypeWiseAfeResponseDto],
	 })
	 @Get('/request-type-wise-count')
	 public getRequestTypeWiseCount(
		 @Req() request: RequestContext,
		 @Query('year') year: number,
		 @Query('entityId') entityId: number | null = null,
	 ) {
		 return this.dashboardService.getRequestWiseMonthlyCount(request.currentContext, year, entityId);
	 }

}
