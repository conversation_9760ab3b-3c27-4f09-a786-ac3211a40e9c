import { Injectable } from '@nestjs/common';
import { AttachmentApiClient } from 'src/shared/clients/attachment-api.client';
import { ATTACHMENT_ENTITY_TYPE, HttpStatus } from 'src/shared/enums';
import { HttpException } from 'src/shared/exceptions';
import { multiObjectToInstance, singleObjectToInstance } from 'src/shared/helpers';
import { AttachmentContentResponseDto } from '../dtos';

@Injectable()
export class AttachmentService {
	constructor(
		private readonly attachmentApiClient: AttachmentApiClient,
	) { }

	/**
	 * Get content by id
	 * @returns  GetAttachmentContentResponseDto
	 */
	public async getContentByFileId(fileId: string): Promise<AttachmentContentResponseDto> {
		const fileContent = await this.attachmentApiClient.getContentByFileId(fileId);
		if (!fileContent) {
			throw new HttpException(`Content not available for file id ${fileId}`, HttpStatus.NOT_FOUND);
		}
		return singleObjectToInstance(AttachmentContentResponseDto, fileContent);
	}

	/**
	 * Get all the attachments of submitted afe proposal.
	 * @param afeProposalId
	 * @returns
	 */
	public async getProposalAttachmentMetaData(
		afeProposalId: number,
	): Promise<AttachmentContentResponseDto[]> {
		const response = await this.attachmentApiClient.getAllAttachments(
			afeProposalId,
			ATTACHMENT_ENTITY_TYPE.AFE_SUBMIT,
		);
		return multiObjectToInstance(AttachmentContentResponseDto, response);
	}
}
