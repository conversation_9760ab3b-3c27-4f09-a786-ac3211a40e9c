import { Inject, Injectable } from '@nestjs/common';
import * as msal from '@azure/msal-node';
import { MS_GRAPH_API } from '../constants';
import { ConfigService } from 'src/config/config.service';
import { HttpService } from '../services';
import { ADUserDetails, AzureADConfig, ResponseHeaders } from '../types';

@Injectable()
export class MSGraphApiClient {
	private tokenRequest: { scopes: string[] };
	private azureADConfig: AzureADConfig;

	constructor(
		@Inject(MS_GRAPH_API.MS_GRAPH_API_PROVIDER)
		private readonly msGraphApiProvider: msal.ConfidentialClientApplication,
		@Inject(MS_GRAPH_API.MS_GRAPH_HTTP_SERVICE_PROVIDER)
		private readonly msGraphApiHttpService: HttpService,
		@Inject(ConfigService) private readonly configService: ConfigService,
	) {
		this.azureADConfig = this.configService.getAppConfig().azureAD;
		const { graphApiUrl } = this.azureADConfig;
		this.tokenRequest = {
			scopes: [`${graphApiUrl}/.default`]
		};
	}

	private async getToken(): Promise<msal.AuthenticationResult> {
		return await this.msGraphApiProvider.acquireTokenByClientCredential(this.tokenRequest);
	}

	private async getHeaders(): Promise<ResponseHeaders> {
		const { accessToken } = await this.getToken();
		return { Authorization: `Bearer ${accessToken}` };
	}

	public async getUserDetails(userId: string): Promise<ADUserDetails> {
		const headers = await this.getHeaders();
		const { data } = await this.msGraphApiHttpService.withHeaders(headers).get(`/users/?$select=id,givenName,jobTitle,surname,mail,userPrincipalName,userType,displayName&$filter=mail eq '${userId}' OR userPrincipalName eq '${userId}'`);
		return data?.value[data.value.length - 1] || null;
	}

	public async getUserDetailsByEmail(email: string): Promise<ADUserDetails> {
		const headers = await this.getHeaders();
		const { data } = await this.msGraphApiHttpService.withHeaders(headers).get(`/users/?$select=givenName,jobTitle,surname,mail,userPrincipalName,userType,displayName&$filter=mail eq '${email}'`);
		return data?.value[data.value.length - 1] || null;
	}

	public async getUsersDetails(userIds: string[]): Promise<ADUserDetails[]> {
		const headers = await this.getHeaders();
		const ids = userIds.map(id => `'${id}'`).join(',');
		const results: ADUserDetails[] = [];
		let nextLink: string | null = `/users/?$select=givenName,jobTitle,surname,mail,userPrincipalName,userType&$filter=mail in (${ids}) OR userPrincipalName in (${ids})`;
		while (nextLink) {
			const response = await this.msGraphApiHttpService.withHeaders(headers).get(`/users/?$select=givenName,jobTitle,surname,mail,userPrincipalName,userType&$filter=mail in (${ids}) OR userPrincipalName in (${ids})`);
			const { data } = response;
			results.push(...data.value);
			nextLink = data["@odata.nextLink"]?.replace(`${this.azureADConfig.graphApiUrl}/${this.azureADConfig.graphApiVersion}`, '') || null;
		}
		return results;
	}
}
