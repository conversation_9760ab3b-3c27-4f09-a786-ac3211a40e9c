import { Column, DataType, Table } from 'sequelize-typescript';
import { BaseModel } from 'src/shared/models';

@Table({ tableName: 'kpi_config' })
export class KpiConfig extends BaseModel<KpiConfig> {
    @Column({ type: DataType.STRING(255), allowNull: false })
    public system_code: string;

    @Column({ type: DataType.STRING(255), allowNull: false })
    public title: string;

    @Column({ type: DataType.STRING, allowNull: true })
    public detail: string;

    @Column({ type: DataType.JSONB, allowNull: true })
    public tags: JSON | null;

    @Column({ type: DataType.STRING(50), allowNull: false })
    public kpi_type: string;

    @Column({ type: DataType.JSONB, allowNull: false })
    public kpi_config: JSON | null;

    @Column({ type: DataType.BOOLEAN, allowNull: false })
    public is_mandatory: boolean;

    @Column({ type: DataType.JSONB, allowNull: false })
    public applicable_equipment_type: JSON | null;

    @Column({ type: DataType.JSONB, allowNull: false })
    public dependent_kpis: JSON | null;

    @Column({ type: DataType.JSONB, allowNull: false })
    public alternative_labels: JSON | null;

    @Column({ type: DataType.BOOLEAN, allowNull: false })
    public override_scenario: boolean;

}