import { Injectable } from '@nestjs/common';
import { AdminApiClient } from 'src/shared/clients';
import { multiObjectToInstance } from 'src/shared/helpers';
import { CurrentContext } from 'src/shared/types';
import { UserPermissionResponseDto } from '../dtos';

@Injectable()
export class PermissionService {
	constructor(private readonly adminApiClient: AdminApiClient) {}

	/**
	 * Get list of permissions with locations of a user.
	 * @param currentContext
	 * @returns
	 */
	public async getListOfUserPermissions(
		currentContext: CurrentContext,
	): Promise<UserPermissionResponseDto[]> {
		const { username } = currentContext.user;
		const permissions = await this.adminApiClient.getListOfUserPermissions(username);
		return multiObjectToInstance(UserPermissionResponseDto, permissions);
	}
}
