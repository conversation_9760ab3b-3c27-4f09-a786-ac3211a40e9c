import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsArray, IsNotEmpty, IsOptional } from 'class-validator';

export class ModelScenarioDataResponseDto {
	@ApiProperty({ type: Number })
	@Expose()
	public scenario_id: number;

	@ApiProperty({ type: String })
	@Expose()
	public kpi_code: string;

	@ApiProperty({ type: Number })
	@Expose()
	public value: number;
}
