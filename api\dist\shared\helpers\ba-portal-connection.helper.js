"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var BAPortalConnectionHelper_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.BAPortalConnectionHelper = void 0;
const common_1 = require("@nestjs/common");
const sequelize_1 = require("sequelize");
const sequelize_typescript_1 = require("sequelize-typescript");
const config_service_1 = require("../../config/config.service");
let BAPortalConnectionHelper = BAPortalConnectionHelper_1 = class BAPortalConnectionHelper {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(BAPortalConnectionHelper_1.name);
        this.connections = new Map();
        this.initializeConnections();
    }
    initializeConnections() {
        const { sourceConfig } = this.configService.getAppConfig();
        if (!sourceConfig || !sourceConfig.baportal) {
            this.logger.warn('No BA Portal configuration found');
            return;
        }
        try {
            const { database, username, password, port, host, dialect } = sourceConfig.baportal;
            const sequelize = new sequelize_typescript_1.Sequelize(database, username, password, {
                dialect: dialect,
                host,
                port,
                logging: false,
                dialectOptions: {
                    ssl: {
                        require: true,
                    },
                    options: {
                        requestTimeout: 300000,
                    },
                },
                ssl: true,
                pool: {
                    max: 5,
                    min: 0,
                    acquire: 30000,
                    idle: 10000,
                },
            });
            sequelize
                .authenticate()
                .then(() => {
                this.logger.log(`Successfully connected to BA Portal: ${database}`);
                this.connections.set('default', sequelize);
            })
                .catch(error => {
                this.logger.error(`Failed to connect to BA Portal: ${error.message}`);
            });
        }
        catch (error) {
            this.logger.error(`Error initializing BA Portal connection: ${error.message}`);
        }
    }
    getConnection(name = 'default') {
        if (!this.connections.has(name)) {
            this.logger.warn(`BA Portal connection '${name}' not found`);
            return null;
        }
        return this.connections.get(name);
    }
    executeQuery(query, connectionName = 'default') {
        return __awaiter(this, void 0, void 0, function* () {
            const connection = this.getConnection(connectionName);
            if (!connection) {
                throw new Error(`Azure Synapse connection '${connectionName}' not found`);
            }
            try {
                const results = yield connection.query(query, {
                    type: sequelize_1.QueryTypes.SELECT,
                    raw: true,
                });
                return results;
            }
            catch (error) {
                this.logger.error(`Error executing query on Azure Synapse: ${error.message}`);
                throw error;
            }
        });
    }
};
BAPortalConnectionHelper = BAPortalConnectionHelper_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_service_1.ConfigService])
], BAPortalConnectionHelper);
exports.BAPortalConnectionHelper = BAPortalConnectionHelper;
//# sourceMappingURL=ba-portal-connection.helper.js.map