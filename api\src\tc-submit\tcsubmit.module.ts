import { Module } from '@nestjs/common';
import {
	ModelRepository,
	ModelScenarioDataRepository,
	ModelScenarioRepository,
} from './repositories';
import { TcSubmitService } from './services';
import { TcSubmitController } from './controllers';
import { TcConfigService } from 'src/tc-config/services';
import { KpiCalculatorService } from 'src/tc-config/services/kpi-calculator.service';
import { BuSetupRepository, KpiConfigRepository, KpiDataRepository } from 'src/tc-config/repositories';
import { SequlizeOperator } from 'src/shared/helpers';

const repositories = [
  ModelRepository, 
  ModelScenarioRepository, 
  ModelScenarioDataRepository, 
  BuSetupRepository, 
  KpiConfigRepository, 
  KpiDataRepository
];

@Module({
	providers: [
    TcSubmitService, 
    TcConfigService, 
    KpiCalculatorService, 
    SequlizeOperator, 
    ...repositories],
	controllers: [TcSubmitController],
})
export class TcSubmitModule {}
