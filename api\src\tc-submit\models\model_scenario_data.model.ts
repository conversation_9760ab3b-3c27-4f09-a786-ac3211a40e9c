import { Column, DataType, Table } from 'sequelize-typescript';
import { NOTIFICATION_TYPE } from 'src/shared/enums/notification-type.enum';
import { enumToArray } from 'src/shared/helpers';
import { BaseModel } from 'src/shared/models';

@Table({ tableName: 'model_scenario_data' })
export class ModelScenarioData extends BaseModel<ModelScenarioData> {
    @Column({ type: DataType.NUMBER, allowNull: false })
    public scenario_id: number;

    @Column({ type: DataType.STRING(255), allowNull: false })
    public kpi_code: string;

    @Column({ type: 'DOUBLE PRECISION', allowNull: true })
    public value: number;
}