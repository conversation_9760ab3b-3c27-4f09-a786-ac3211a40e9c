import { <PERSON>, <PERSON> } from '@nestjs/common';
import { SchedulerService } from '../services';

@Controller('scheduler')
export class SchedulerController {
	constructor(private readonly schedulerService: SchedulerService) {}

	@Post('run-all')
	async runAllDataPulls(): Promise<{ message: string }> {
		await this.schedulerService.runScheduler();
		return { message: 'Data pull initiated for all KPIs' };
	}

    @Post('retry-failed')
	async retryFailedKpis(): Promise<{ message: string }> {
		await this.schedulerService.runRetryScheduler();
		return { message: 'Data pull initiated for all KPIs' };
	}
}
