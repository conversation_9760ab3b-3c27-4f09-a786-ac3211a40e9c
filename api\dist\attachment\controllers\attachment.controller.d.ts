import { AttachmentContentResponseDto } from '../dtos';
import { AttachmentService } from '../services/attachment.service';
export declare class AttachmentController {
    private readonly attachmentService;
    constructor(attachmentService: AttachmentService);
    getDraftsByIdAndActiveUser(fileId: string, res: any): Promise<void>;
    getProposalAttachmentMetaData(afeProposalId: number): Promise<AttachmentContentResponseDto[]>;
}
