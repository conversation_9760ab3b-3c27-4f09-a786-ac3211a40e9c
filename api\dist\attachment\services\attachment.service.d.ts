import { AttachmentApiClient } from 'src/shared/clients/attachment-api.client';
import { AttachmentContentResponseDto } from '../dtos';
export declare class AttachmentService {
    private readonly attachmentApiClient;
    constructor(attachmentApiClient: AttachmentApiClient);
    getContentByFileId(fileId: string): Promise<AttachmentContentResponseDto>;
    getProposalAttachmentMetaData(afeProposalId: number): Promise<AttachmentContentResponseDto[]>;
}
