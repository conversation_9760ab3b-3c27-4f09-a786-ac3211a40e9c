import { BaseRepository } from 'src/shared/repositories';
import { CurrentContext } from 'src/shared/types';
import { SyncLog } from '../models';
export declare class SyncLogRepository extends BaseRepository<SyncLog> {
    constructor();
    createSyncLog(request: any, currentContext: CurrentContext): Promise<SyncLog | null>;
    updateSyncLog(id: any, request: any, currentContext: CurrentContext): Promise<number | null>;
}
