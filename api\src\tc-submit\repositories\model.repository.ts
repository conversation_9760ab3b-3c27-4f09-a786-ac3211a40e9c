import { Injectable } from '@nestjs/common';
import { literal, Op } from 'sequelize';
import { BaseRepository } from 'src/shared/repositories';
import { CurrentContext, NotificationPayload } from 'src/shared/types';
import { Model } from '../models';

@Injectable()
export class ModelRepository extends BaseRepository<Model> {
	constructor() {
		super(Model);
	}

	public updateModel(
		id,
		request: Model,
		currentContext: CurrentContext,
	): Promise<number | null> {
		return this.update(request, currentContext, { where: { id } });
	}
}
