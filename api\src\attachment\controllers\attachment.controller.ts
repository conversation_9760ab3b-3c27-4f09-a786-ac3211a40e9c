import { <PERSON>, Get, Param, Res, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AttachmentContentResponseDto } from '../dtos';
import { AttachmentService } from '../services/attachment.service';

@ApiTags('Attachment API')
@Controller('attachment')
export class AttachmentController {
	constructor(private readonly attachmentService: AttachmentService) {}

	/*
	 * Get document content by id.
	 */
	@ApiResponse({
		status: 200,
		description: 'Get document content by id.',
		type: Buffer,
	})
	@Get('content/:fileId')
	public async getDraftsByIdAndActiveUser(@Param('fileId') fileId: string, @Res() res: any) {
		const data = await this.attachmentService.getContentByFileId(fileId);
		res.status(200);
		res.setHeader('Content-Type', data.attachment_content_type);
		res.setHeader('Content-Disposition', 'attachment; filename=' + data.attachment_name);
		const buffer: Buffer = Buffer.from(data.contents.data);
		res.write(buffer);
		res.end();
	}

	@ApiBearerAuth()
	@UseGuards(AuthGuard('oauth-bearer'))
	@ApiResponse({
		status: 200,
		description: 'Get meta data of Afe proposal attachments',
		type: [AttachmentContentResponseDto],
	})
	@Get(':afeProposalId/meta-data')
	public async getProposalAttachmentMetaData(
		@Param('afeProposalId') afeProposalId: number,
	) {
		return this.attachmentService.getProposalAttachmentMetaData(afeProposalId);
	}
}
