# <PROJECT NAME> API

---

# Overview

The API is built on the typescript [nestjs](https://nestjs.com/) framework and uses `Sequelize` with `Postgres` for the database.

# Setup

## 1. Installation

For development, you need to install a few dependencies:

- **Node.js**

  This project uses Node v. 16.16.0 (see [`.node-version`](.node-version)). It's recommended to use a Node Version
  Manager to install it, such as [fnm](https://github.com/Schniz/fnm).

- **[Nest.js](https://docs.nestjs.com/) CLI installation**

  ```sh
  npm install -g @nestjs/cli
  ```

- **Project Dependencies**

  Install all modules listed in `dependencies` and `devDependencies` with:

  ```sh
  npm i --production=false
  ```

- **Postgres**

  Postgres (v. 14.4) is the relational database that we use in this project. To install it on macOS:

  ```sh
  brew install postgres
  ```

  To install it on Windows: Download the installer [installer](https://www.postgresql.org/download/windows/)

  To install it on Linux (Ubuntu): Follow these [instructions](https://www.postgresql.org/download/linux/ubuntu/)

## 2. Configure environment

**Create a `.env` file with the required parameters**

**Postgres**

We must create a user and a database before proceeding.

Log into the postgres service:

```sh
psql postgres
```

Then create a new user and set a password (here we'll use `postgres` and `password`, respectively), and allow it to create databases:

```sql
CREATE ROLE postgres WITH LOGIN PASSWORD 'password';
ALTER ROLE postgres CREATEDB;
```

Then create a database:

```sql
CREATE DATABASE <DATABASE NAME>;
```

Now store the information about your user, password and database name in your `.env` file, e.g.:

```sh
DB_USER=postgres
DB_PASS=password
DB_HOST=127.0.0.1
DB_PORT=5432
DB_NAME=afe_db
DB_DIALECT=postgres
```

## 3. Start Services

**Postgres**

It's likely already running. For Unix machines, check the status with `systemctl status postgresql.service`.

On macOS you can start it with:

```sh
brew services start postgres
```

## 4. Run the Server

```sh
npm run start:dev # Run Node server in dev mode
```
