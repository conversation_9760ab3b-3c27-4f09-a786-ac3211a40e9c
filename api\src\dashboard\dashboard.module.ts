import { Modu<PERSON> } from '@nestjs/common';
import { AdminApiClient } from 'src/shared/clients';
import { SequlizeOperator } from 'src/shared/helpers';
import { SharedPermissionService } from 'src/shared/services';
import { DashboardController } from './controllers';
import { DashboardService } from './services';

const repositories = [
];

@Module({
	controllers: [
		DashboardController
	],
	providers: [
		DashboardService, 
		AdminApiClient, 
		SharedPermissionService,
		SequlizeOperator,
		...repositories
	],
})
export class DashboardModule { }
