import { Column, DataType, Table } from 'sequelize-typescript';
import { QUEUE_LOG_ACTION, SCHEDULER_TYPE } from 'src/shared/enums';
import { enumToArray } from 'src/shared/helpers';
import { BaseModel } from 'src/shared/models';
import { RuleCondition } from 'src/shared/types';
import { SchedulerRecipients } from '../types';

@Table({ tableName: 'sync_log' })
export class SyncLog extends BaseModel<SyncLog> {
    @Column({ field: 'start_time', allowNull: true, type: 'TIMESTAMP' })
    public start_time?: Date | null;

    @Column({ field: 'end_time', allowNull: true, type: 'TIMESTAMP' })
    public end_time?: Date | null;

    @Column({ field: 'sync_status', type: DataType.STRING, allowNull: true })
    public sync_status: string;

    @Column({ field: 'data_date_range', type: DataType.JSONB, allowNull: true })
    public data_date_range: any;

    @Column({ field: 'total_data', type: DataType.INTEGER, allowNull: true })
    public total_data?: number | null;

    @Column({ field: 'total_kpis', type: DataType.INTEGER, allowNull: true })
    public total_kpis?: number | null;

    @Column({ field: 'success_kpis', type: DataType.INTEGER, allowNull: true })
    public success_kpis?: number | null;

    @Column({ field: 'error_kpis', type: DataType.INTEGER, allowNull: true })
    public error_kpis?: number | null;

    @Column({ field: 'success_kpi_list', type: DataType.JSONB, allowNull: true })
    public success_kpi_list: any;

    @Column({ field: 'error_kpi_list', type: DataType.JSONB, allowNull: true })
    public error_kpi_list: any;

    @Column({ field: 'retry_count', type: DataType.INTEGER, allowNull: true })
    public retry_count?: number | null;

}
