import {
	Body,
	Controller,
	Get,
	HttpCode,
	Param,
	Post,
	Query,
	Req,
	UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Pagination } from 'src/core/pagination';
import { RequestContext } from 'src/shared/types';
import { TcSubmitService } from '../services';
import {
	CreateScenarioDto,
	KpiCalculateDto,
	KpiValuesUpdateDto,
	ModelChartDto,
	ModelDto,
	ModelListingDto,
	ModelRequestDto,
	RefreshKpiDto,
} from '../dtos';

@ApiTags('TC Submit APIs')
@ApiBearerAuth()
@UseGuards(AuthGuard('oauth-bearer'))
@Controller('tc-submit')
export class TcSubmitController {
	constructor(private readonly submitService: TcSubmitService) {}

	@Post('create-get-model-with-all-scenario')
	async createModelWithAllScenario(
		@Req() request: RequestContext,
		@Body() modelDto: ModelDto,
	): Promise<any> {
		return this.submitService.createOrGetModelWithAllScenario(request.currentContext, modelDto);
	}

	@Get('get-latest-active-model')
	@ApiResponse({ status: 200, description: 'Get all active models' })
	async getAllActiveModels(@Req() request: RequestContext): Promise<any> {
		return this.submitService.getLatestActiveModel(request.currentContext);
	}

	@Post('get-all-models')
	@ApiResponse({ status: 200, description: 'Get all active models' })
	async getAllModels(
		@Req() request: RequestContext,
		@Body() modelDto: ModelListingDto,
	): Promise<any> {
		return this.submitService.getAllModels(request.currentContext, modelDto);
	}

	@Get('get-model-by-id/:id')
	@ApiResponse({ status: 200, description: 'Get all active models' })
	async getModelById(@Req() request: RequestContext, @Param('id') id: number): Promise<any> {
		return this.submitService.getModelById(id);
	}

	@Post('save-model-chart-kpi')
	@ApiResponse({ status: 200, description: 'Save model summsry chart' })
	async saveModelChartKpi(
		@Req() request: RequestContext,
		@Body() modelDto: ModelChartDto,
	): Promise<any> {
		return this.submitService.saveModelChartKpi(modelDto, request.currentContext);
	}

	@Post('save-model-tabular-kpi')
	@ApiResponse({ status: 200, description: 'Save model summsry chart' })
	async saveModelTabularKpi(
		@Req() request: RequestContext,
		@Body() modelDto: ModelChartDto,
	): Promise<any> {
		return this.submitService.saveModelTabularKpi(modelDto, request.currentContext);
	}

	@Get('get-model-scenarios-by-id/:id')
	@ApiResponse({ status: 200, description: 'Get all active models' })
	async getModelScenariosById(
		@Req() request: RequestContext,
		@Param('id') id: number,
	): Promise<any> {
		return this.submitService.getModelScenariosById(id);
	}

	@Post('update-kpi-values')
	@ApiResponse({ status: 200, description: 'KPI values updated successfully' })
	async updateKpiValues(
		@Req() request: RequestContext,
		@Body() updateDto: KpiValuesUpdateDto,
	): Promise<any> {
		return this.submitService.updateKpiValues(
			request.currentContext,
			updateDto.model_id,
			updateDto.scenario_id,
			updateDto.kpi_values,
		);
	}

	@Post('create-new-scenario')
	@ApiResponse({ status: 201, description: 'New scenario created successfully' })
	async createNewScenario(
		@Req() request: RequestContext,
		@Body() createDto: CreateScenarioDto,
	): Promise<any> {
		return this.submitService.createNewScenario(request.currentContext, createDto.model_id);
	}

	@Post('publish-model')
	@ApiResponse({ status: 200, description: 'Model published successfully' })
	@ApiResponse({ status: 400, description: 'Invalid scenario data' })
	async publishModel(
		@Req() request: RequestContext,
		@Body() publishDto: { model_id: number },
	): Promise<any> {
		return this.submitService.publishModel(request.currentContext, publishDto.model_id);
	}

	@Post('refresh-kpi-values')
	@ApiResponse({ status: 200, description: 'KPI values refreshed successfully' })
	async refreshKpiValues(
		@Req() request: RequestContext,
		@Body() refreshDto: RefreshKpiDto,
	): Promise<any> {
		return this.submitService.refreshKpiValues(
			request.currentContext,
			refreshDto.model_id,
			refreshDto.scenario_ids,
		);
	}

	@Post('calculate-kpi-codes')
	@ApiResponse({ status: 200, description: 'KPI values calculated successfully' })
	async calculateKpiCodes(
		@Req() request: RequestContext,
		@Body() kpiCalculateDto: KpiCalculateDto,
	): Promise<any> {
		return this.submitService.calculateKpiValues(
			request.currentContext,
			kpiCalculateDto.model_id,
			kpiCalculateDto.scenario_id,
			kpiCalculateDto.kpi_codes,
		);
	}

	@Post('refresh-model')
	@ApiResponse({ status: 200, description: 'Model refreshed successfully' })
	async refreshModel(
		@Req() request: RequestContext,
		@Body() refreshDto: { model_id: number },
	): Promise<any> {
		return this.submitService.refreshModel(request.currentContext, refreshDto.model_id);
	}

	@Post('update-model-fetch-mechanism')
	@ApiResponse({ status: 200, description: 'Model refreshed successfully' })
	async updateModelFetchMechanism(
		@Req() request: RequestContext,
		@Body() refreshDto: { model_id: number, override_fetch_mechanism_type: string, pull_from_date?: Date, pull_to_date?: Date },
	): Promise<any> {
		return await this.submitService.updateModelFetchMechanism(request.currentContext, refreshDto.model_id, refreshDto.override_fetch_mechanism_type, refreshDto.pull_from_date, refreshDto.pull_to_date);
	}
}
