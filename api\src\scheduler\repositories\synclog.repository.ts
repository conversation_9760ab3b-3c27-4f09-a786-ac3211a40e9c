import { Injectable } from '@nestjs/common';
import { Op } from 'sequelize';
import { SCHEDULER_TYPE } from 'src/shared/enums';
import { QUEUE_LOG_ACTION } from 'src/shared/enums/queue-log-action-type.enum';
import { BaseRepository } from 'src/shared/repositories';
import { CurrentContext } from 'src/shared/types';
import { SyncLog } from '../models';

@Injectable()
export class SyncLogRepository extends BaseRepository<SyncLog> {
    constructor() {
        super(SyncLog);
    }

    public createSyncLog(request, currentContext: CurrentContext): Promise<SyncLog | null> {
		return this.save(request, currentContext);
	}

	public updateSyncLog(id, request, currentContext: CurrentContext): Promise<number | null> {
		return this.update(request, currentContext, { where: { id } });
	}

}