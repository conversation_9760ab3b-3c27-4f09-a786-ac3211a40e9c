import { Injectable } from '@nestjs/common';
import { toNumber } from 'lodash';
import { AttachmentApiClient } from 'src/shared/clients/attachment-api.client';
import { AttachmentResponseDto } from '../dtos/attachment-response.dto';
import { ATTACHMENT_ENTITY_TYPE } from '../enums';
import { multiObjectToInstance } from '../helpers';
import { replaceUrlVariable } from '../helpers/url-creator';
import {
	Attachment,
	BulkUploadResponse,
	NewAttachmentRequest,
	UpdateAttachmentRequest,
} from '../types/attachment.type';

@Injectable()
export class SharedAttachmentService {
	constructor(private readonly attachmentApiClient: AttachmentApiClient) { }

	/**
	 * Add Bulk Attachment
	 * @request Attachment[]
	 * @returns  BulkUploadResponse[]
	 */
	public async addBulkAttachment(
		attachments: Attachment[],
		entityId: number,
		entityType: string,
		relPath: string,
		userId: string,
	): Promise<BulkUploadResponse[]> {
		const newUploadPayload = attachments.map((attachmentData: Attachment) => {
			attachmentData.file_base64 = attachmentData.file_base64.split('base64,')[1];
			return {
				entity_id: entityId,
				entity_type: entityType,
				attachment_rel_path: replaceUrlVariable(relPath, { entity_id: entityId }),
				file_data: Buffer.from(attachmentData.file_base64, 'base64'),
				created_by: userId.toLowerCase(),
				attachment_name: attachmentData.attachment_name,
				attachment_content_type: attachmentData.attachment_content_type,
				attachment_content_size: attachmentData.size,
				description: attachmentData.description,
				meta_data_1: entityId.toString(),
				meta_data_2: attachmentData.fileimage,
				meta_data_3: attachmentData.size,
			} as NewAttachmentRequest;
		});

		if (attachments.length) {
			return this.attachmentApiClient.addBulkAttachments(newUploadPayload);
		}
	}

	/**
	 * Update Bulk Attachment
	 * @request Attachment[]
	 * @returns  null
	 */
	public async updateBulkAttachment(attachments: Attachment[]): Promise<null> {
		attachments.forEach(async (attachmentData: Attachment) => {
			const requestData = {
				id: toNumber(attachmentData.id),
				description: attachmentData.description,
			} as UpdateAttachmentRequest;
			await this.attachmentApiClient.updateAttachments(requestData);
		});

		return null;
	}

	/**
	 * Delete Bulk Attachment
	 * @request Attachment[]
	 * @returns  null
	 */
	public async deleteBulkAttachment(attachments: AttachmentResponseDto[]): Promise<null> {
		attachments.forEach(async (attachmentData: AttachmentResponseDto) => {
			await this.attachmentApiClient.deleteAttachmentByFileId(
				attachmentData.file_id,
			);
		});
		return null;
	}


	/**
	 * Perform Add And Update Activity for New or updated supporitng documents
	 * @param supportingDocuments 
	 * @param entityId 
	 * @param entityType 
	 * @param relativePath 
	 * @param userId 
	 * @returns 
	 */
	public async supportingDocumentsActivity(
		supportingDocuments: Attachment[],
		entityId: number,
		entityType: ATTACHMENT_ENTITY_TYPE,
		relativePath: string,
		userId: string,
	) {
		let uploadedDocumentList = [];
		const newAttachmentRequest = supportingDocuments.filter((newAttachment: Attachment) => {
			return newAttachment.IsNew;
		});
		const updateMetadataRequest = supportingDocuments.filter((updateRequest: Attachment) => {
			return !updateRequest.IsNew && updateRequest.IsEdited;
		});

		if (newAttachmentRequest.length) {
			await this.addBulkAttachment(
				newAttachmentRequest,
				entityId,
				entityType,
				relativePath,
				userId,
			);
		}

		if (updateMetadataRequest.length) {
			await this.updateBulkAttachment(updateMetadataRequest);
		}

		if (newAttachmentRequest.length) {
			const uploadedDocumentData = await this.attachmentApiClient.getAllAttachments(entityId, entityType);
			uploadedDocumentList = multiObjectToInstance(AttachmentResponseDto, uploadedDocumentData);
			uploadedDocumentList.forEach(uploadData => {
				uploadData.IsNew = false;
				uploadData.IsEdited = false;
			});
		}

		return uploadedDocumentList;
	}

	/**
	 * Perform Delete Activity for already existing supporitng documents
	 */
	public async deleteSupportingDocumentsActivity(supportingDocuments: AttachmentResponseDto[]) {
		const deleteAttachmentRequest = supportingDocuments.filter(
			(newAttachment: AttachmentResponseDto) => {
				return !newAttachment.IsNew;
			},
		);
		if (deleteAttachmentRequest.length) {
			await this.deleteBulkAttachment(deleteAttachmentRequest);
		}
		return null;
	}
}
