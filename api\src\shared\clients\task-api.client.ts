import { Inject, Injectable } from '@nestjs/common';
import { INTERNAL_API } from '../constants';
import { HttpService } from '../services/http.service';
import { AddTaskResponse, CancelOverDueTask, CancelTask, CompleteAllTask, CompleteTask, CreateTask, TaskData } from '../types';

@Injectable()
export class TaskApiClient {
	constructor(
		@Inject(INTERNAL_API.REQUEST_API_PROVIDER) private readonly requestApiProvider: HttpService,
	) { }

	public async createTaskWithoutUseDelegation(payload: CreateTask): Promise<number> {
		const { data } = await this.requestApiProvider.post('/task/add', { ...payload, use_delegation: false });
		return data;
	}

	public async createTaskWithUseDelegation(payload: CreateTask): Promise<AddTaskResponse[]> {
		const { data } = await this.requestApiProvider.post('/task/add', { ...payload, use_delegation: true });
		return data;
	}


	public async cancelTask(payload: CancelTask): Promise<null> {
		const { data } = await this.requestApiProvider.post('/task/cancel', payload);
		return data;
	}

	public async completeTask(payload: CompleteTask): Promise<null> {
		const { data } = await this.requestApiProvider.post('/task/complete', payload);
		return data;
	}

	public async getAllTasks(
		entityId: number,
		entityType: string,
		entitySection?: string,
	): Promise<TaskData[]> {
		const { data } = await this.requestApiProvider.post('/task/getAll', {
			entity_id: entityId,
			entity_type: entityType,
			entity_section: entitySection,
		});
		return data;
	}


	public async getAllPendingUserTasks(userId: string): Promise<TaskData[]> {
		const { data } = await this.requestApiProvider.post('/task/userTask', { user_name: userId });
		return data;
	}

	public async getTaskById(id: number): Promise<TaskData | null> {
		const { data } = await this.requestApiProvider.post('/task/getById', { id });
		return data;
	}

	public async cancelAllTasks(payload: CancelTask): Promise<null> {
		const { data } = await this.requestApiProvider.post('/task/cancelAll', payload);
		return data;
	}

	public async cancelOverdueTasks(payload: CancelOverDueTask): Promise<null> {
		const { data } = await this.requestApiProvider.post('/task/cancelOverdue', payload);
		return data;
	}

	public async completeAllTasks(payload: CompleteAllTask): Promise<null> {
		const { data } = await this.requestApiProvider.post('/task/completeAll', payload);
		return data;
	}
}
