{"database": {"host": "psql-hoapps-nonprod.postgres.database.azure.com", "port": 5432, "username": "cadevuser", "password": "LG(h]8SV5X", "db": "appsgoe", "dialect": "postgres", "schema": "uat_cop", "enableSSL": true}, "azureAD": {"authority": "https://login.microsoftonline.com", "tennantId": "2bd16c9b-7e21-4274-9c06-7919f7647bbb", "clientId": "1adb1c92-7181-447b-ae47-d5d5bbab1b65", "clientSecret": "****************************************", "version": "v2.0", "discovery": ".well-known/openid-configuration", "audience": "api://1adb1c92-7181-447b-ae47-d5d5bbab1b65", "graphApiUrl": "https://graph.microsoft.com", "graphApiVersion": "v1.0", "scope": ["General"]}, "swagger": {"user": "user", "password": "password"}, "microservices": {"adminApi": {"url": "http://adminapi.uat-admin.svc.cluster.local/api", "tennantId": "ebd93e46-23b9-45fb-93de-75dadf77efe0", "appId": "88ba922b-2ed1-48da-a886-3fcbd82ef5dd"}, "requestApi": {"url": "http://requestapi.uat-admin.svc.cluster.local/api", "tennantId": "ebd93e46-23b9-45fb-93de-75dadf77efe0", "appId": "88ba922b-2ed1-48da-a886-3fcbd82ef5dd"}, "notificationApi": {"url": "http://notification.uat-admin.svc.cluster.local/api", "tennantId": "ebd93e46-23b9-45fb-93de-75dadf77efe0", "appId": "88ba922b-2ed1-48da-a886-3fcbd82ef5dd"}}, "uiClient": {"baseUrl": "https://hoappsuat.dpworld.com/JC", "task": {"relativeUrl": "/tasks/{{taskId}}"}}, "webClientConfig": {"apiBaseUrl": "https://hoappsuat.dpworld.com/JC/api/api", "msDetail": {"authority": "https://login.microsoftonline.com/2bd16c9b-7e21-4274-9c06-7919f7647bbb", "clientId": "e602d1a3-8381-4200-9e11-aedeeeac5b9e", "redirectUrl": "https://hoappsuat.dpworld.com/JC", "scope": ["api://1adb1c92-7181-447b-ae47-d5d5bbab1b65/General"], "graphUrl": "https://graph.microsoft.com/v1.0/users", "graphScope": ["User.Read.All"]}}, "sourceConfig": {"synapse": {"dialect": "mssql", "host": "cdpz-prod-sharing-synapse-ondemand.sql.azuresynapse.net", "port": 1433, "username": "cdp_terminalopstool_login", "password": "d0jy440h-A*L", "database": "cdp_prod_serving", "schema": "rpt_pt_tos"}, "baportal": {"dialect": "mssql", "host": "***********", "port": 1433, "username": "tpaportal_DashboardUser", "password": "UKLXM2w22030$4", "database": "GOPS_TPAPORTAL", "schema": "dbo"}}, "logLevel": ["error"]}