import { ConsoleLogger, Global, Module } from '@nestjs/common';
import { ConfigService } from 'src/config/config.service';
import { INTERNAL_API, MS_GRAPH_API } from 'src/shared/constants';
import { InternalApiProviders, MSGraphProviders } from './providers';
import { LoggingInterceptor } from './interceptors';
import { LoggerService } from './services';

@Global()
@Module({
	providers: [
		LoggingInterceptor,
		LoggerService,
		ConsoleLogger,
		ConfigService,
		...InternalApiProviders,
		...MSGraphProviders,
	],
	exports: [
		LoggerService,
		INTERNAL_API.ADMIN_API_PROVIDER,
		INTERNAL_API.REQUEST_API_PROVIDER,
		INTERNAL_API.NOTIFICATION_API_PROVIDER,
		MS_GRAPH_API.MS_GRAPH_API_PROVIDER,
		MS_GRAPH_API.MS_GRAPH_HTTP_SERVICE_PROVIDER,
	],
})
export class CoreModule {}
