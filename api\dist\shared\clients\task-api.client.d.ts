import { HttpService } from '../services/http.service';
import { AddTaskResponse, CancelOverDueTask, CancelTask, CompleteAllTask, CompleteTask, CreateTask, TaskData } from '../types';
export declare class TaskApiClient {
    private readonly requestApiProvider;
    constructor(requestApiProvider: HttpService);
    createTaskWithoutUseDelegation(payload: CreateTask): Promise<number>;
    createTaskWithUseDelegation(payload: CreateTask): Promise<AddTaskResponse[]>;
    cancelTask(payload: CancelTask): Promise<null>;
    completeTask(payload: CompleteTask): Promise<null>;
    getAllTasks(entityId: number, entityType: string, entitySection?: string): Promise<TaskData[]>;
    getAllPendingUserTasks(userId: string): Promise<TaskData[]>;
    getTaskById(id: number): Promise<TaskData | null>;
    cancelAllTasks(payload: CancelTask): Promise<null>;
    cancelOverdueTasks(payload: CancelOverDueTask): Promise<null>;
    completeAllTasks(payload: CompleteAllTask): Promise<null>;
}
