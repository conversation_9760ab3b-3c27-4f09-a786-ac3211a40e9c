import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsArray, IsBoolean, ValidateNested } from 'class-validator';

export class BusinessEntityResponseDto {
	@ApiProperty()
	@Expose()
	public id: number;

	@ApiProperty()
	@Expose()
	public code: string;

	@ApiProperty({ name: 'shortName' })
	@Expose({ name: 'shortName', toPlainOnly: true, toClassOnly: true })
	public short_name: string;

	@ApiProperty({ name: 'fullName' })
	@Expose({ name: 'fullName' })
	public full_name: string;

	@ApiProperty({ required: false, name: 'parentId' })
	@Expose({ name: 'parentId' })
	public parent_id?: number | null;

	@ApiProperty({ name: 'entityType' })
	@Expose({ name: 'entityType' })
	public entity_type: string;

	@ApiProperty({ name: 'isNode' })
	@Expose({ name: 'isNode' })
	@IsBoolean()
	public is_node: boolean;

	@ApiProperty()
	@Expose()
	@IsBoolean()
	public active: boolean;

	@ApiProperty({ required: false, name: 'countryId' })
	@Expose({ name: 'countryId' })
	public country_id?: number | null;

	@ApiProperty({ required: false, name: 'mapCenter' })
	@Expose({ name: 'mapCenter' })
	public map_center?: string | null;

	@ApiProperty({ required: false, name: 'utcTimeDiff' })
	@Expose({ name: 'utcTimeDiff' })
	public utc_time_diff?: string | null;

	@ApiProperty({ required: false, type: [BusinessEntityResponseDto] })
	@ValidateNested({ each: true })
	@IsArray()
	@Expose()
	@Type(() => BusinessEntityResponseDto)
	public children?: BusinessEntityResponseDto[] | [] | null;

	constructor(partial: Partial<BusinessEntityResponseDto> = {}) {
		Object.assign(this, partial);
	}
}
