import { Injectable } from '@nestjs/common';
import { literal, Op, StringDataType } from 'sequelize';
import { BaseRepository } from 'src/shared/repositories';
import { CurrentContext, NotificationPayload } from 'src/shared/types';
import { KpiData } from '../models';

@Injectable()
export class KpiDataRepository extends BaseRepository<KpiData> {
	constructor() {
		super(KpiData);
	}

	public getAllByMonthYear(year: number, month: number): Promise<KpiData[] | null> {
		return this.findAll({
			where: {
				kpi_year: year,
				...(month ? { kpi_month: month } : {}),
			},
		});
	}

	public deleteAllKpiCodeByDateRange(
		kpiCode: String,
		startYear: number,
		endYear: number,
		startMonth: number,
		endMonth: number,
	): Promise<any> {
		return this.hardDeleteByCondition({
			kpi_code: kpiCode,
			[Op.or]: [
				// Case 1: Same year (e.g., Feb 2023 - May 2023)
				{
					kpi_year: startYear,
					kpi_month: { [Op.between]: [startMonth, endMonth] },
				},
				// Case 2: Cross-year (e.g., Nov 2023 - Feb 2024)
				{
					[Op.and]: [
						{ kpi_year: startYear, kpi_month: { [Op.gte]: startMonth } },
						{ kpi_year: endYear, kpi_month: { [Op.lte]: endMonth } },
					],
				},
			],
		});
	}
}
