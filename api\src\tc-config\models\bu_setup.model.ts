import { Column, DataType, Table } from 'sequelize-typescript';
import { NOTIFICATION_TYPE } from 'src/shared/enums/notification-type.enum';
import { enumToArray } from 'src/shared/helpers';
import { BaseModel } from 'src/shared/models';

@Table({ tableName: 'bu_setup' })
export class BuSetup extends BaseModel<BuSetup> {
    @Column({ type: DataType.NUMBER, allowNull: false })
    public entity_id: number;

    @Column({ type: DataType.STRING(255), allowNull: false })
    public entity_code: string | null;

    @Column({ type: DataType.JSONB, allowNull: true })
    public excluded_equipment_type: JSON;

    @Column({ type: DataType.JSONB, allowNull: false })
    public excluded_kpi: JSON | null;
}