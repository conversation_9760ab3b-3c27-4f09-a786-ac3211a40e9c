import { LogLevel } from "@nestjs/common/services";
export interface DatabaseConfig {
    host: string;
    port: number;
    username: string;
    password: string;
    db: string;
    dialect: string;
    schema: string;
    enableSSL: boolean;
}
export interface AzureADConfig {
    authority: string;
    tennantId: string;
    clientId: string;
    clientSecret: string;
    version: string;
    discovery: string;
    audience: string;
    graphApiUrl: string;
    graphApiVersion: string;
    scope: string[];
}
export interface SwaggerConfig {
    user: string;
    password: string;
}
export interface MicroserviceConfig {
    url: string;
    tennantId?: string;
    appId?: string;
    Authorization?: string;
}
export interface MicroservicesConfig {
    adminApi: MicroserviceConfig;
    requestApi: MicroserviceConfig;
    notificationApi: MicroserviceConfig;
    fusionApi: MicroserviceConfig;
}
export interface AppConfig {
    database: DatabaseConfig;
    azureAD: AzureADConfig;
    swagger: SwaggerConfig;
    microservices: MicroservicesConfig;
    uiClient: {
        baseUrl: string;
        task: TaskConfig;
    };
    businessEntityLevelForProjectReferenceNumber: string;
    webClientConfig: WebClientConfig;
    apiKey: string;
    oneAppApiKey: string;
    logLevel: LogLevel[];
    thresholdDaysForReminder: number;
    sourceConfig: SourceConfig;
}
export interface TaskConfig {
    relativeUrl: string;
}
export interface WebClientConfig {
    apiBaseUrl: string;
    msDetail: MsDetail;
}
export interface MsDetail {
    authority: string;
    clientId: string;
    redirectUrl: string;
    scope: string[];
    graphUrl: string;
    graphScope: string[];
}
export interface SourceConfig {
    synapse: DbConfig;
    baportal: DbConfig;
    databricks?: DatabricksConfig;
}
export interface DbConfig {
    dialect: string;
    host: string;
    port: number;
    username: string;
    password: string;
    database: string;
}
export interface DatabricksConfig {
    jdbcUrl: string;
    username: string;
    password: string;
    httpPath?: string;
    token?: string;
}
