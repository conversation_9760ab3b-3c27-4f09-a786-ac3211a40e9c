import { Injectable } from '@nestjs/common';
import { toNumber } from 'lodash';
import { Op } from 'sequelize';
import { IFilter } from '../interfaces/filter.interface';

@Injectable()
export class ConditionCreatorService {
	constructor() {}

	prepareWhereCondition(filter: IFilter) {
		let condition = {};
		Object.keys(filter).forEach((filterKey: string) => {
			const filterValue =
				filter[filterKey] && isNaN(filter[filterKey])
					? filter[filterKey]
					: toNumber(filter[filterKey]);
			condition[filterKey] = filterValue ? filterValue : null;
			condition = { ...condition };
		});
		return condition;
	}

	prepareWorkflowWhereCondition(filters: any) {
		let filterCondition = [];
		const {
			requestTypes,
			budgetType,
			businessEntities,
			projectComponents,
			isPublished,
			parentId,
			year
		} = filters;
		if (requestTypes?.length) {
			filterCondition.push({ requestTypeId: { [Op.in]: requestTypes } });
		}

		if (budgetType?.length) {
			filterCondition.push({ budgetType: { [Op.in]: budgetType } });
		}

		if (isPublished === true || isPublished === false) {
			filterCondition.push({ published:  isPublished });
		}

		if (businessEntities?.length) {
			filterCondition.push({ entityId: { [Op.in]: businessEntities } });
		}

		if (projectComponents?.length) {
			filterCondition.push({ projectComponentId: { [Op.in]: projectComponents } });
		}

		if (toNumber(parentId) >= 0) {
			filterCondition.push({ parentId: parentId ? parentId : null });
		}

		if (year) {
			filterCondition.push({ year: year });
		}

		return filterCondition;
	}
}
