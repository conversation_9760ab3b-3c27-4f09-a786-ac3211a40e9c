import { Sequelize } from 'sequelize-typescript';
import { createNamespace } from 'cls-hooked';
import { Injectable } from '@nestjs/common';
import { Transaction } from 'sequelize';
import { generateMigration } from 'sequelize-typescript-model-migration';
import path from 'path';

const cls = createNamespace('db-transactions');

/**
 * Database helper has wrapper functions of orm or database object.
 */
@Injectable()
export class DatabaseHelper {
	constructor(private readonly sequelize: Sequelize) {
		/**
		 * Sequelize doesn't automatically pass transactions to queries when we're using unmanaged transactions.
		 * Here we are  manually set the transaction property on the CLS namespace, just like Sequelize does on
		 * a managed transaction
		 */
		this.sequelize.Sequelize.useCLS(cls);
	}

	//Wrapper on sequelize transaction
	public startTransaction<T>(autoCallback: (t: Transaction) => PromiseLike<T>): Promise<T> {
		return this.sequelize.transaction(autoCallback);
	}

	public async generateMigration(migrationName: string = 'migration') {
		await generateMigration(this.sequelize, {
			outDir: path.join(__dirname, '../../database//migrations'),
			snapshotDir: path.join(__dirname, '../../database/snapshots'),
			migrationName: `${Date.now()}-${migrationName}`,
		});
	}
}
