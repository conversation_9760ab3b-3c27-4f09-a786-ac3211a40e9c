import { Inject, Injectable } from '@nestjs/common';
import { INTERNAL_API } from '../constants';
import { HttpService } from '../services/http.service';
import { GenerateNextSequenceNumberRequest } from '../types';

@Injectable()
export class RequestApiClient {
	constructor(
		@Inject(INTERNAL_API.REQUEST_API_PROVIDER) private readonly requestApiProvider: HttpService,
	) {}

	public async generateNextSequenceNumber(
		payload: GenerateNextSequenceNumberRequest,
	): Promise<number> {
		const { data } = await this.requestApiProvider.post('/request/generateNextSequence', payload);
		return data;
	}
}
