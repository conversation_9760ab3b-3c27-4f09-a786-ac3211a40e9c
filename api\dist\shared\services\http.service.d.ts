import { AxiosInstance, AxiosRequestConfig, AxiosRequestHeaders, AxiosResponse } from 'axios';
import { HttpMethod, HttpResponse } from '../types';
type BodyFormat = 'json' | 'formParams';
export declare class HttpService {
    private request;
    private readonly axiosInstance;
    private bodyFormat;
    constructor();
    requestConfig(): AxiosRequestConfig;
    axios(): AxiosInstance;
    withBaseUrl(baseUrl: string): this;
    withHeaders(headers: AxiosRequestHeaders): this;
    withQueryParams(queryParams: object): this;
    withBasicAuth(username: string, password: string): this;
    withToken(token: string, type?: string): this;
    withOptions<D = any>(options?: AxiosRequestConfig<D>): this;
    withPayload(data: any): this;
    withTimeout(timeout: number): this;
    withTimeoutInSeconds(timeout: number): this;
    asJson(): this;
    asFormParams(): this;
    payloadFormat(format: BodyFormat): this;
    accept(accept: string): this;
    acceptJson(): this;
    contentType(contentType: string): this;
    get(url: string, queryParams?: object): Promise<any>;
    post(url: string, payload?: any): Promise<HttpResponse>;
    put(url: string, payload?: any): Promise<HttpResponse>;
    patch(url: string, payload?: any): Promise<HttpResponse>;
    delete(url: string, queryParams?: object): Promise<HttpResponse>;
    options(url: string, queryParams?: object): Promise<HttpResponse>;
    send(method: HttpMethod, url: string): Promise<HttpResponse>;
    createAndSendRequest(method: HttpMethod, url: string): Promise<AxiosResponse>;
    prepareRequestPayload(): any;
}
export {};
