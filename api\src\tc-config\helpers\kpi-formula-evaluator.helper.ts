import * as math from 'mathjs';

/**
 * Evaluates a KPI formula by replacing placeholders with actual values
 * @param formula The formula string with placeholders like {#kpi_code#}
 * @param kpiValues Object containing kpi_code -> value mappings
 * @returns The calculated result, -1 if evaluation fails
 */
export const evaluateKpiFormula = (formula: string, kpiValues: Record<string, any>): number => {
  try {
    if (!formula) return -1;
    
    // Check if all dependencies are available
    const dependencies = extractDependentKpisFromFormula(formula);
    const missingDependencies = dependencies.filter(dep => 
      kpiValues[dep] === undefined || kpiValues[dep] === null || isNaN(kpiValues[dep])
    );
    
    if (missingDependencies.length > 0) {
      console.warn(`Missing dependencies for formula: ${formula}. Missing: ${missingDependencies.join(', ')}`);
      return -1;
    }
    
    // Replace placeholders in formula
    let processedFormula = formula;
    
    // Replace {#kpi_code#} with actual values
    const regex = /\{#(.*?)#\}/g;
    processedFormula = processedFormula.replace(regex, (match, kpiCode) => {
      // Ensure the value is a valid number
      const value = kpiValues[kpiCode];
      if (value === undefined || value === null || isNaN(Number(value))) {
        console.warn(`Invalid value for KPI ${kpiCode}: ${value}`);
        return '0';
      }
      return value.toString();
    });
    
    // Handle MIN/MAX functions before evaluation
    let evaluableFormula = processedFormula;
    
    // Handle MIN function
    const minRegex = /MIN\((.*?),(.*?)\)/g;
    evaluableFormula = evaluableFormula.replace(minRegex, (_, a, b) => {
      return `min(${a.trim()},${b.trim()})`;
    });
    
    // Handle MAX function
    const maxRegex = /MAX\((.*?),(.*?)\)/g;
    evaluableFormula = evaluableFormula.replace(maxRegex, (_, a, b) => {
      return `max(${a.trim()},${b.trim()})`;
    });
    
    // Evaluate the formula using mathjs
    const result = safeEvaluate(evaluableFormula, formula);
    
    // Check if result is NaN or undefined
    if (result === null || isNaN(result) || result === Infinity) {
      console.warn(`Formula evaluation resulted in NaN or Infinity: ${formula} -> ${evaluableFormula}`);
      return 0;
    }
    
    return result;
  } catch (error) {
    console.error('Error evaluating KPI formula:', error, 'Formula:', formula);
    return 0;
  }
};

/**
 * Extracts dependent KPI codes from a formula
 * @param formula The formula string with placeholders like {#kpi_code#}
 * @returns Array of dependent KPI codes
 */
export const extractDependentKpisFromFormula = (formula: string): string[] => {
  if (!formula) return [];
  
  const regex = /\{#(.*?)#\}/g;
  const matches = formula.matchAll(regex);
  const dependentKpis = new Set<string>();
  
  for (const match of matches) {
    if (match[1]) {
      dependentKpis.add(match[1]);
    }
  }
  
  return Array.from(dependentKpis);
};

/**
 * Safely evaluates a mathematical expression using mathjs
 * @param expression The mathematical expression to evaluate
 * @param formula The original formula (for logging)
 * @returns The calculated result or null if evaluation fails
 */
export const safeEvaluate = (expression: string, formula: string): number | null => {
  try {
    // Create a limited math.js scope with only safe functions
    const limitedEval = math.evaluate;
    
    // Set up a safe evaluation scope
    const scope = {
      min: Math.min,
      max: Math.max
    };
    
    // Evaluate the expression in the safe scope
    const result = limitedEval(expression, scope);
    
    // Explicitly check for NaN
    if (typeof result !== 'number' || isNaN(result)) {
      console.warn(`Invalid result from evaluation: ${result} for expression: ${expression}`);
      return null;
    }
    
    return result;
  } catch (error) {
    console.error('Error in safe evaluation:', error, ' Formula', formula, ' Expression:', expression);
    console.log('Error in formula evaluation: Formula', formula, ' Expression:', expression);
    return null;
  }
};







