import { Column, DataType, Table } from 'sequelize-typescript';
import { NOTIFICATION_TYPE } from 'src/shared/enums/notification-type.enum';
import { enumToArray } from 'src/shared/helpers';
import { BaseModel } from 'src/shared/models';

@Table({ tableName: 'model_scenario' })
export class ModelScenario extends BaseModel<ModelScenario> {
    @Column({ type: DataType.NUMBER, allowNull: false })
    public model_id: number;

    @Column({ type: DataType.BOOLEAN, allowNull: true })
    public is_current_state: boolean;

    @Column({ type: DataType.NUMBER, allowNull: true })
    public scenario_number:number;

    @Column({ type: DataType.JSONB, allowNull: true })
    public kpi_data: JSON | null;
}