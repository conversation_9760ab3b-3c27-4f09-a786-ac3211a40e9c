import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsA<PERSON>y, IsNotEmpty, IsOptional } from 'class-validator';

export class KpiDataResponseDto {
	@ApiProperty({ type: String })
	@Expose()
	kpi_code: string;

	@ApiProperty({ type: Number })
	@Expose()
	public kpi_day: number | null;

	@ApiProperty({ type: Number })
    @Expose()
	public kpi_month: number;

	@ApiProperty({ type: Number })
    @Expose()
	public kpi_year: number;

	@ApiProperty({ type: Number })
    @Expose()
	public kpi_value: number;
}
