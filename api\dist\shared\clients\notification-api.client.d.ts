import { HttpService } from '../services/http.service';
import { CancelInviteRequest, SendNotificationRequest } from '../types';
export declare class NotificationApiClient {
    private readonly notificationApiProvider;
    constructor(notificationApiProvider: HttpService);
    sendNotification(payload: SendNotificationRequest): Promise<null>;
    cancelInvite(payload: CancelInviteRequest): Promise<{
        result: boolean;
    }>;
}
