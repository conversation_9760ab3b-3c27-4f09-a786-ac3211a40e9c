import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsNotEmpty, IsNumber } from 'class-validator';

export class PublishScenarioDto {
    @ApiProperty({ name: 'model_id', type: Number, description: 'Model ID' })
    @Expose({ name: 'model_id' })
    @IsNotEmpty()
    @IsNumber()
    public model_id: number;

    @ApiProperty({ name: 'scenario_id', type: Number, description: 'Scenario ID' })
    @Expose({ name: 'scenario_id' })
    @IsNotEmpty()
    @IsNumber()
    public scenario_id: number;
}