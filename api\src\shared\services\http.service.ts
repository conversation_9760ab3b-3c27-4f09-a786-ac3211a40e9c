import { HttpException } from '@nestjs/common';
import Axios, {
	AxiosInstance,
	AxiosRequestConfig,
	AxiosRequestHeaders,
	AxiosResponse,
} from 'axios';
import { HttpStatus } from '../enums';
import { HttpMethod, HttpResponse } from '../types';

type BodyFormat = 'json' | 'formParams';

export class HttpService {
	private request: AxiosRequestConfig;
	private readonly axiosInstance: AxiosInstance;
	private bodyFormat: BodyFormat = 'json';

	/**
	 * Createa new pending HTTP request instance.
	 */
	constructor() {
		this.request = {};
		this.axiosInstance = Axios.create();
		this.asJson();
	}

	/**
	 * Returns the Axios request config.
	 * @returns {AxiosRequestConfig}
	 */
	requestConfig(): AxiosRequestConfig {
		return this.request;
	}

	/**
	 * Returns the Axios instance.
	 * @returns {AxiosInstance}
	 */
	axios(): AxiosInstance {
		return this.axiosInstance;
	}

	/**
	 * Use the given `baseUrl` for all requests.
	 * @param {String} baseUrl
	 */
	withBaseUrl(baseUrl: string): this {
		if (typeof baseUrl !== 'string') {
			throw new Error(`The base URL must be a string. Received "${typeof baseUrl}"`);
		}
		this.request.baseURL = baseUrl;
		return this;
	}

	/**
	 * Add request headers.
	 * @param {AxiosRequestHeaders} headers
	 */
	withHeaders(headers: AxiosRequestHeaders): this {
		this.request.headers = { ...this.request.headers, ...headers };
		return this;
	}

	/**
	 * Add query parameters to the request.
	 * @param {Object} queryParams
	 */
	withQueryParams(queryParams: object): this {
		this.request.params = { ...this.request.params, ...queryParams };
		return this;
	}

	/**
	 * Add basic authentication via `username` and `password` to the request.
	 * @param {String} username
	 * @param {String} password
	 */
	withBasicAuth(username: string, password: string): this {
		this.request.auth = { username, password };
		return this;
	}

	/**
	 * Add an authorization `token` to the request.
	 * @param {String} token
	 * @param {String} type
	 */
	withToken(token: string, type: string = 'Bearer'): this {
		return this.withHeaders({
			Authorization: `${type} ${token}`.trim(),
		});
	}

	/**
	 * Merge your own custom Axios options into the request.
	 * @param {Object} options
	 */
	withOptions<D = any>(options: AxiosRequestConfig<D> = {}): this {
		Object.assign(this.request, options);
		return this;
	}

	/**
	 * Add a request payload.
	 * @param {*} data
	 */
	withPayload(data: any): this {
		this.request.data = data;
		return this;
	}

	/**
	 * Define the request `timeout` in milliseconds.
	 * @param {Number} timeout
	 */
	withTimeout(timeout: number): this {
		this.request.timeout = timeout;
		return this;
	}

	/**
	 * Define the request `timeout` in seconds.
	 * @param {Number} timeout
	 */
	withTimeoutInSeconds(timeout: number): this {
		return this.withTimeout(timeout * 1000);
	}

	/**
	 * Send the request as JSON payload.
	 */
	asJson(): this {
		return this.payloadFormat('json').contentType('application/json');
	}

	/**
	 * Send the request as form parameters,
	 * encoded as URL query parameters.
	 */
	asFormParams(): this {
		return this.payloadFormat('formParams').contentType('application/x-www-form-urlencoded');
	}

	/**
	 * Set the request payload format.
	 * @param {String} format
	 */
	payloadFormat(format: BodyFormat): this {
		this.bodyFormat = format;

		return this;
	}

	/**
	 * Set the `Accept` request header. This indicates what
	 * content type the server should return.
	 */
	accept(accept: string): this {
		return this.withHeaders({ Accept: accept });
	}

	/**
	 * Set the `Accept` request header to JSON. This indicates
	 * that the server should return JSON data.
	 * @param {String} accept
	 */
	acceptJson(): this {
		return this.accept('application/json');
	}

	/**
	 * Set the `Content-Type` request header.
	 * @param {String} contentType
	 */
	contentType(contentType: string): this {
		return this.withHeaders({ 'Content-Type': contentType });
	}

	/**
	 * Send an HTTP GET request, optionally with the given `queryParams`.
	 * @param {String} url
	 * @param {Object} queryParams
	 * @throws
	 */
	async get(url: string, queryParams: object = {}): Promise<any> {
		this.withQueryParams(queryParams);
		return this.send('GET', url);
	}

	/**
	 * Send an HTTP POST request, optionally with the given `payload`.
	 * @param {String} url
	 * @param {Object} payload
	 * @throws
	 */
	async post(url: string, payload?: any): Promise<HttpResponse> {
		if (payload) {
			this.withPayload(payload);
		}
		return this.send('POST', url);
	}

	/**
	 * Send an HTTP PUT request, optionally with the given `payload`.
	 * @param {String} url
	 * @param {Object} payload
	 * @throws
	 */
	async put(url: string, payload?: any): Promise<HttpResponse> {
		if (payload) {
			this.withPayload(payload);
		}
		return this.send('PUT', url);
	}

	/**
	 * Send an HTTP PATCH request, optionally with the given `payload`.
	 * @param {String} url
	 * @param {Object} payload
	 * @throws
	 */
	async patch(url: string, payload?: any): Promise<HttpResponse> {
		if (payload) {
			this.withPayload(payload);
		}
		return this.send('PATCH', url);
	}

	/**
	 * Send an HTTP DELETE request, optionally with the given `queryParams`.
	 * @param {String} url
	 * @param {Object} queryParams
	 * @throws
	 */
	async delete(url: string, queryParams: object = {}): Promise<HttpResponse> {
		this.withQueryParams(queryParams);
		return this.send('DELETE', url);
	}

	/**
	 * Send an HTTP OPTIONS request, optionally with the given `queryParams`.
	 * @param {String} url
	 * @param {Object} queryParams
	 * @throws
	 */
	async options(url: string, queryParams: object = {}): Promise<HttpResponse> {
		this.withQueryParams(queryParams);
		return this.send('OPTIONS', url);
	}

	/**
	 * Send the HTTP request.
	 * @param {String} method
	 * @param {String} url
	 * @throws
	 */
	async send(method: HttpMethod, url: string): Promise<HttpResponse> {
		try {
			const result = await this.createAndSendRequest(method, url);
			const { headers, data, status } = result;
			return {
				headers,
				data,
				status,
			};
		} catch (error) {
			if (error.response) {
				const { status, data } = error.response;
				throw new HttpException(data.message, status);
			} else if(error.message) {
				throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
			} else {
				throw new HttpException('Internal Server Error', HttpStatus.INTERNAL_SERVER_ERROR);
			}
		}
	}

	/**
	 * Create and send the HTTP request.
	 * @param {String} method
	 * @param {String} url
	 * @returns {Request}
	 */
	async createAndSendRequest(method: HttpMethod, url: string): Promise<AxiosResponse> {
		return await this.axiosInstance({
			url,
			method,
			withCredentials: true,
			...this.request,
			data: this.prepareRequestPayload(),
			maxContentLength: 100000000,
			maxBodyLength: 1000000000
		});
	}

	/**
	 * Returns the request payload depending on the selected request payload format.
	 */
	prepareRequestPayload(): any {
		return this.bodyFormat === 'formParams'
			? new URLSearchParams(this.request.data).toString()
			: this.request.data;
	}
}
