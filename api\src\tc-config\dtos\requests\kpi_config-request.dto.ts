import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsArray, IsNotEmpty, IsNumber, IsOptional } from 'class-validator';
import { json } from 'express';

export class KpiConfigRequestDto {
	@ApiProperty({ type: String })
	@IsNotEmpty()
	public system_code: string;

	@ApiProperty({ type: String })
	@IsNotEmpty()
	public title: string;

	@ApiProperty({ type: String })
	@IsOptional()
	public detail : string;

	@ApiProperty({ type: Object })
    @IsOptional()
	public tags: JSON | null;

	@ApiProperty({ type: String })
	@IsOptional()
	public kpi_type : string;

	@ApiProperty({ type: Object })
    @IsOptional()
	public kpi_config: JSON | null;

	@ApiProperty({ type: Object })
    @IsOptional()
	public applicable_equipment_type: JSON | null;

	@ApiProperty({ type: Boolean })
	@IsOptional()
	public is_mandatory : Boolean;

	@ApiProperty({ type: Boolean })
	@IsOptional()
	public override_scenario : Boolean;
}

export class TagKpiConfigDto {

	@ApiProperty({ name: 'entityId' })
	@Expose({ name: 'entityId' })
	@IsNotEmpty()
	@IsNumber()
	public entityId: number;

	@ApiProperty({ name: 'year' })
	@Expose({ name: 'year' })
	@IsNotEmpty()
	@IsNumber()
	public year: number;

	@ApiProperty({ name: 'tags' })
	@Expose({ name: 'tags' })
	@IsNotEmpty()
	@IsArray()
	public tags: string[]| [];

	constructor(partial: Partial<TagKpiConfigDto> = {}) {
		Object.assign(this, partial);
	}
}

export class KpiConfigCodeDto {

	@ApiProperty({ name: 'model_id' })
	@Expose({ name: 'model_id' })
	@IsNotEmpty()
	@IsNumber()
	public model_id: number;

	@ApiProperty({ name: 'scenario_id' })
	@Expose({ name: 'scenario_id' })
	@IsNotEmpty()
	@IsNumber()
	public scenario_id: number;

	@ApiProperty({ name: 'system_codes' })
	@Expose({ name: 'system_codes' })
	@IsNotEmpty()
	@IsArray()
	public system_codes: string[]| [];

	@ApiProperty({ name: 'year' })
	@Expose({ name: 'year' })
	@IsNotEmpty()
	@IsNumber()
	public year: number;

	constructor(partial: Partial<KpiConfigCodeDto> = {}) {
		Object.assign(this, partial);
	}
}