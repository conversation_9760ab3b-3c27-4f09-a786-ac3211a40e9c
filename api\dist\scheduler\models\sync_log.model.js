"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SyncLog = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const models_1 = require("../../shared/models");
let SyncLog = class SyncLog extends models_1.BaseModel {
};
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'start_time', allowNull: true, type: 'TIMESTAMP' }),
    __metadata("design:type", Date)
], SyncLog.prototype, "start_time", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'end_time', allowNull: true, type: 'TIMESTAMP' }),
    __metadata("design:type", Date)
], SyncLog.prototype, "end_time", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'sync_status', type: sequelize_typescript_1.DataType.STRING, allowNull: true }),
    __metadata("design:type", String)
], SyncLog.prototype, "sync_status", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'data_date_range', type: sequelize_typescript_1.DataType.JSONB, allowNull: true }),
    __metadata("design:type", Object)
], SyncLog.prototype, "data_date_range", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'total_data', type: sequelize_typescript_1.DataType.INTEGER, allowNull: true }),
    __metadata("design:type", Number)
], SyncLog.prototype, "total_data", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'total_kpis', type: sequelize_typescript_1.DataType.INTEGER, allowNull: true }),
    __metadata("design:type", Number)
], SyncLog.prototype, "total_kpis", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'success_kpis', type: sequelize_typescript_1.DataType.INTEGER, allowNull: true }),
    __metadata("design:type", Number)
], SyncLog.prototype, "success_kpis", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'error_kpis', type: sequelize_typescript_1.DataType.INTEGER, allowNull: true }),
    __metadata("design:type", Number)
], SyncLog.prototype, "error_kpis", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'success_kpi_list', type: sequelize_typescript_1.DataType.JSONB, allowNull: true }),
    __metadata("design:type", Object)
], SyncLog.prototype, "success_kpi_list", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'error_kpi_list', type: sequelize_typescript_1.DataType.JSONB, allowNull: true }),
    __metadata("design:type", Object)
], SyncLog.prototype, "error_kpi_list", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'retry_count', type: sequelize_typescript_1.DataType.INTEGER, allowNull: true }),
    __metadata("design:type", Number)
], SyncLog.prototype, "retry_count", void 0);
SyncLog = __decorate([
    (0, sequelize_typescript_1.Table)({ tableName: 'sync_log' })
], SyncLog);
exports.SyncLog = SyncLog;
//# sourceMappingURL=sync_log.model.js.map