import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class CountResponseDto {
	@ApiProperty({ type: Number })
	@Expose()
	public totalCount: number;

	@ApiProperty({ type: Number })
	@Expose()
	public totalAmount: number;
}

export class StatusWiseAFEResponseDto {

	@ApiProperty()
	@Expose()
	public total: CountResponseDto;

    @ApiProperty()
	@Expose()
	public approved: CountResponseDto;

    @ApiProperty()
	@Expose()
	public rejected: CountResponseDto;

    @ApiProperty()
	@Expose()
	public inprocess: CountResponseDto;

    @ApiProperty()
	@Expose()
	public drafted: CountResponseDto;

}
