import { HttpService } from '../services/http.service';
import { GetAttachmentFileByIdResponse, GetAttachmentResponse, GetContentFileByIdResponse, NewAttachmentRequest, UpdateAttachmentRequest, BulkUploadResponse, MoveAttachmentsRequest, MoveAttachmentsResponse } from '../types';
export declare class AttachmentApiClient {
    private readonly attachmentApi;
    constructor(attachmentApi: HttpService);
    getAllAttachments(entity_id: number, entity_type: string, entity_section?: string): Promise<GetAttachmentResponse[]>;
    addBulkAttachments(newAttachmentRequests: NewAttachmentRequest[]): Promise<BulkUploadResponse[]>;
    addNewAttachments(newAttachmentRequest: NewAttachmentRequest): Promise<string>;
    updateAttachments(updateAttachmentRequest: UpdateAttachmentRequest): Promise<null>;
    getContentByFileId(file_id: string): Promise<GetContentFileByIdResponse>;
    getAttachmentByFileId(file_id: string): Promise<GetAttachmentFileByIdResponse>;
    deleteAttachmentByFileId(file_id: string): Promise<null>;
    moveAttachments(payload: MoveAttachmentsRequest): Promise<MoveAttachmentsResponse>;
}
