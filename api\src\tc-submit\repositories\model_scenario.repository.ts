import { Injectable } from '@nestjs/common';
import { literal, Op } from 'sequelize';
import { BaseRepository } from 'src/shared/repositories';
import { CurrentContext, NotificationPayload } from 'src/shared/types';
import { ModelScenario } from '../models';

@Injectable()
export class ModelScenarioRepository extends BaseRepository<ModelScenario> {
    constructor() {
        super(ModelScenario);
    }

    public getAll(ids: number[]): Promise<ModelScenario[] | null> {
		return this.findAll({
			where: { ID: ids },
		});
	}


	public getById(id: number): Promise<ModelScenario | null> {
		return this.findById(id);
	}

	public createScenario(request, currentContext: CurrentContext): Promise<ModelScenario | null> {
		return this.save(request, currentContext);
	}

	public updateScenario(id, request: ModelScenario, currentContext: CurrentContext): Promise<number | null> {
		return this.update(request, currentContext, { where: { id } });
	}
}