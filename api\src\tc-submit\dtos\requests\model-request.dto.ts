import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsArray, IsNotEmpty, IsOptional } from 'class-validator';

export class ModelRequestDto {
	@ApiProperty({ name: 'entity_id', type: Number })
	@Expose({ name: 'entity_id' })
	@IsNotEmpty()
	entity_id: number;

	@ApiProperty({ name: 'entity_code', type: String })
	@Expose({ name: 'entity_code' })
	@IsNotEmpty()
	public entity_code: string | null;

	@ApiProperty({ name: 'summary_tabular_kpis', type: String })
	@Expose({ name: 'summary_tabular_kpis' })
	@IsOptional()
	public summary_tabular_kpis: any;

	@ApiProperty({ name: 'summary_chart_kpis', type: String })
	@Expose({ name: 'summary_chart_kpis' })
	@IsOptional()
	public summary_chart_kpis: any;

	@ApiProperty({ name: 'override_fetch_mechanism_type', type: String })
	@Expose({ name: 'override_fetch_mechanism_type' })
	@IsOptional()
	public override_fetch_mechanism_type: string | null;

	@ApiProperty({ name: 'pull_from_date', type: Date })
	@Expose({ name: 'pull_from_date' })
	@IsOptional()
	public pull_from_date: Date | null;

	@ApiProperty({ name: 'pull_to_date', type: Date })
	@Expose({ name: 'pull_to_date' })
	@IsOptional()
	public pull_to_date: Date | null;
}

export class ModelDto {
	@ApiProperty({ name: 'business_entity_id', type: Number })
	@Expose({ name: 'business_entity_id' })
	@IsNotEmpty()
	business_entity_id: number;

	@ApiProperty({ name: 'business_entity_code', type: String })
	@Expose({ name: 'business_entity_code' })
	@IsNotEmpty()
	public business_entity_code: string | null;

	@ApiProperty({ name: 'title', type: String })
	@Expose({ name: 'title' })
	@IsNotEmpty()
	public title: string | null;
}

export class ModelListingDto {
	@ApiProperty({ name: 'business_entity_code', type: Number })
	@Expose({ name: 'business_entity_code' })
	@IsNotEmpty()
	business_entity_code: number;
}

export class ModelChartDto {
	@ApiProperty({ name: 'model_id', type: Number })
	@Expose({ name: 'model_id' })
	@IsNotEmpty()
	model_id: number;

	@ApiProperty({
		description: 'Array of KPI codes',
		example: ['QUAY_TEU', 'YARD_CAPACITY'],
	})
	@Expose({ name: 'kpi_codes' })
	@IsArray()
	@IsNotEmpty()
	kpi_codes: string[];
}
