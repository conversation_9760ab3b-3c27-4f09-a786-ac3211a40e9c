import { Column, DataType, Table } from 'sequelize-typescript';
import { NOTIFICATION_TYPE } from 'src/shared/enums/notification-type.enum';
import { enumToArray } from 'src/shared/helpers';
import { BaseModel } from 'src/shared/models';

@Table({ tableName: 'kpi_data' })
export class KpiData extends BaseModel<KpiData> {

    @Column({ type: DataType.NUMBER, allowNull: false })
	public business_entity_id: number;

    @Column({ type: DataType.STRING(255), allowNull: false })
	public business_entity_code: string;

	@Column({ type: DataType.STRING(255), allowNull: false })
	public kpi_code: string;

	@Column({ type: DataType.NUMBER, allowNull: true })
	public kpi_day: number;

	@Column({ type: DataType.NUMBER, allowNull: true })
	public kpi_month: number;

	@Column({ type: DataType.NUMBER, allowNull: true })
	public kpi_year: number;

	@Column({ type: 'DOUBLE PRECISION', allowNull: true })
	public kpi_value: number;
}
