import { Logger, ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { json } from 'express';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import helmet from 'helmet';
import basicAuth from 'express-basic-auth';
import { AppModule } from './app.module';
import { ENV } from './shared/enums';
import { ConfigService } from './config/config.service';
import * as pg from 'pg';
declare const module:any;

async function bootstrap() {
	const app = await NestFactory.create(AppModule);
	const config = app.get(ConfigService).getAppConfig();

	const globalPrefix = 'api';
	app.setGlobalPrefix(globalPrefix);
	app.use(json({ limit: '200mb' }));
	app.enableCors();
	//add helmet middlewares to protect APIs from known vulnerabilities.
	app.use(helmet({ crossOriginResourcePolicy: false }));
	app.use(helmet.noSniff());
	app.use(helmet.hidePoweredBy());
	app.use(helmet.contentSecurityPolicy());

	app.useGlobalPipes(
		new ValidationPipe({
			whitelist: true,
			transform: true,
		}),
	);

	/**
	 * Setting up swagger documentation with authentication
	 **/
	if (process.env.NODE_ENV !== ENV.PROD) {
		app.use(
			['/doc', '/doc-json'],
			basicAuth({
				challenge: true,
				users: {
					[config.swagger.user]: config.swagger.password,
				},
			}),
		);

		const options = new DocumentBuilder()
			.addBearerAuth()
			.setTitle('<PROJECT NAME>')
			.setDescription('The <PROJECT NAME> service APIs')
			.setVersion('1.0')
			.addTag('<projectName>')
			.build();

		const document = SwaggerModule.createDocument(app, options);
		const swaggerOptions = { swaggerOptions: { persistAuthorization: true } };
		SwaggerModule.setup('doc', app, document, swaggerOptions);
	}

	const port = process.env.PORT || 5100;
	await app.listen(port, () => {
		Logger.log(`Listening at http://localhost:${port}/${globalPrefix}`);
	});

	//To Parse the pg database date to local timezone. [START]
	pg.defaults.parseInputDatesAsUTC = true;
	pg.types.setTypeParser(
		pg.types.builtins.TIMESTAMP, 
		(stringValue: string) => new Date(`${stringValue}Z`)
	);

	if (module.hot) {
		module.hot.accept();
		module.hot.dispose(() => app.close());
	}
	//To Parse the pg database date to local timezone. [END]
}
bootstrap();
