"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TcConfigModule = void 0;
const common_1 = require("@nestjs/common");
const repositories_1 = require("./repositories");
const services_1 = require("./services");
const controllers_1 = require("./controllers");
const kpi_calculator_service_1 = require("./services/kpi-calculator.service");
const helpers_1 = require("../shared/helpers");
const repositories_2 = require("../tc-submit/repositories");
const repositories = [repositories_1.BuSetupRepository, repositories_1.KpiConfigRepository, repositories_1.KpiDataRepository, repositories_2.ModelRepository];
let TcConfigModule = class TcConfigModule {
};
TcConfigModule = __decorate([
    (0, common_1.Module)({
        providers: [services_1.TcConfigService, kpi_calculator_service_1.KpiCalculatorService, helpers_1.SequlizeOperator, ...repositories],
        controllers: [controllers_1.TcConfigController],
    })
], TcConfigModule);
exports.TcConfigModule = TcConfigModule;
//# sourceMappingURL=tcconfig.module.js.map