import { BaseModel } from 'src/shared/models';
export declare class SyncLog extends BaseModel<SyncLog> {
    start_time?: Date | null;
    end_time?: Date | null;
    sync_status: string;
    data_date_range: any;
    total_data?: number | null;
    total_kpis?: number | null;
    success_kpis?: number | null;
    error_kpis?: number | null;
    success_kpi_list: any;
    error_kpi_list: any;
    retry_count?: number | null;
}
