import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsNumber } from 'class-validator';

export class KpiCalculateDto {
    @ApiProperty({
        description: 'Model ID',
        example: 1
    })
    @IsNumber()
    @IsNotEmpty()
    model_id: number;

    @ApiProperty({
        description: 'Scenario ID',
        example: 1
    })
    @IsNumber()
    @IsNotEmpty()
    scenario_id: number;

    @ApiProperty({
        description: 'Array of KPI codes to calculate',
        example: ['QUAY_TEU', 'YARD_CAPACITY']
    })
    @IsArray()
    @IsNotEmpty()
    kpi_codes: string[];
}