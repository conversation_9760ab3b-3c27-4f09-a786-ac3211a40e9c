interface Metadata {
	meta_data_1?: string;
	meta_data_2?: string;
	meta_data_3?: string;
}
export interface GetAttachmentResponse extends Metadata {
	id: number;
	attachment_name?: string;
	entity_id: number;
	additional_info?: object;
	file_id?: string;
	contents: Buffer;
	attachment_content_type: string;
	base64?: string;
	created_by: string;
	created_on: Date;
	IsNew?: Boolean;
}

export interface BulkUploadResponse {
	file_id: string;
	attachment_name: string;
}

export interface Entity {
	entity_id: number;
	entity_type: string;
	entity_section?: string; // optional
}

export interface NewAttachmentRequest extends Entity, Metadata {
	id?: number;
	file_data: Buffer;
	created_by: string;
	attachment_rel_path: string;
	description?: string;
	attachment_name: string;
	attachment_content_type: string;
	attachment_content_size: string;
	additional_info?: any; // optional
}

export interface UpdateAttachmentRequest extends Metadata {
	id: number;
	description?: string; //optional
	additional_info?: any; // optional
}

export interface FileId {
	file_id: string;
}

export interface GetContentFileByIdResponse extends Metadata {
	id: number;
	attachment_name?: string;
	entity_id: number;
	additional_info?: object;
	description?: string;
	file_id?: string;
	contents: {
		type: string;
		data: Buffer;
	};
	attachment_content_type: string;
	created_by: string;
	created_on: Date;
}

export interface GetAttachmentFileByIdResponse extends Metadata {
	id?: number;
	tennant_id?: string;
	application_id?: string;
	entity_type?: string;
	entity_id?: number;
	description?: string;
	entity_section?: string;
	attachment_name?: string;
	attachment_rel_path?: string;
	attachment_content_type?: string;
	attachment_content_size?: number;
	created_by?: string;
	created_on?: Date;
	additional_info?: object;
	file_id?: string;
}

export class Attachment {
	file_base64: any;
	attachment_name: string;
	type: string;
	fileimage: string | null;
	file: any;
	attachment_content_type: string;
	file_id: string;
	isdeleted: boolean;
	IsPrimary: boolean;
	id: number;
	IsNew = false;
	IsEdited = false;
	url: string;
	size: string;
	description: string;
	prev_description: string;
	additional_info: string;
	section: string;
	created_by: string;
	created_on: string;
	progress: number;
}

export class supportingDocuments {
	attachments: Attachment[];
}

export interface MoveAttachmentsRequest {
	source_entity_type: string;
	source_entity_id: number;
	destination_entity_type: string;
	destination_entity_id: number;
	destination_folder_path: string;
}

export interface MoveAttachmentsResponse {
	old_file_id: string;
	new_file_id: string;
}
