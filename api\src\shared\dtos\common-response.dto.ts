import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class CommonResposeDto {
	@ApiProperty()
	@Expose()
	public id: number;

	@ApiProperty()
	@Expose()
	public createdBy: number;

	@ApiProperty()
	@Expose()
	public createdOn: Date;

	@ApiProperty()
	@Expose()
	public updatedBy: number;

	@ApiProperty()
	@Expose()
	public updatedOn: Date;

	@ApiProperty()
	@Expose()
	public deletedBy: number;

	@ApiProperty()
	@Expose()
	public deleted: boolean;

	@ApiProperty()
	@Expose()
	public active: boolean;
}
