import { Column, DataType, Table } from 'sequelize-typescript';
import { NOTIFICATION_TYPE } from 'src/shared/enums/notification-type.enum';
import { enumToArray } from 'src/shared/helpers';
import { BaseModel } from 'src/shared/models';

@Table({ tableName: 'model' })
export class Model extends BaseModel<Model> {

    @Column({ type: DataType.STRING(1000), allowNull: false })
    public title: string | null;

    @Column({ type: DataType.NUMBER, allowNull: false })
    public entity_id: number;

    @Column({ type: DataType.STRING(255), allowNull: false })
    public entity_code: string | null;

    @Column({ type: DataType.JSONB, allowNull: true })
    public summary_tabular_kpis: any;

    @Column({ type: DataType.JSONB, allowNull: true })
    public summary_chart_kpis: any | null;

    @Column({ type: DataType.STRING(255), allowNull: true })
    public user_status: string | null;

    @Column({ type: DataType.STRING(255), allowNull: true })
    public workflow_status: string | null;

    @Column({ type: DataType.STRING(255), allowNull: true })
    public override_fetch_mechanism_type: string | null;

    @Column({ type: DataType.DATE, allowNull: true })
    public pull_from_date: Date | null;

    @Column({ type: DataType.DATE, allowNull: true })
    public pull_to_date: Date | null;
}