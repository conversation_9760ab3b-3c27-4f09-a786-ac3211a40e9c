{"compilerOptions": {"esModuleInterop": true, "types": ["node"], "module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "es6", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "resolveJsonModule": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false}}