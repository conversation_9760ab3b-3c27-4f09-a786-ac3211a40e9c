import { HISTORY_ACTION_TYPE, HISTORY_ENTITY_TYPE } from '../enums';

export interface AddHistoryRequest {
	created_by: string;
	entity_id: number;
	entity_type: HISTORY_ENTITY_TYPE;
	action_performed: HISTORY_ACTION_TYPE;
	action_date?: Date;
	comments?: string;
	additional_info?: any;
}

export interface CopyHistoryRequest {
	source_entity_id : number, 
	source_entity_type: string, 
	destination_entity_id: number, 
	destination_entity_type: string 
}

export interface HistoryResponse {
	action_performed: HISTORY_ACTION_TYPE;
	action_comments: string;
	action_date: Date;
	additional_info: any;
	created_by?: string;
}