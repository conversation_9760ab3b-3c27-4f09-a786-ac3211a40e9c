import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { LoggerService } from 'src/core/services';
import { INTERNAL_API } from '../constants';
import { HISTORY_ENTITY_TYPE } from '../enums';
import { HttpService } from '../services/http.service';
import { AddHistoryRequest, CopyHistoryRequest, HistoryResponse } from '../types';

@Injectable()
export class HistoryApiClient {
	constructor(
		@Inject(INTERNAL_API.REQUEST_API_PROVIDER) private readonly requestApiProvider: HttpService,
		private readonly loggerService: LoggerService

	) { }

	public async getRequestHistory(entityId: number, entityType: HISTORY_ENTITY_TYPE): Promise<HistoryResponse[]> {
		const { data } = await this.requestApiProvider.post('/history/getrequesthistory', {
			entity_id: entityId,
			entity_type: entityType,
		});
		return data;
	}

	public async addRequestHistory(payload: AddHistoryRequest, throwError: boolean = false): Promise<null> {
		try {
			const { data } = await this.requestApiProvider.post('/history/addrequesthistory', payload);
			return data;
		} catch (err) {
			if(throwError) {
				throw err;
			} else {
				this.loggerService.error( {...err}, err.stack )
			}
		}
	}

	public async addBulkRequestHistory(payload: AddHistoryRequest[], throwError: boolean = false): Promise<null> {
		try {
			const { data } = await this.requestApiProvider.post('/history/addbulk', {
				data: payload
			});
			return data;
		} catch (err) {
			if(throwError) {
				throw err;
			} else {
				this.loggerService.error( {...err}, err.stack )
			}
		}
	}

	public async copyRequestHistory(payload: CopyHistoryRequest, throwError: boolean = false): Promise<null> {
		try {
			const { data } = await this.requestApiProvider.post('/history/copyentity', payload);
			return data;
		} catch (err) {
			if(throwError) {
				throw err;
			} else {
				this.loggerService.error( {...err}, err.stack )
			}
		}
	}

	public async copyBulkRequestHistory(payload: CopyHistoryRequest[], throwError: boolean = false): Promise<null> {
		try {
			const { data } = await this.requestApiProvider.post('/history/copyentitybulk', {
				data: payload
			});
			return data;
		} catch (err) {
			if(throwError) {
				throw err;
			} else {
				this.loggerService.error( {...err}, err.stack )
			}
		}
	}
}
