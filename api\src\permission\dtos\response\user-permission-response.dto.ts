import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsArray } from 'class-validator';

export class UserPermissionResponseDto {
	@ApiProperty()
	@Expose()
	public permissionName: string;

	@ApiProperty()
	@Expose()
	public applicationId: string;

	@ApiProperty()
	@IsArray()
	@Expose()
	public locations: string[] | [];

	constructor(partial: Partial<UserPermissionResponseDto> = {}) {
		Object.assign(this, partial);
	}
}
