import {
	ClassConstructor,
	ClassTransformOptions,
	instanceToPlain as instanceToValue,
	plainToInstance,
} from 'class-transformer';

/**
 * Wrapper around plainToInstance function of class-transformer
 * function plainToInstance<T, V>(cls: ClassConstructor<T>, plain: V, options?: ClassTransformOptions): T;
 * @param cls
 * @param plain
 * @param options
 * @returns
 */
export function singleObjectToInstance<T, V>(
	cls: ClassConstructor<T>,
	plain: V,
	options: ClassTransformOptions = { excludeExtraneousValues: true },
): T {
	return plainToInstance(cls, plain, options);
}

/**
 * Wrapper around plainToInstance function of class-transformer
 * function plainToInstance<T, V[]>(cls: ClassConstructor<T>, plain: V, options?: ClassTransformOptions): T[];
 * @param cls
 * @param plain
 * @param options
 * @returns
 */
export function multiObjectToInstance<T, V>(
	cls: ClassConstructor<T>,
	plain: V[],
	options: ClassTransformOptions = { excludeExtraneousValues: true },
): T[] {
	return plainToInstance(cls, plain, options);
}

/**
 * Wrapper around instanceToPlain function of class-transformer
 * function instanceToPlain<T>(object: T, options?: ClassTransformOptions): Record<string, any>;
 * @param object
 * @param options
 * @returns
 */
export function instanceToPlain<T, V>(
	object: T,
	options?: ClassTransformOptions,
): Record<string, any> {
	return instanceToValue(object, options);
}
