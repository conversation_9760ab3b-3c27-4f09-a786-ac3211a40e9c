import { QUEUE_LOG_ACTION } from 'src/shared/enums';
import { BaseRepository } from 'src/shared/repositories';
import { CurrentContext } from 'src/shared/types';
import { QueueLog } from '../models';
export declare class QueueLogRepository extends BaseRepository<QueueLog> {
    constructor();
    createQueueLogEntry(payload: any, currentContext: CurrentContext): Promise<QueueLog | null>;
    getUnprocessedQueueLogs(): Promise<QueueLog[]>;
    getQueueLogsByLogConditionAfterLastRunAt(entityIds: number[], actions: QUEUE_LOG_ACTION[], finalApproval: boolean, rule: any, lastRunAt: any): Promise<QueueLog[]>;
    markQueueLogAsProcessed(id: number, currentContext: CurrentContext): Promise<void>;
}
