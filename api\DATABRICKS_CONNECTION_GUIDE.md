# Databricks Connection Guide

This guide explains how to configure and use the DatalakeConnectionHelper to connect to Databricks using JDBC URL and username/password authentication.

## Configuration

The DatalakeConnectionHelper connects to Databricks using JDBC URL format with username and password authentication. The implementation uses Databricks SQL API under the hood while maintaining a JDBC-like interface.

### Configuration Structure

Add the following configuration to your `config.json`, `config.dev.json`, and `config.staging.json` files:

```json
{
  "sourceConfig": {
    "databricks": {
      "jdbcUrl": "**************************************************************************************************************************",
      "username": "your-databricks-username",
      "password": "your-databricks-password"
    }
  }
}
```

### Configuration Parameters

- **jdbcUrl**: The JDBC URL for your Databricks cluster/warehouse (includes httpPath parameter)
- **username**: Your Databricks username OR "Token" for token-based authentication
- **password**: Your Databricks password OR access token (when username is "Token")

### Example Configuration Values

Replace the placeholder values with your actual Databricks configuration:

**Token-based Authentication (Recommended):**
```json
{
  "sourceConfig": {
    "databricks": {
      "jdbcUrl": "*******************************************************************************************************************************************************;",
      "username": "Token",
      "password": "dapi1234567890abcdef1234567890abcdef"
    }
  }
}
```

**Username/Password Authentication:**
```json
{
  "sourceConfig": {
    "databricks": {
      "jdbcUrl": "***************************************************************************************************************************************",
      "username": "<EMAIL>",
      "password": "your-password"
    }
  }
}
```

## Usage

The DatalakeConnectionHelper is already integrated into the application and can be used in services via dependency injection.

### Example Usage in a Service

```typescript
import { Injectable } from '@nestjs/common';
import { DatalakeConnectionHelper } from 'src/shared/helpers';

@Injectable()
export class YourService {
  constructor(
    private readonly datalakeHelper: DatalakeConnectionHelper,
  ) {}

  async executeQuery() {
    try {
      const query = 'SELECT * FROM your_table LIMIT 10';
      const results = await this.datalakeHelper.executeQuery(query);
      console.log('Query results:', results);
      return results;
    } catch (error) {
      console.error('Query execution failed:', error.message);
      throw error;
    }
  }
}
```

## API Methods

### executeQuery(query: string, connectionName?: string): Promise<any[]>

Executes a SQL query on Databricks and returns the results.

**Parameters:**
- `query`: The SQL query to execute
- `connectionName`: Optional connection name (defaults to 'default')

**Returns:** Promise that resolves to an array of query results

**Example:**
```typescript
const results = await this.datalakeHelper.executeQuery(
  'SELECT COUNT(*) as total FROM my_table WHERE date >= "2023-01-01"'
);
```

### getConnection(name?: string): DatabricksConnection | null

Gets a Databricks connection instance.

**Parameters:**
- `name`: Optional connection name (defaults to 'default')

**Returns:** DatabricksConnection instance or null if not found

## Connection Details

The helper uses JDBC URL format but implements the connection using Databricks SQL API (`/api/2.0/sql/statements`) for optimal performance. This provides:

- **JDBC URL compatibility**: Standard JDBC URL format for easy configuration
- **HTTP-based communication**: Reliable connection using Databricks SQL API
- **Username/password authentication**: Simple credential-based authentication
- **Timeout handling**: Configurable query timeouts (default: 5 minutes)
- **Error handling**: Comprehensive error reporting and logging

## Migration from Azure Synapse

If you're migrating from Azure Synapse, the main changes are:

1. **Configuration**: Update your config files to use the `databricks` section instead of `synapse`
2. **Connection method**: The helper now uses HTTP API instead of direct database connections
3. **Query execution**: Same `executeQuery` method, but now targets Databricks

## Troubleshooting

### Common Issues

1. **Connection timeout**: Increase the timeout in the configuration if queries take longer than 5 minutes
2. **Authentication errors**: Verify your username/password or access token
3. **Warehouse not found**: Ensure the httpPath points to a valid SQL warehouse
4. **Invalid JDBC URL**: Check the format of your JDBC URL

### Logging

The helper provides detailed logging for connection and query execution. Check your application logs for:
- Connection establishment messages
- Query execution details
- Error messages with specific failure reasons

## Security Notes

- Store credentials securely (consider using environment variables)
- Use access tokens instead of passwords when possible
- Ensure your Databricks workspace has proper network security configured
- Regularly rotate access credentials
