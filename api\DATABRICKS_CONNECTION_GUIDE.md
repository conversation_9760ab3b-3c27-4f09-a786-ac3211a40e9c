# Databricks Connection Guide

This guide explains how to configure and use the DatalakeConnectionHelper to connect to Databricks instead of Azure Synapse.

## Configuration

The DatalakeConnectionHelper now connects to Databricks using the HTTP-based SQL API instead of traditional JDBC connections. You need to configure the Databricks connection in your configuration files.

### Configuration Structure

Add the following configuration to your `config.json`, `config.dev.json`, and `config.staging.json` files:

```json
{
  "sourceConfig": {
    "databricks": {
      "jdbcUrl": "**************************************************************************************************************************",
      "username": "your-databricks-username",
      "password": "your-databricks-password",
      "httpPath": "/sql/1.0/warehouses/your-warehouse-id"
    }
  }
}
```

### Configuration Parameters

- **jdbcUrl**: The JDBC URL for your Databricks cluster/warehouse
- **username**: Your Databricks username
- **password**: Your Databricks password or access token
- **httpPath**: The HTTP path to your SQL warehouse (extracted from the JDBC URL)

### Example Configuration Values

Replace the placeholder values with your actual Databricks configuration:

```json
{
  "sourceConfig": {
    "databricks": {
      "jdbcUrl": "***************************************************************************************************************************************",
      "username": "<EMAIL>",
      "password": "dapi1234567890abcdef1234567890abcdef",
      "httpPath": "/sql/1.0/warehouses/abc123def456"
    }
  }
}
```

## Usage

The DatalakeConnectionHelper is already integrated into the application and can be used in services via dependency injection.

### Example Usage in a Service

```typescript
import { Injectable } from '@nestjs/common';
import { DatalakeConnectionHelper } from 'src/shared/helpers';

@Injectable()
export class YourService {
  constructor(
    private readonly datalakeHelper: DatalakeConnectionHelper,
  ) {}

  async executeQuery() {
    try {
      const query = 'SELECT * FROM your_table LIMIT 10';
      const results = await this.datalakeHelper.executeQuery(query);
      console.log('Query results:', results);
      return results;
    } catch (error) {
      console.error('Query execution failed:', error.message);
      throw error;
    }
  }
}
```

## API Methods

### executeQuery(query: string, connectionName?: string): Promise<any[]>

Executes a SQL query on Databricks and returns the results.

**Parameters:**
- `query`: The SQL query to execute
- `connectionName`: Optional connection name (defaults to 'default')

**Returns:** Promise that resolves to an array of query results

**Example:**
```typescript
const results = await this.datalakeHelper.executeQuery(
  'SELECT COUNT(*) as total FROM my_table WHERE date >= "2023-01-01"'
);
```

### getConnection(name?: string): DatabricksConnection | null

Gets a Databricks connection instance.

**Parameters:**
- `name`: Optional connection name (defaults to 'default')

**Returns:** DatabricksConnection instance or null if not found

## Connection Details

The helper uses the Databricks SQL API (`/api/2.0/sql/statements`) to execute queries. This provides:

- **HTTP-based communication**: More reliable than JDBC for cloud environments
- **Built-in authentication**: Uses username/password or token authentication
- **Timeout handling**: Configurable query timeouts (default: 5 minutes)
- **Error handling**: Comprehensive error reporting

## Migration from Azure Synapse

If you're migrating from Azure Synapse, the main changes are:

1. **Configuration**: Update your config files to use the `databricks` section instead of `synapse`
2. **Connection method**: The helper now uses HTTP API instead of direct database connections
3. **Query execution**: Same `executeQuery` method, but now targets Databricks

## Troubleshooting

### Common Issues

1. **Connection timeout**: Increase the timeout in the configuration if queries take longer than 5 minutes
2. **Authentication errors**: Verify your username/password or access token
3. **Warehouse not found**: Ensure the httpPath points to a valid SQL warehouse
4. **Invalid JDBC URL**: Check the format of your JDBC URL

### Logging

The helper provides detailed logging for connection and query execution. Check your application logs for:
- Connection establishment messages
- Query execution details
- Error messages with specific failure reasons

## Security Notes

- Store credentials securely (consider using environment variables)
- Use access tokens instead of passwords when possible
- Ensure your Databricks workspace has proper network security configured
- Regularly rotate access credentials
