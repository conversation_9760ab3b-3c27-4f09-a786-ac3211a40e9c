import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { <PERSON>Array, IsNotEmpty, IsOptional } from 'class-validator';
import { DATE } from 'sequelize';

export class ModelResponseDto {
	@ApiProperty({ type: Number })
	@Expose()
	entity_id: number;

	@ApiProperty({ type: String })
	@Expose()
	public entity_code: string | null;

	@ApiProperty({ type: Object })
	@Expose()
	public summary_tabular_kpis: JSON;

	@ApiProperty({ type: Object })
	@Expose()
	public summary_chart_kpis: JSON;

	@ApiProperty({ type: Object })
	@Expose()
	public override_fetch_mechanism_type: string | null;

	@ApiProperty({ type: Object })
	@Expose()
	public pull_from_date: Date | null;

	@ApiProperty({ type: DATE })
	@Expose()
	public pull_to_date: Date | null;
}
