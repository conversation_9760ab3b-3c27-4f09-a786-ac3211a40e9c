import { RuleExpressionCondition } from '../types';

/**
 * Recursive function to convert rule expression to postgres json query string
 * @param rule 
 * @param jsonColumn 
 * @returns 
 */
export const convertRuleExpressionToSqlQuery = (rule: RuleExpressionCondition, jsonColumn: string): string => {
    if (rule.fact) {
        // Handle fact rule
        const fact = rule.fact;
        const value = rule.value;
        const operator = rule.operator;
        const path = rule.path || fact;

        let condition = `${jsonColumn}->>'${path}'`;

        if (operator === 'equal') {
            condition += ` = '${value}'`;
        } else if (operator === 'notEqual') {
            condition += ` != '${value}'`;
        } else if (operator === 'in') {
            condition += ` IN (${value.map((v: any) => `'${v}'`).join(', ')})`;
        } else if (operator === 'notIn') {
            condition += ` NOT IN (${value.map((v: any) => `'${v}'`).join(', ')})`;
        } else if (operator === 'greaterThan') {
            condition += ` > '${value}'`;
        } else if (operator === 'lessThan') {
            condition += ` < '${value}'`;
        } else if (operator === 'greaterThanInclusive') {
            condition += ` >= '${value}'`;
        } else if (operator === 'lessThanInclusive') {
            condition += ` <= '${value}'`;
        } else if (operator === 'contains') {
            condition += ` @> '[${value}]'`;
        }

        return condition;
    } else if (rule.all) {
        // Handle all rule
        const conditions = rule.all.map((r: any) => convertRuleExpressionToSqlQuery(r, jsonColumn)).join(' AND ');
        return `(${conditions})`;
    } else if (rule.any) {
        // Handle any rule
        const conditions = rule.any.map((r: any) => convertRuleExpressionToSqlQuery(r, jsonColumn)).join(' OR ');
        return `(${conditions})`;
    }
}