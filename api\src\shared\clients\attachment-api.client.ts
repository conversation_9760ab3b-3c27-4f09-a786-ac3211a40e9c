import { Inject, Injectable } from '@nestjs/common';
import { INTERNAL_API } from '../constants';
import { HttpService } from '../services/http.service';
import {
	GetAttachmentFileByIdResponse,
	Entity,
	GetAttachmentResponse,
	GetContentFileByIdResponse,
	NewAttachmentRequest,
	UpdateAttachmentRequest,
	BulkUploadResponse,
	MoveAttachmentsRequest,
	MoveAttachmentsResponse,
} from '../types';

@Injectable()
export class AttachmentApiClient {
	constructor(
		@Inject(INTERNAL_API.REQUEST_API_PROVIDER) private readonly attachmentApi: HttpService,
	) {}

	/**
	 * @parms entity_id: number, entity_type: string, entity_section: string = null
	 * Get all uploaded attachments with entity id and entity type.
	 * @returns all the attachments: GetAttachmentResponse[]
	 */
	public async getAllAttachments(
		entity_id: number,
		entity_type: string,
		entity_section: string = null,
	): Promise<GetAttachmentResponse[]> {
		const requestData: Entity = { entity_id, entity_type };
		if (entity_section) {
			requestData.entity_section = entity_section;
		}
		const { data } = await this.attachmentApi.post('/attachment/getattachments', requestData);
		return data;
	}

	/**
	 * Upload bulk attachments.
	 * @params NewAttachmentRequest
	 * @returns file_id: string
	 */
	public async addBulkAttachments(
		newAttachmentRequests: NewAttachmentRequest[],
	): Promise<BulkUploadResponse[]> {
		const { data } = await this.attachmentApi.post('/attachment/addBulk', {
			att_data: newAttachmentRequests,
		});
		return data;
	}

	/**
	 * Add new attachments.
	 * @params NewAttachmentRequest
	 * @returns file_id: string
	 */
	public async addNewAttachments(newAttachmentRequest: NewAttachmentRequest): Promise<string> {
		const requestData: NewAttachmentRequest = { ...newAttachmentRequest };
		const { data } = await this.attachmentApi.post('/attachment/add', requestData);
		return data;
	}

	/**
	 * Update existing attachment metadata.
	 * @params UpdateAttachmentRequest
	 * @returns null
	 */
	public async updateAttachments(updateAttachmentRequest: UpdateAttachmentRequest): Promise<null> {
		const requestData: UpdateAttachmentRequest = { ...updateAttachmentRequest };
		const { data } = await this.attachmentApi.post('/attachment/update', requestData);
		return data;
	}

	/**
	 * Get conent by file id.
	 * @params file_id: FileId
	 * @returns null
	 */
	public async getContentByFileId(file_id: string): Promise<GetContentFileByIdResponse> {
		const { data } = await this.attachmentApi.post('/attachment/getcontentbyfileid', { file_id });
		return data;
	}

	/**
	 * Get attachment by file id.
	 * @params file_id: FileId
	 * @returns null
	 */
	public async getAttachmentByFileId(file_id: string): Promise<GetAttachmentFileByIdResponse> {
		const { data } = await this.attachmentApi.post('/attachment/getattchmentbyfileid', { file_id });
		return data;
	}

	/**
	 * Delete attachment by file id.
	 * @params file_id: FileId
	 * @returns null
	 */
	public async deleteAttachmentByFileId(file_id: string): Promise<null> {
		const { data } = await this.attachmentApi.post('/attachment/deleteattachmentbyfileid', {
			file_id,
		});
		return data;
	}

	/**
	 * Move attachments from source to different destination.
	 * @param payload
	 * @returns
	 */
	public async moveAttachments(payload: MoveAttachmentsRequest): Promise<MoveAttachmentsResponse> {
		const { data } = await this.attachmentApi.post('/attachment/moveEntity', payload);
		return data;
	}
}
