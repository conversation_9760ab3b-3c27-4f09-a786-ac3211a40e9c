import { Global, Module } from '@nestjs/common';
import {
	AdminApiClient,
	AttachmentApiClient,
	NotificationApiClient,
	TaskApiClient,
	MSGraphApiClient,
	RequestApiClient,
	HistoryApiClient,
} from './clients';
import { SequlizeOperator, BAPortalConnectionHelper, DatalakeConnectionHelper } from './helpers';
import { DatabaseHelper } from './helpers/database.helper';
import { EntityService, SharedPermissionService, SharedAttachmentService, ExcelSheetService } from './services';
import { SharedNotificationService } from './services/shared-notification.service';
import { WorkflowYearValidator } from './validators';

const repositories = [
	SequlizeOperator,
];
@Global()
@Module({
	providers: [
		DatabaseHelper,
		DatalakeConnectionHelper,
		BAPortalConnectionHelper,
		AdminApiClient,
		AttachmentApiClient,
		EntityService,
		MSGraphApiClient,
		SharedAttachmentService,
		TaskApiClient,
		NotificationApiClient,
		RequestApiClient,
		HistoryApiClient,
		SharedPermissionService,
		SharedNotificationService,
		ExcelSheetService,
		WorkflowYearValidator,
		...repositories
	],
	exports: [
		DatabaseHelper,
		DatalakeConnectionHelper,
		BAPortalConnectionHelper,
		AdminApiClient,
		AttachmentApiClient,
		EntityService,
		MSGraphApiClient,
		SharedAttachmentService,
		TaskApiClient,
		NotificationApiClient,
		RequestApiClient,
		HistoryApiClient,
		SharedPermissionService,
		SharedNotificationService,
		ExcelSheetService,
		WorkflowYearValidator,
		...repositories
	],
})
export class SharedModule { }
