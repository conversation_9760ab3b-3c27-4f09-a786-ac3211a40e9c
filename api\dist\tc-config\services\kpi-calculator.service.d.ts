import { KpiConfig } from '../models/kpi_config.model';
import { KpiDataRepository, KpiConfigRepository, BuSetupRepository } from '../repositories';
export declare class KpiCalculatorService {
    private readonly kpiConfigRepository;
    private readonly kpiDataRepository;
    private readonly buSetupRepository;
    constructor(kpiConfigRepository: KpiConfigRepository, kpiDataRepository: KpiDataRepository, buSetupRepository: BuSetupRepository);
    saveKpiValues(entityId: number, month: number, kpiValues: Record<string, any>, userId: string): Promise<void>;
    getAllKpisAndValues(business_entity_id: number, inputValues?: Record<string, any>, overrideFetchMechanism?: string, startDate?: Date, endDate?: Date): Promise<Record<string, any>>;
    getKpiDataBySystemCodes(business_entity_id: number, systemCodes: string[], inputValues?: Record<string, any>, overrideFetchMechanism?: string, startDate?: Date, endDate?: Date): Promise<any[]>;
    getKpisByTags(business_entity_id: number, tags: string[], year: number, inputValues?: Record<string, any>): Promise<Record<string, any>>;
    private findFormulaDependencies;
    private calculateKpiValues;
    private calculateInputKpiValue;
    private calculatePullDataKpiValue;
    private detectCircularDependencies;
    private calculateFormulaKpis;
    private roundKpiValue;
    private aggregateKpiValues;
    private getKpiDataForFetchMechanism;
    findAllDependencies(kpiCodes: string[]): Promise<Set<string>>;
    filterKpisByExcludedEquipment(kpiConfigs: KpiConfig[], business_entity_id: number): Promise<KpiConfig[]>;
}
