import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsArray, IsNotEmpty, IsOptional } from 'class-validator';
import { NUMBER } from 'sequelize';

export class BuSetupRequestDto {
	@ApiProperty({ name: 'entity_id', type: Number })
	@Expose({ name: 'entity_id' })
	@IsNotEmpty()
	public entity_id: number;

	@ApiProperty({ name: 'entity_code', type: String })
	@Expose({ name: 'entity_code' })
	@IsNotEmpty()
	public entity_code: string | null;

	@ApiProperty({ name: 'excluded_equipment_type', type: Object })
	@Expose({ name: 'excluded_equipment_type' })
	@IsOptional()
	public excluded_equipment_type: JSON;

	@ApiProperty({ name: 'excluded_kpi', type: Object })
	@Expose({ name: 'excluded_kpi' })
	@IsOptional()
	public excluded_kpi: JSON | null;
}
