import { Inject, Injectable } from '@nestjs/common';
import { toNumber } from 'lodash';
import { INTERNAL_API } from '../constants';
import { HttpService } from '../services/http.service';
import {
	BusinessEntity,
	BusinessEntityWithChildIds,
	BusinessEntityWithChildren,
	NotificationTemplateResponse,
	PermissionsResponse,
	User,
	UserRoles,
} from '../types';

@Injectable()
export class AdminApiClient {
	constructor(@Inject(INTERNAL_API.ADMIN_API_PROVIDER) private readonly adminApi: HttpService) { }

	/**
	 * Get details of business entity by its id.
	 * @returns all the business hierarchy
	*/
	public async getBusinessEntityDetailsById(entityId: number): Promise<BusinessEntity> {
		const { data } = await this.adminApi.get(`/businessentity/getbyId/${entityId}`);
		return data;
	}

	/**
	 * Get full business hierarchy.
	 * @returns all the business hierarchy
	 */
	public async getAllBusinessHierarchy(): Promise<BusinessEntityWithChildren> {
		const { data } = await this.adminApi.get('/setup/getBusinessEntitiesHeirarchy');
		return data;
	}

	/**
	 * Get the list of user permissions
	 * @param username
	 * @returns
	 */
	public async getListOfUserPermissions(username: string): Promise<PermissionsResponse[]> {
		const { data } = await this.adminApi.post('/user/getuserpermissions', { user_name: username });
		return data;
	}

	/**
	 * Get the roles of business entity level by entity level name.
	 * @param entityLevel
	 * @returns
	 */
	public async getBusinessEntityRoles(entityLevel: string) {
		const { data } = await this.adminApi.post('/role/getrolesbylevel', { level_name: entityLevel });
		return data;
	}

	/**
	 * Get list of all the childern ids of the busniess entity
	 * @param entityId
	 * @returns
	 */
	public async getChildernListOfBusinessEntity(entityId: number): Promise<number[]> {
		const { data } = await this.adminApi.post('/businessentity/getallchildfromparent', {
			parent_id: entityId,
		});
		return data;
	}

	/**
	 * Get all the locations or business entities hierarchy by permission and user
	 * @param username
	 * @param permission
	 * @returns
	 */
	public async getAllBusinessHierarchyByUserAndPermission(
		username: string,
		permission: string,
		parentId: number,
		lastLevel: string
	) {
		const { data } = await this.adminApi.post('/businessentity/getallbusinessentity', {
			user_name: username,
			permission,
			parent_id: parentId,
			last_level: lastLevel
		});
		return data;
	}

	/**
	 * Get full path (parents hierarchy) of a given entity id from the complete business entities Heirarchy
	 * @param entityId
	 * @returns
	 */
	public async getParentIdsOfEntity(entityId: number): Promise<number[]> {
		const entities = await this.getAllBusinessHierarchy();
		//NOTE: Here entityId changed to string from number becuase API returning the entity id as string.
		return this.getPath([entities], `${entityId}`)?.map(e => Number(e.id)) || [];
	}

	/**
	 * Get full path (parents hierarchy) with complete entity object of a given
	 * entity id from the complete business entities Heirarchy.
	 * @param entityId
	 * @returns
	 */
	public async getParentsOfEntity(entityId: number): Promise<BusinessEntityWithChildren[]> {
		const entities = await this.getAllBusinessHierarchy();
		return this.getPath([entities], `${entityId}`) || [];
	}

	/**
	 * Recursively get full path (parents hierarchy) of a given node in a nested object
	 * @param entities
	 * @param entityId
	 * @returns
	 */
	public getPath(entities, entityId: string): BusinessEntityWithChildren[] {
		for (let i = 0; i < entities.length; i++) {
			const entity = entities[i];
			if (entity.id !== entityId) {
				if (entity.children) {
					const path = this.getPath(entity.children, entityId);
					if (path) {
						path.unshift(entity);
						return path;
					}
				}
			} else {
				return [entity];
			}
		}
	}

	/**
	 * Return navigation bar menu by its position
	 * @param position
	 * @returns
	 */
	public async getNavBarByPosition(position: string): Promise<Record<string, any>> {
		const { data } = await this.adminApi.post('/nav/getNavigation', { nav_type: position });
		return data;
	}

	/**
	 * Check if the user has given permission with Entity Id.
	 * @param username
	 * @param permission
	 * @param entityId
	 * @returns
	 */
	public async hasPermissionToUser(
		username: string,
		permission: string,
		entityId?: number,
	): Promise<boolean> {

		let requestParam: { user_name: string; permission: string; business_entity_id?: number } = {
			user_name: username,
			permission,
		};
		if (toNumber(entityId)) {
			requestParam = { ...requestParam, business_entity_id: toNumber(entityId) };
		}

		const { data: response } = await this.adminApi.post('/user/hasuserpermission', requestParam);
		return !!response;
	}

	/**
	 * Check if the user has given role with Entity Id.
	 * @param username
	 * @param role
	 * @param entityId
	 * @returns
	 */
	public async hasUserRole(
		username: string,
		role: string,
		entityId?: number,
	): Promise<boolean> {
		let requestParam: { user_name: string; role: string; business_entity_id?: number } = {
			user_name: username,
			role,
		};
		if (entityId) {
			requestParam = { ...requestParam, business_entity_id: entityId };
		}
		const { data: response } = await this.adminApi.post('/user/hasuserrole', requestParam);
		return !!response;
	}

	/**
	 * Get all roles of the user with Login Id.
	 * @param loginId
	 * @returns
	 */
	public async getUserRoles(
		loginId: string,
	): Promise<UserRoles[]> {
		const { data: response } = await this.adminApi.post('/user/getuserroles', {
			user_name: loginId
		});
		return response;
	}


	/**
	 * Return all the levels of the business entity hierarchy
	 */
	public async getBusinessEntityLevels(): Promise<Record<string, any>> {
		const { data } = await this.adminApi.get('/businessentity/getbusinessentitylevels');
		return data;
	}

	/**
	 * Return the parent entity details of an entity of given level.
	 * @param entityId
	 * @param level
	 * @returns
	 */
	public async getParentEntityOfAnEntityOfGivenLevel(
		entityId: number,
		level: string,
	): Promise<BusinessEntityWithChildIds> {
		const { data } = await this.adminApi.post('/businessentity/getparentbytype', {
			business_entity_id: entityId,
			type: level,
		});
		return data;
	}

	/**
	 * Return users list of a given role of an entity.
	 * @param role
	 * @param entityId
	 * @returns
	 */
	public async getUsersByRoleOfAnEntity(role: string, entityId: number): Promise<User[]> {
		const { data } = await this.adminApi.post('/role/getroleusers', {
			role_name: role,
			business_entity_id: entityId,
		});
		return data;
	}

	/**
	 * Return array of locations where user has given permission.
	 * @param username
	 * @param permission
	 * @returns
	 */
	public async getBusinessEntitiesByUserAndPermission(
		username: string,
		permission?: string,
	): Promise<BusinessEntityWithChildIds[]> {
		const { data } = await this.adminApi.post('/businessentity/getbusinessentityallchild', {
			user_name: username,
			permission: permission,
		});
		return data;
	}

	/**
	 * Return the email tempplate details.
	 * @param templateName 
	 * @returns 
	 */
	public async getNotificationTemplate(templateName: string): Promise<NotificationTemplateResponse> {
		const { data } = await this.adminApi.post('/notification/getNotificationTemplate', { system_name: templateName });
		return data;
	}
}
