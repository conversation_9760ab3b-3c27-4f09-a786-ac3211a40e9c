import { Injectable } from '@nestjs/common';
import { Pagination } from 'src/core/pagination';
import { multiObjectToInstance, singleObjectToInstance } from 'src/shared/helpers';
import { CurrentContext, NotificationPayload } from 'src/shared/types';
import { BuSetupRepository, KpiConfigRepository, KpiDataRepository } from '../repositories';
import { KpiCalculatorService } from './kpi-calculator.service';
import { KpiConfigResponseDto, KpiDataResponseDto } from '../dtos';
import { ModelRepository } from 'src/tc-submit/repositories';

@Injectable()
export class TcConfigService {
	constructor(
		private readonly buSetupRepository: BuSetupRepository,
		private readonly kpiConfigRepository: KpiConfigRepository,
		private readonly kpiDataRepository: KpiDataRepository,
		private readonly kpiCalculatorService: KpiCalculatorService,
        private readonly modelRepository: ModelRepository,
	) {}

	/**
	 * Get all KPI configurations
	 */
	public async getAllKpiConfigs(model_id: number): Promise<KpiConfigResponseDto[]> {
		let kpiConfigs = await this.kpiConfigRepository.findAll();

        var model  = await this.modelRepository.findById(model_id);

        if (!model) {
			throw new Error(`Model with ID ${model_id} not found`);
		}

        kpiConfigs = await this.kpiCalculatorService.filterKpisByExcludedEquipment(kpiConfigs, model.entity_id);
		return multiObjectToInstance(KpiConfigResponseDto, kpiConfigs);
	}

	public async getKpiConfigByCode(
		business_entity_id: number,
		systemCodes: string[],
		year: number,
		inputValues: Record<string, any> = {},
	): Promise<any> {
		return this.kpiCalculatorService.getKpiDataBySystemCodes(
			business_entity_id,
			systemCodes,
			inputValues,
		);
	}

	/**
	 * Get KPI configuration by system code
	 */
	public async getAllKpisAndValues(
		business_entity_id: number,
		inputValues: Record<string, any> = {},
		overrideFetchMechanism?: string,
		startDate?: Date,
		endDate?: Date,
	): Promise<any> {
		return await this.kpiCalculatorService.getAllKpisAndValues(
			business_entity_id,
			inputValues,
			overrideFetchMechanism,
			startDate,
			endDate,
		);
	}

	/**
	 * Get KPIs filtered by tags
	 * @param entityId The entity ID
	 * @param tags Array of tags to filter KPIs
	 * @param year The year for data retrieval
	 * @param month Optional month for data retrieval
	 * @param inputValues Input values provided by the user
	 * @returns Object with KPI values for the filtered KPIs
	 */
	public async getKpisByTags(
		entityId: number,
		tags: string[],
		year: number,
		inputValues: Record<string, any> = {},
	): Promise<Record<string, any>> {
		return this.kpiCalculatorService.getKpisByTags(entityId, tags, year, inputValues);
	}

	/**
	 * Get KPI configurations with metadata filtered by tags
	 * @param tags Array of tags to filter KPIs
	 * @returns Array of KPI configurations matching the tags
	 */
	public async getKpiConfigsByTags(tags: string[]): Promise<KpiConfigResponseDto[]> {
		const allKpiConfigs = await this.kpiConfigRepository.findAll();

		// Filter KPIs by tags
		const filteredKpiConfigs = allKpiConfigs.filter(kpi => {
			const kpiTags = Array.isArray(kpi.tags)
				? kpi.tags
				: typeof kpi.tags === 'string'
				? JSON.parse(kpi.tags as string)
				: [];

			return tags.some(tag => kpiTags.includes(tag));
		});

		return multiObjectToInstance(KpiConfigResponseDto, filteredKpiConfigs);
	}
}
