import { Module } from '@nestjs/common';
import { SchedulerService } from './services';
import { SchedulerController } from './controllers';
import { QueueLogRepository } from 'src/queue/repositories';
import { SyncLogRepository } from './repositories';
import { AdminApiClient, MSGraphApiClient, NotificationApiClient } from 'src/shared/clients';
import { SequlizeOperator } from 'src/shared/helpers';
import { SharedNotificationService } from 'src/shared/services';
import { BuSetupRepository, KpiDataRepository } from 'src/tc-config/repositories';
import { ConfigModule } from 'src/config/config.module';

@Module({
  imports: [
    ConfigModule,
  ],
  controllers: [SchedulerController],
  providers: [
    SchedulerService,
    SyncLogRepository,
    QueueLogRepository,
    AdminApiClient,
    MSGraphApiClient,
    NotificationApiClient,
    SharedNotificationService,
    KpiDataRepository,
    SequlizeOperator,
    BuSetupRepository
  ],
  exports: [
    SchedulerService,
  ]
})
export class SchedulerModule { }
