import { TASK_ACTION } from '../enums'

export const NOTIFICATION_TITLES = {
    AFE_SUBMIT: {
        SUBMITTER: 'You have submitted the AFE {afeReferenceNumber}.',
        SUBSCRIBER: 'AFE {afeReferenceNumber} has been submitted by {submitterName}.'
    },
    AFE_RE_SUBMIT: {
        SUBMITTER: 'You have resubmitted the AFE {oldAfeReferenceNumber} with new AFE {newAfeReferenceNumber}.',
        SUBSCRIBER: 'AFE {oldAfeReferenceNumber} has been resubmitted by {submitterName} with new AFE {newAfeReferenceNumber}.'
    },
    AFE_APPROVAL_ACTION: {
        [TASK_ACTION.APPROVE]: {
            SUBMITTER: 'Your AFE {afeReferenceNumber} has been approved by {approverName}.',
             SUBSCRIBER: 'AFE {afeReferenceNumber} has been approved by {approverName}.',
            APPROVER: 'You have approved the AFE {afeReferenceNumber}.'
        },
        [TASK_ACTION.DELEGATE]: {
            SUBMITTER: 'Your AFE {afeReferenceNumber} has been delegated to {delegateeName} by {approverName}.',
            DELEGATEE: 'AFE {afeReferenceNumber} has been delegated to you by {approverName}',
            SUBSCRIBER: 'AFE {afeReferenceNumber} has been delegated to {delegateeName} by {approverName}.',
            APPROVER: 'You have delegated the AFE {afeReferenceNumber} to {delegateeName}.'
        },
        [TASK_ACTION.DISCARD]: {
            SUBSCRIBER: 'AFE {afeReferenceNumber} has been discarded by {approverName}.',
            APPROVER: 'You have discarded the AFE {afeReferenceNumber}.'
        },
        [TASK_ACTION.MORE_DETAIL]: {
            SUBMITTER: '{approverName} has asked more details for AFE {afeReferenceNumber} from {delegateeName}.',
            DELEGATEE: '{approverName} has asked more details for AFE {afeReferenceNumber} from you.',
            SUBSCRIBER: '{approverName} has asked more details for AFE {afeReferenceNumber} from {delegateeName}.',
            APPROVER: 'You asked more details for AFE {afeReferenceNumber} from {delegateeName} .'
        },
        [TASK_ACTION.MORE_DETAIL_SUBMITTED]: {
            SUBMITTER: '{approverName} has submitted the more details for your AFE {afeReferenceNumber}.',
            SUBSCRIBER: '{approverName} has submitted the more details for AFE {afeReferenceNumber}.',
            APPROVER: 'You have submitted the more details for AFE {afeReferenceNumber}.'
        },
        [TASK_ACTION.REASSIGNE]: {
            SUBMITTER: '{approverName} has reassigned your AFE {afeReferenceNumber} to {assigneeName}.',
            DELEGATEE: '{approverName} has reassigned the AFE {afeReferenceNumber} to you.',
            SUBSCRIBER: '{approverName} has reassigned the AFE {afeReferenceNumber} to {assigneeName}.',
            APPROVER: 'You have reassigned the AFE {afeReferenceNumber} to {assigneeName}.'
        },
        [TASK_ACTION.REJECT]: {
            SUBMITTER: 'Your AFE {afeReferenceNumber} has been rejected by {approverName}.',
            SUBSCRIBER: 'AFE {afeReferenceNumber} has been rejected by {approverName}.',
            APPROVER: 'You have rejected the AFE {afeReferenceNumber}.'
        },
        [TASK_ACTION.SEND_BACK]: {
            SUBMITTER: 'Your AFE {afeReferenceNumber} has been sent back to you by {approverName}.',
            SUBSCRIBER: 'AFE {afeReferenceNumber} has been sent back to {submitterName} by {approverName}.',
            APPROVER: 'You have sent back the AFE {afeReferenceNumber} to {submitterName}.'
        },
    },
    AFE_TASK_ASSIGNMENT: 'Approval task has been assigned to you for AFE {afeReferenceNumber}.'
}

export const NOTIFICATION_URLS = {
    AFE_DETAILS_URL: '/afe/afe-detail/{afeId}',
    AFE_TASK_URL: '/mytask/task/{afeId}?taskId={taskId}'
}