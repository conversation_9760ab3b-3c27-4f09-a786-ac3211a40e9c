import { PaginationResultInterface } from './pagination-results.interface';

export class Pagination<PaginationEntity> {
	public records: PaginationEntity[];
	public pageTotal: number;
	public total: number;

	constructor(paginationResults: PaginationResultInterface<PaginationEntity>) {
		this.records = paginationResults.records;
		this.pageTotal = paginationResults.records.length;
		this.total = paginationResults.total;
	}
}
