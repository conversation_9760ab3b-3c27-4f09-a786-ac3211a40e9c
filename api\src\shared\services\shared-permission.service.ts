import { Injectable } from '@nestjs/common';
import { AdminApiClient } from '../clients';
import { PermissionsResponse } from '../types';

@Injectable()
export class SharedPermissionService {
	constructor(private readonly adminApiClient: AdminApiClient) {}

	/**
	 * Get location ids for an given permission for the user.
	 * @param username
	 * @param permission
	 * @returns
	 */
	public async getAllLocationIdForGivenPermission(
		username: string,
		permission: string,
	): Promise<number[] | null> {
		const response = await this.adminApiClient.getListOfUserPermissions(username);
		const locations = response.find(
			(p: PermissionsResponse) => p.permissionName === permission,
		)?.locations.map(Number);
		return locations || null;
	}
}
