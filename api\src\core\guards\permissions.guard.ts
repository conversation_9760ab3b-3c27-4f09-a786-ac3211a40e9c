import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AdminApiClient } from 'src/shared/clients';
import { SharedPermissionService } from 'src/shared/services';

@Injectable()
export class PermissionsGuard implements CanActivate {
	constructor(
		private readonly reflector: Reflector,
		private readonly adminApiClient: AdminApiClient,
		private readonly permissionService: SharedPermissionService,
	) {}

	async canActivate(context: ExecutionContext): Promise<boolean> {
		const routePermission = this.reflector.get<{
			permission: string;
			checkEntity?: boolean;
			checkReader?: boolean;
		}>('permissions', context.getHandler());

		let request = context.switchToHttp().getRequest();
		const { body, user, query } = request;
		const { permission, checkEntity } = routePermission;
		let entityId = null;
		/** check if we are getting entityId in the request object and send as a
		 * parameter to check if user as a permission on that entity for the given operation
		 **/
		if (checkEntity && (body?.entityId || query?.entityId)) {
			entityId = body?.entityId || query?.entityId;
		}

		/**
		 * Attach the list of locations in the current context
		 * where user has the given permission.
		 */
		const locations = await this.permissionService.getAllLocationIdForGivenPermission(
			user.unique_name,
			permission,
		);

		request.user.locations = locations || [];
		return this.adminApiClient.hasPermissionToUser(user.unique_name, permission, entityId);
	}
}
