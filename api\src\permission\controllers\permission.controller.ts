import { Controller, Get, Req, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { RequestContext } from 'src/shared/types';
import { UserPermissionResponseDto } from '../dtos';
import { PermissionService } from '../services';

@ApiTags('Permission APIs')
@ApiBearerAuth()
@UseGuards(AuthGuard('oauth-bearer'))
@Controller('permissions')
export class PermissionController {
	constructor(public readonly permissionService: PermissionService) {
	}

	@ApiResponse({
		status: 200,
		description: 'Get list of permissions with locations for the user.',
		type: [UserPermissionResponseDto],
	})
	@Get('/user')
	public getListOfUserPermissions(
		@Req() request: RequestContext,
	): Promise<UserPermissionResponseDto[]> {
		return this.permissionService.getListOfUserPermissions(request.currentContext);
	}
}
