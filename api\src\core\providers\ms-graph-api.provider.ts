import * as msal from '@azure/msal-node';
import { ConfigService } from 'src/config/config.service';
import { MS_GRAPH_API } from 'src/shared/constants';
import { HttpService } from 'src/shared/services';

export const MSGraphProviders = [
	{
		provide: MS_GRAPH_API.MS_GRAPH_API_PROVIDER,
		useFactory: async (configService: ConfigService) => {
			/**
			 * Configuration object to be passed to MSAL instance on creation.
			 * For a full list of MSAL Node configuration parameters, visit:
			 * https://github.com/AzureAD/microsoft-authentication-library-for-js/blob/dev/lib/msal-node/docs/configuration.md
			 */
			const { azureAD } = configService.getAppConfig();
			const { clientId, authority, tennantId, clientSecret } = azureAD;
			const msalConfig = {
				auth: {
					clientId: clientId,
					authority: `${authority}/${tennantId}`,
					clientSecret: clientSecret,
				},
			};

			/**
			 * Initialize a confidential client application.
			 */
			return new msal.ConfidentialClientApplication(msalConfig);
		},
		inject: [ConfigService],
	},
	{
		provide: MS_GRAPH_API.MS_GRAPH_HTTP_SERVICE_PROVIDER,
		useFactory: (configService: ConfigService) => {
			const { azureAD } = configService.getAppConfig();
			const { graphApiUrl, graphApiVersion } = azureAD;
			const url = `${graphApiUrl}/${graphApiVersion}`;
			return new HttpService().withBaseUrl(url);
		},
		inject: [ConfigService],
	},
];
