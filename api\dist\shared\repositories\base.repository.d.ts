import { FindOptions, Model, ModelStatic } from 'sequelize';
import { IBaseRepo } from '../interfaces';
import { CurrentContext, SearchOptions, MutationParameters, FindParameters } from '../types';
export declare abstract class BaseRepository<M extends Model> implements IBaseRepo<M> {
    private model;
    private options;
    constructor(model: ModelStatic<M>, options?: {
        useSoftDelete: boolean;
    });
    save(model: M, currentContext: CurrentContext, params?: MutationParameters): Promise<M | null>;
    findById(id: number, params?: FindParameters): Promise<M | null>;
    findByIds(ids: number[], params?: FindParameters): Promise<M[] | null>;
    findOne(findOptions: any, params?: FindParameters): Promise<M | null>;
    findAll(findOptions?: any, params?: FindParameters): Promise<M[] | null | any>;
    findAndCountAll(findOptions?: any, params?: FindParameters): Promise<{
        rows: M[];
        count: number;
    } | null>;
    insertMany(records: any[], currentContext: CurrentContext, options?: any, params?: MutationParameters): Promise<M[]>;
    update(values: any, currentContext: CurrentContext, options: any, params?: MutationParameters): Promise<number | null>;
    upsert(values: any, currentContext: CurrentContext, options?: any, params?: MutationParameters): Promise<[M, boolean | null]>;
    upsertBySystem(values: any, options?: any, params?: MutationParameters): Promise<[M, boolean | null]>;
    private _upsertInternal;
    private _buildWhereCondition;
    updateWithoutUser(values: any, options: any, params?: MutationParameters): Promise<number | null>;
    deleteByCondition(condition: any, currentContext: CurrentContext, params?: MutationParameters): Promise<boolean>;
    hardDeleteByCondition(condition: any): Promise<any>;
    deleteById(id: number, currentContext: CurrentContext, params?: MutationParameters): Promise<boolean>;
    deleteByIds(ids: number[], currentContext: CurrentContext, params?: MutationParameters): Promise<boolean>;
    count(filter: any): Promise<number>;
    isRecordExist(findOptions: any, params?: FindParameters): Promise<boolean>;
    increment(col: string, incrementBy: number, condition: any, params?: FindParameters): Promise<any>;
    max(col: string, condition: any, params?: FindParameters): Promise<number>;
    search(condition?: any, options?: SearchOptions): Promise<M[]>;
    executeQuery(query: string, replacements: any): Promise<any[]>;
    findAllRaw(options?: FindOptions<any>): Promise<any[]>;
    findOneRaw(options?: FindOptions<any>): Promise<any | null>;
}
