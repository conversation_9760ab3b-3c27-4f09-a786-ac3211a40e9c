import { Module } from '@nestjs/common';
import { BuSetupRepository, KpiConfigRepository, KpiDataRepository } from './repositories';
import { TcConfigService } from './services';
import { TcConfigController } from './controllers';
import { KpiCalculatorService } from './services/kpi-calculator.service';
import { SequlizeOperator } from 'src/shared/helpers';
import { ModelRepository } from 'src/tc-submit/repositories';

const repositories = [BuSetupRepository, KpiConfigRepository, KpiDataRepository, ModelRepository];

@Module({
	providers: [TcConfigService, KpiCalculatorService, SequlizeOperator, ...repositories],
	controllers: [TcConfigController],
})
export class TcConfigModule {}
