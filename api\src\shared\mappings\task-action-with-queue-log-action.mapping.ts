import { QUEUE_LOG_ACTION, TASK_ACTION } from '../enums';

export const TASK_ACTION_WITH_QUEUE_LOG_ACTION_MAPPING = {
    [TASK_ACTION.APPROVE]: QUEUE_LOG_ACTION.APPROVED,
    [TASK_ACTION.AUTO_APPROVE]: QUEUE_LOG_ACTION.AUTO_APPROVED,
    [TASK_ACTION.DELEGATE]: QUEUE_LOG_ACTION.DELEGATED,
    [TASK_ACTION.DISCARD]: QUEUE_LOG_ACTION.DISCARDED,
    [TASK_ACTION.MORE_DETAIL]: QUEUE_LOG_ACTION.MORE_DETAIL,
    [TASK_ACTION.MORE_DETAIL_SUBMITTED]: QUEUE_LOG_ACTION.MORE_DETAIL_SUBMITTED,
    [TASK_ACTION.REASSIGNE]: QUEUE_LOG_ACTION.REASSIGNED,
    [TASK_ACTION.REJECT]: QUEUE_LOG_ACTION.REJECTED,
    [TASK_ACTION.RESUBMIT]: QUEUE_LOG_ACTION.RESUBMITED,
    [TASK_ACTION.SEND_BACK]: QUEUE_LOG_ACTION.SEND_BACK,
};
