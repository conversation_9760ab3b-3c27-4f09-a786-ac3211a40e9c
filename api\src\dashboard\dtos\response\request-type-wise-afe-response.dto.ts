import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class RequestTypeWiseAfeResponseDto {

	@ApiProperty({ name: 'requestTypeId' })
	@Expose({ name: 'requestTypeId' })
	public afe_request_type_id: number;

    @ApiProperty()
	@Expose()
	public totalCount: number;

    @ApiProperty()
	@Expose()
	public month: number;

	constructor(partial: Partial<RequestTypeWiseAfeResponseDto> = {}) {
		Object.assign(this, partial);
	}
}
