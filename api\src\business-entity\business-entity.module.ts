import { Module } from '@nestjs/common';
import { AdminApiClient } from 'src/shared/clients';
import { BusinessEntityController } from './controllers';
import { BusinessEntityService } from './services';

import { SequlizeOperator } from 'src/shared/helpers';

const repositories = [
	
]
@Module({
	controllers: [BusinessEntityController],
	providers: [BusinessEntityService, AdminApiClient, SequlizeOperator, ...repositories],
})
export class BusinessEntityModule {}
