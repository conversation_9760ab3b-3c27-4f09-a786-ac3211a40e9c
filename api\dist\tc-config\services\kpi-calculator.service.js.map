{"version": 3, "file": "kpi-calculator.service.js", "sourceRoot": "", "sources": ["../../../src/tc-config/services/kpi-calculator.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA,2CAA4C;AAG5C,0FAGiD;AACjD,kDAA4F;AAC5F,4FAAiF;AACjF,yCAA0C;AAKnC,IAAM,oBAAoB,GAA1B,MAAM,oBAAoB;IAChC,YACkB,mBAAwC,EACxC,iBAAoC,EACpC,iBAAoC;QAFpC,wBAAmB,GAAnB,mBAAmB,CAAqB;QACxC,sBAAiB,GAAjB,iBAAiB,CAAmB;QACpC,sBAAiB,GAAjB,iBAAiB,CAAmB;IACnD,CAAC;IAUS,aAAa,CACzB,QAAgB,EAChB,KAAa,EACb,SAA8B,EAC9B,MAAc;;YAEd,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAE7C,KAAK,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,IAAI,UAAU,EAAE;gBAC7C,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,SAAS;oBAAE,SAAS;gBAG1D,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;oBACzD,KAAK,EAAE;wBACN,QAAQ,EAAE,OAAO;wBACjB,SAAS,EAAE,KAAK;qBAChB;iBACD,CAAC,CAAC;gBAEH,IAAI,YAAY,EAAE;iBAMjB;qBAAM;iBAUN;aACD;QACF,CAAC;KAAA;IAEY,mBAAmB,CAC/B,kBAA0B,EAC1B,cAAmC,EAAE,EACrC,sBAA+B,EAC/B,SAAgB,EAChB,OAAc;;YAGd,IAAI,aAAa,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,CAAC;YAG5D,aAAa,GAAG,MAAM,IAAI,CAAC,6BAA6B,CAAC,aAAa,EAAE,kBAAkB,CAAC,CAAC;YAG5F,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAC9C,aAAa,EACb,kBAAkB,EAClB,WAAW,EACX,sBAAsB,EACtB,SAAS,EACT,OAAO,CACP,CAAC;YAEF,MAAM,WAAW,GAAG,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YAG9D,MAAM,MAAM,GAAwB,EAAE,CAAC;YACvC,KAAK,MAAM,OAAO,IAAI,WAAW,EAAE;gBAClC,MAAM,CAAC,OAAO,CAAC,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC;aACrC;YAED,OAAO,MAAM,CAAC;QACf,CAAC;KAAA;IAEY,uBAAuB,CACnC,kBAA0B,EAC1B,WAAqB,EACrB,cAAmC,EAAE,EACrC,sBAA+B,EAC/B,SAAgB,EAChB,OAAc;;YAGd,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,CAAC;YAG9D,MAAM,mBAAmB,GAAG,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC;YAE/F,IAAI,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE;gBACrC,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;aAChE;YAGD,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;YAGrE,MAAM,kBAAkB,GAAG,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC;YAG9F,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAC9C,kBAAkB,EAClB,kBAAkB,EAClB,WAAW,EACX,sBAAsB,EACtB,SAAS,EACT,OAAO,CACP,CAAC;YAGF,MAAM,MAAM,GAAU,EAAE,CAAC;YAEzB,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE;gBACrC,MAAM,SAAS,GAAG,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,KAAK,UAAU,CAAC,CAAC;gBAElF,IAAI,CAAC,SAAS;oBAAE,SAAS;gBAGzB,MAAM,eAAe,GAAG,IAAI,GAAG,CAAS,CAAC,UAAU,CAAC,CAAC,CAAC;gBACtD,IAAI,SAAS,CAAC,QAAQ,KAAK,SAAS,EAAE;oBACrC,MAAM,YAAY,GACjB,OAAO,SAAS,CAAC,UAAU,KAAK,QAAQ;wBACvC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,UAAoB,CAAC;wBAC5C,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC;oBAEzB,MAAM,OAAO,GAAG,CAAA,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,OAAO,KAAI,EAAE,CAAC;oBAC5C,MAAM,kBAAkB,GAAG,IAAA,8DAA+B,EAAC,OAAO,CAAC,CAAC;oBAEpE,kBAAkB,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;iBACpE;gBAaD,MAAM,CAAC,IAAI,CAAC;oBACX,QAAQ,EAAE,UAAU;oBACpB,UAAU,EAAE,SAAS;oBACrB,aAAa,EAAE,SAAS,CAAC,UAAU,CAAC;oBAEpC,YAAY,EAAE,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,UAAU,CAAC;oBAC7E,iBAAiB,EAAE,MAAM,CAAC,WAAW,CACpC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,MAAM,CAC/B,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG,KAAK,UAAU,CACzD,CACD;iBACD,CAAC,CAAC;aACH;YAED,OAAO,MAAM,CAAC;QACf,CAAC;KAAA;IAEY,aAAa,CACzB,kBAA0B,EAC1B,IAAc,EACd,IAAY,EACZ,cAAmC,EAAE;;YAGrC,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,CAAC;YAG9D,MAAM,kBAAkB,GAAG,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;gBACrD,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC;oBACtC,CAAC,CAAC,GAAG,CAAC,IAAI;oBACV,CAAC,CAAC,OAAO,GAAG,CAAC,IAAI,KAAK,QAAQ;wBAC9B,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAc,CAAC;wBAChC,CAAC,CAAC,EAAE,CAAC;gBAEN,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC;YAEH,MAAM,gBAAgB,GAAG,kBAAkB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YACxE,MAAM,iBAAiB,GAAG,IAAI,GAAG,EAAU,CAAC;YAG5C,MAAM,WAAW,GAAG,kBAAkB,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC;YACjF,KAAK,MAAM,GAAG,IAAI,WAAW,EAAE;gBAC9B,IAAI,CAAC,uBAAuB,CAAC,GAAG,EAAE,aAAa,EAAE,iBAAiB,CAAC,CAAC;aACpE;YAGD,MAAM,sBAAsB,GAAG,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,MAAM,CAClE,IAAI,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,CACxC,CAAC;YAEF,MAAM,mBAAmB,GAAG,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CACtD,sBAAsB,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,CAChD,CAAC;YAEF,MAAM,kBAAkB,GAAG,CAAC,GAAG,kBAAkB,EAAE,GAAG,mBAAmB,CAAC,CAAC;YAG3E,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAC9C,kBAAkB,EAClB,kBAAkB,EAClB,WAAW,CACX,CAAC;YAGF,MAAM,MAAM,GAAwB,EAAE,CAAC;YACvC,KAAK,MAAM,OAAO,IAAI,gBAAgB,EAAE;gBACvC,MAAM,CAAC,OAAO,CAAC,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC;aACrC;YAED,OAAO,MAAM,CAAC;QACf,CAAC;KAAA;IAGO,uBAAuB,CAC9B,SAAoB,EACpB,aAA0B,EAC1B,aAA0B;QAE1B,MAAM,YAAY,GACjB,OAAO,SAAS,CAAC,UAAU,KAAK,QAAQ;YACvC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,UAAoB,CAAC;YAC5C,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC;QAEzB,MAAM,OAAO,GAAG,CAAA,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,OAAO,KAAI,EAAE,CAAC;QAC5C,MAAM,YAAY,GAAG,IAAA,8DAA+B,EAAC,OAAO,CAAC,CAAC;QAG9D,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;QAG5D,IAAI,oBAAoB,GAAG,IAAI,CAAC;QAChC,OAAO,oBAAoB,EAAE;YAC5B,oBAAoB,GAAG,KAAK,CAAC;YAE7B,KAAK,MAAM,OAAO,IAAI,aAAa,EAAE;gBACpC,MAAM,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,KAAK,OAAO,CAAC,CAAC;gBAEzE,IAAI,SAAS,IAAI,SAAS,CAAC,QAAQ,KAAK,SAAS,EAAE;oBAClD,MAAM,YAAY,GACjB,OAAO,SAAS,CAAC,UAAU,KAAK,QAAQ;wBACvC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,UAAoB,CAAC;wBAC5C,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC;oBAEzB,MAAM,UAAU,GAAG,CAAA,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,OAAO,KAAI,EAAE,CAAC;oBAC/C,MAAM,UAAU,GAAG,IAAA,8DAA+B,EAAC,UAAU,CAAC,CAAC;oBAE/D,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE;wBACnC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;4BAClC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;4BAC7B,oBAAoB,GAAG,IAAI,CAAC;yBAC5B;qBACD;iBACD;aACD;SACD;IACF,CAAC;IASa,kBAAkB,CAC/B,UAAuB,EACvB,kBAA0B,EAC1B,cAAmC,EAAE,EACrC,sBAA+B,EAC/B,SAAgB,EAChB,OAAc;;YAEd,MAAM,SAAS,GAAwB,EAAE,CAAC;YAG1C,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC;YACrE,KAAK,MAAM,GAAG,IAAI,SAAS,EAAE;gBAC5B,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;aACjF;YAGD,MAAM,YAAY,GAAG,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,KAAK,WAAW,CAAC,CAAC;YAC5E,KAAK,MAAM,GAAG,IAAI,YAAY,EAAE;gBAC/B,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAChE,GAAG,EACH,kBAAkB,EAClB,sBAAsB,EACtB,SAAS,EACT,OAAO,CACP,CAAC;aACF;YAGD,MAAM,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC;YACzE,MAAM,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,kBAAkB,EAAE,SAAS,CAAC,CAAC;YAE5E,OAAO,SAAS,CAAC;QAClB,CAAC;KAAA;IAEa,sBAAsB,CACnC,GAAc,EACd,WAAgC;;YAEhC,IAAI,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YAEzC,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;gBAClE,OAAO,CAAC,IAAI,CAAC,yBAAyB,GAAG,CAAC,WAAW,KAAK,KAAK,EAAE,CAAC,CAAC;gBACnE,OAAO,CAAC,CAAC,CAAC;aACV;YAED,OAAO,KAAK,CAAC;QACd,CAAC;KAAA;IAEa,yBAAyB,CACtC,GAAc,EACd,kBAA0B,EAC1B,sBAA+B,EAC/B,SAAgB,EAChB,OAAc;;YAGd,MAAM,YAAY,GACjB,OAAO,GAAG,CAAC,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAoB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC;YAG5F,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,2BAA2B,CACnD,GAAG,CAAC,WAAW,EACf,kBAAkB,EAClB,YAAY,EACZ,sBAAsB,EACtB,SAAS,EACT,OAAO,CACP,CAAC;YAGF,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QACvC,CAAC;KAAA;IAOO,0BAA0B,CAAC,UAAuB;QAKzD,MAAM,aAAa,GAAG,IAAI,GAAG,EAAuB,CAAC;QAGrD,KAAK,MAAM,GAAG,IAAI,UAAU,EAAE;YAC7B,IAAI,GAAG,CAAC,QAAQ,KAAK,SAAS,EAAE;gBAC/B,MAAM,YAAY,GACjB,OAAO,GAAG,CAAC,UAAU,KAAK,QAAQ;oBACjC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAoB,CAAC;oBACtC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC;gBAEnB,MAAM,OAAO,GAAG,CAAA,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,OAAO,KAAI,EAAE,CAAC;gBAC5C,MAAM,YAAY,GAAG,IAAA,8DAA+B,EAAC,OAAO,CAAC,CAAC;gBAE9D,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;aAC1D;iBAAM;gBACN,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;aAC9C;SACD;QAGD,MAAM,oBAAoB,GAAG,IAAI,GAAG,EAAU,CAAC;QAC/C,MAAM,OAAO,GAAG,IAAI,GAAG,EAAU,CAAC;QAClC,MAAM,cAAc,GAAG,IAAI,GAAG,EAAU,CAAC;QAEzC,MAAM,GAAG,GAAG,CAAC,OAAe,EAAE,OAAiB,EAAE,EAAW,EAAE;YAE7D,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;gBACzD,OAAO,KAAK,CAAC;aACb;YAGD,IAAI,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;gBAEhC,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBACzC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBACrD,OAAO,CAAC,IAAI,CAAC,iCAAiC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBAGpE,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;gBACtD,OAAO,IAAI,CAAC;aACZ;YAGD,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACrB,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC5B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAGnB,MAAM,YAAY,GAAG,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC;YAC7D,IAAI,WAAW,GAAG,KAAK,CAAC;YAExB,KAAK,MAAM,GAAG,IAAI,YAAY,EAAE;gBAC/B,IAAI,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE;oBACxB,WAAW,GAAG,IAAI,CAAC;iBAEnB;aACD;YAGD,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAE/B,OAAO,WAAW,CAAC;QACpB,CAAC,CAAC;QAGF,KAAK,MAAM,GAAG,IAAI,UAAU,EAAE;YAC7B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;gBAClC,GAAG,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;aACrB;SACD;QAED,OAAO,EAAE,aAAa,EAAE,oBAAoB,EAAE,CAAC;IAChD,CAAC;IAQa,oBAAoB,CACjC,WAAwB,EACxB,kBAA0B,EAC1B,SAA8B;;YAE9B,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,CAAC;YAG9D,MAAM,EAAE,aAAa,EAAE,oBAAoB,EAAE,GAAG,IAAI,CAAC,0BAA0B,CAAC,aAAa,CAAC,CAAC;YAG/F,IAAI,oBAAoB,CAAC,IAAI,GAAG,CAAC,EAAE;gBAClC,OAAO,CAAC,IAAI,CACX,SAAS,oBAAoB,CAAC,IAAI,qCAAqC,KAAK,CAAC,IAAI,CAChF,oBAAoB,CACpB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CACd,CAAC;aACF;YAGD,MAAM,cAAc,GAAG,IAAI,GAAG,CAC7B,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,SAAS,CAAC,CAClE,CAAC;YAGF,MAAM,WAAW,GAAG,IAAI,GAAG,CAAS,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC;YAG7E,MAAM,KAAK,GAAG,IAAI,GAAG,EAAoB,CAAC;YAC1C,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAkB,CAAC;YAG3C,KAAK,MAAM,OAAO,IAAI,WAAW,EAAE;gBAClC,MAAM,YAAY,GAAa,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;gBACnF,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;gBAGjC,MAAM,mBAAmB,GAAG,YAAY,CAAC,MAAM,CAC9C,GAAG,CAAC,EAAE,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,CACvD,CAAC;gBAEF,QAAQ,CAAC,GAAG,CAAC,OAAO,EAAE,mBAAmB,CAAC,MAAM,CAAC,CAAC;aAClD;YAGD,KAAK,MAAM,OAAO,IAAI,WAAW,EAAE;gBAClC,IAAI,oBAAoB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;oBACtC,MAAM,GAAG,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,KAAK,OAAO,CAAC,CAAC;oBAC7D,IAAI,CAAC,GAAG;wBAAE,SAAS;oBAEnB,OAAO,CAAC,IAAI,CAAC,yCAAyC,OAAO,yBAAyB,CAAC,CAAC;oBAGxF,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;oBACxB,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;oBAC5B,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;oBAG5B,KAAK,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,IAAI,KAAK,CAAC,OAAO,EAAE,EAAE;wBACxD,IAAI,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;4BAC9D,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;yBACrD;qBACD;oBAED,OAAO,CAAC,IAAI,CAAC,wCAAwC,OAAO,0BAA0B,CAAC,CAAC;iBACxF;aACD;YAGD,MAAM,KAAK,GAAa,EAAE,CAAC;YAC3B,KAAK,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,IAAI,QAAQ,CAAC,OAAO,EAAE,EAAE;gBACnD,IAAI,MAAM,KAAK,CAAC,IAAI,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;oBAC7C,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;iBACpB;aACD;YAGD,IAAI,UAAU,GAAG,CAAC,CAAC;YACnB,MAAM,aAAa,GAAG,WAAW,CAAC,IAAI,GAAG,CAAC,CAAC;YAE3C,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,WAAW,CAAC,IAAI,GAAG,CAAC,IAAI,UAAU,GAAG,aAAa,EAAE;gBAC9E,UAAU,EAAE,CAAC;gBAEb,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;gBAC9B,IAAI,CAAC,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC;oBAAE,SAAS;gBAEpD,MAAM,GAAG,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,KAAK,OAAO,CAAC,CAAC;gBAC7D,IAAI,CAAC,GAAG;oBAAE,SAAS;gBAGnB,MAAM,eAAe,GAAG,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;gBAC5E,MAAM,yBAAyB,GAAG,eAAe,CAAC,KAAK,CACtD,CAAC,OAAe,EAAE,EAAE,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,SAAS,CAAC,OAAO,CAAC,KAAK,SAAS,CACpF,CAAC;gBAEF,IAAI,yBAAyB,EAAE;oBAE9B,MAAM,YAAY,GACjB,OAAO,GAAG,CAAC,UAAU,KAAK,QAAQ;wBACjC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAoB,CAAC;wBACtC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC;oBAEnB,MAAM,OAAO,GAAG,CAAA,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,OAAO,KAAI,EAAE,CAAC;oBAG5C,OAAO,CAAC,KAAK,CAAC,+BAA+B,OAAO,KAAK,OAAO,EAAE,CAAC,CAAC;oBACpE,OAAO,CAAC,KAAK,CACZ,wBAAwB,OAAO,GAAG,EAClC,MAAM,CAAC,WAAW,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAC5E,CAAC;oBAGF,IAAI,MAAM,GAAG,IAAA,iDAAkB,EAAC,OAAO,EAAE,SAAS,CAAC,CAAC;oBAGpD,IAAI,MAAM,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE;wBACnC,OAAO,CAAC,IAAI,CAAC,qCAAqC,OAAO,2BAA2B,CAAC,CAAC;wBACtF,MAAM,GAAG,CAAC,CAAC,CAAC;qBACZ;oBAGD,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;oBAEzC,OAAO,CAAC,KAAK,CAAC,4BAA4B,OAAO,KAAK,MAAM,EAAE,CAAC,CAAC;oBAGhE,SAAS,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC;oBAC5B,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;oBAC5B,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;oBAI5B,KAAK,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,IAAI,KAAK,CAAC,OAAO,EAAE,EAAE;wBACxD,IAAI,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;4BAC9D,MAAM,SAAS,GAAG,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;4BAC9C,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;4BAEnC,IAAI,SAAS,KAAK,CAAC,IAAI,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;gCAClD,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;6BACtB;yBACD;qBACD;iBACD;qBAAM;oBAEN,MAAM,mBAAmB,GAAG,eAAe,CAAC,MAAM,CACjD,CAAC,OAAY,EAAE,EAAE,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,SAAS,CAAC,OAAO,CAAC,KAAK,SAAS,CAClF,CAAC;oBAEF,OAAO,CAAC,KAAK,CACZ,OAAO,OAAO,iCAAiC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAC/E,CAAC;oBAGF,MAAM,mBAAmB,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;oBAE3F,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE;wBAInC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;qBACpB;yBAAM;wBAGN,OAAO,CAAC,IAAI,CACX,OAAO,OAAO,uCAAuC,mBAAmB,CAAC,IAAI,CAC5E,IAAI,CACJ,2BAA2B,CAC5B,CAAC;wBACF,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;wBACxB,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;wBAC5B,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;wBAG5B,KAAK,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,IAAI,KAAK,CAAC,OAAO,EAAE,EAAE;4BACxD,IAAI,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;gCAC9D,MAAM,SAAS,GAAG,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;gCAC9C,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;gCAEnC,IAAI,SAAS,KAAK,CAAC,IAAI,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;oCAClD,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;iCACtB;6BACD;yBACD;qBACD;iBACD;aACD;YAGD,IAAI,UAAU,IAAI,aAAa,IAAI,WAAW,CAAC,IAAI,GAAG,CAAC,EAAE;gBACxD,OAAO,CAAC,IAAI,CACX,+BAA+B,aAAa,4BAA4B;oBACvE,uBAAuB,WAAW,CAAC,IAAI,UAAU,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CACtF,CAAC;gBAGF,KAAK,MAAM,OAAO,IAAI,WAAW,EAAE;oBAClC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;iBACxB;aACD;QACF,CAAC;KAAA;IAQO,aAAa,CAAC,KAAU,EAAE,GAAc;QAE/C,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;YAClE,OAAO,CAAC,IAAI,CAAC,yBAAyB,GAAG,CAAC,WAAW,KAAK,KAAK,EAAE,CAAC,CAAC;YACnE,OAAO,CAAC,CAAC,CAAC;SACV;QAGD,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QAE/B,IAAI;YAEH,MAAM,SAAS,GACd,OAAO,GAAG,CAAC,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAoB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC;YAE5F,MAAM,aAAa,GAClB,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,cAAc,MAAK,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAEtF,IAAI,KAAK,CAAC,aAAa,CAAC;gBAAE,OAAO,QAAQ,CAAC;YAG1C,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,aAAa,CAAC,CAAC;YAC3C,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC;SAC9C;QAAC,OAAO,KAAK,EAAE;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAClD,OAAO,QAAQ,CAAC;SAChB;IACF,CAAC;IAQO,kBAAkB,CAAC,MAA8B,EAAE,MAAc;QAExE,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,SAAS,CAAa,CAAC;QAEpE,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAEvC,QAAQ,MAAM,CAAC,WAAW,EAAE,EAAE;YAC7B,KAAK,KAAK;gBACT,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;YAEvD,KAAK,KAAK;gBACT,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC;YAE5E,KAAK,KAAK;gBACT,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC;YAEjC,KAAK,KAAK;gBACT,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC;YAEjC,KAAK,OAAO;gBACX,OAAO,WAAW,CAAC,MAAM,CAAC;YAE3B,KAAK,MAAM;gBACV,OAAO,WAAW,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAE5C,KAAK,OAAO;gBACX,OAAO,WAAW,CAAC,CAAC,CAAC,CAAC;YAEvB;gBACC,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;SACvD;IACF,CAAC;IASa,2BAA2B,CACxC,OAAe,EACf,kBAA0B,EAC1B,SAAc,EACd,sBAA+B,EAC/B,SAAgB,EAChB,OAAc;;YAEd,MAAM,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;YAC7C,MAAM,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;YAG/C,MAAM,eAAe,GAAG,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,gBAAgB,KAAI,KAAK,CAAC;YAC7D,MAAM,sBAAsB,GAAG,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,wBAAwB,KAAI,KAAK,CAAC;YAC5E,MAAM,QAAQ,GAAG,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,SAAS,KAAI,OAAO,CAAC;YACjD,MAAM,UAAU,GAAG,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,UAAU,KAAI,OAAO,CAAC;YAEpD,IAAI,cAAc,GACjB,sBAAsB,IAAI,sBAAsB,KAAK,SAAS;gBAC7D,CAAC,CAAC,sBAAsB;gBACxB,CAAC,CAAC,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,eAAe,KAAI,cAAc,CAAC;YAGjD,MAAM,gBAAgB,GAAG,IAAI,2CAAgB,EAAE,CAAC;YAGhD,IAAI,cAAc,GAAQ;gBACzB,QAAQ,EAAE,UAAU;gBACpB,kBAAkB,EAAE,kBAAkB;aACtC,CAAC;YAEF,QAAQ,cAAc,EAAE;gBACvB,KAAK,eAAe;oBAEnB,MAAM,iBAAiB,GAAG,IAAI,IAAI,EAAE,CAAC;oBACrC,iBAAiB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC;oBAC9D,MAAM,WAAW,GAAG,iBAAiB,CAAC,WAAW,EAAE,CAAC;oBACpD,MAAM,YAAY,GAAG,iBAAiB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;oBAEtD,cAAc,mBACb,QAAQ,EAAE,UAAU,EACpB,kBAAkB,EAAE,kBAAkB,IACnC,gBAAgB,CAAC,UAAU,CAAC;wBAE9B;4BACC,QAAQ,EAAE,gBAAgB,CAAC,eAAe,CAAC,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,CAAC,CAAC;yBAC5E;wBAED;4BACC,QAAQ,EAAE,WAAW;4BACrB,SAAS,EAAE,gBAAgB,CAAC,wBAAwB,CAAC,YAAY,CAAC;yBAClE;wBAED;4BACC,QAAQ,EAAE,WAAW;4BACrB,SAAS,EAAE,gBAAgB,CAAC,qBAAqB,CAAC,YAAY,CAAC;yBAC/D;qBACD,CAAC,CACF,CAAC;oBACF,MAAM;gBAEP,KAAK,cAAc;oBAClB,cAAc,GAAG;wBAChB,QAAQ,EAAE,UAAU;wBACpB,kBAAkB,EAAE,kBAAkB;wBACtC,QAAQ,EAAE,WAAW;wBACrB,SAAS,EAAE,gBAAgB,CAAC,qBAAqB,CAAC,YAAY,CAAC;qBAC/D,CAAC;oBACF,MAAM;gBAEP,KAAK,WAAW;oBACf,cAAc,GAAG;wBAChB,QAAQ,EAAE,UAAU;wBACpB,kBAAkB,EAAE,kBAAkB;wBACtC,QAAQ,EAAE,WAAW,GAAG,CAAC;qBACzB,CAAC;oBACF,MAAM;gBAEP,KAAK,QAAQ;oBACZ,IAAI,CAAC,SAAS,IAAI,CAAC,OAAO,EAAE;wBAC3B,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAC;qBACnF;oBAED,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;oBACpD,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;oBACtD,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;oBAChD,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;oBAElD,IAAI,SAAS,KAAK,OAAO,EAAE;wBAE1B,cAAc,GAAG;4BAChB,QAAQ,EAAE,UAAU;4BACpB,kBAAkB,EAAE,kBAAkB;4BACtC,QAAQ,EAAE,SAAS;4BACnB,SAAS,EAAE,gBAAgB,CAAC,eAAe,CAAC,UAAU,EAAE,QAAQ,CAAC;yBACjE,CAAC;qBACF;yBAAM;wBAEN,cAAc,mBACb,QAAQ,EAAE,UAAU,EACpB,kBAAkB,EAAE,kBAAkB,IACnC,gBAAgB,CAAC,UAAU,CAAC;4BAE9B;gCACC,QAAQ,EAAE,gBAAgB,CAAC,eAAe,CAAC,SAAS,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC,CAAC;6BACtE;4BAED;gCACC,QAAQ,EAAE,SAAS;gCACnB,SAAS,EAAE,gBAAgB,CAAC,wBAAwB,CAAC,UAAU,CAAC;6BAChE;4BAED;gCACC,QAAQ,EAAE,OAAO;gCACjB,SAAS,EAAE,gBAAgB,CAAC,qBAAqB,CAAC,QAAQ,CAAC;6BAC3D;yBACD,CAAC,CACF,CAAC;qBACF;oBACD,MAAM;gBAEP,KAAK,YAAY;oBAEhB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;wBACtD,KAAK,EAAE;4BACN,QAAQ,EAAE,UAAU;4BACpB,kBAAkB,EAAE,kBAAkB;yBACtC;wBACD,KAAK,EAAE;4BACN,CAAC,UAAU,EAAE,MAAM,CAAC;4BACpB,CAAC,WAAW,EAAE,MAAM,CAAC;4BACrB,CAAC,SAAS,EAAE,MAAM,CAAC;yBACnB;qBACD,CAAC,CAAC;oBACH,OAAO,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,SAAS,MAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAE7E;oBACC,cAAc,GAAG;wBAChB,QAAQ,EAAE,UAAU;wBACpB,kBAAkB,EAAE,kBAAkB;wBACtC,QAAQ,EAAE,WAAW;qBACrB,CAAC;aACH;YAGD,IAAI,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK,KAAK,EAAE;gBAC9C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC;oBACtD,UAAU,EAAE,CAAC,CAAC,qBAAS,CAAC,EAAE,CAAC,eAAe,EAAE,qBAAS,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;oBACtF,KAAK,EAAE,cAAc;iBACrB,CAAC,CAAC;gBAEH,OAAO,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,SAAS,MAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACtE;iBAEI,IAAI,QAAQ,KAAK,OAAO,EAAE;gBAC9B,IAAI;oBAEH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC;wBAC3D,UAAU,EAAE;4BACX,WAAW;4BACX,CAAC,qBAAS,CAAC,EAAE,CAAC,sBAAsB,EAAE,qBAAS,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,EAAE,eAAe,CAAC;yBACnF;wBACD,KAAK,EAAE,cAAc;wBACrB,KAAK,EAAE,CAAC,WAAW,CAAC;qBACpB,CAAC,CAAC;oBAGH,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;wBAC7C,OAAO,CAAC,CAAC;qBACT;oBAGD,MAAM,aAAa,GAAG,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;wBAC5C,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC;wBACjC,OAAO,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClE,CAAC,CAAC,CAAC;oBAGH,QAAQ,eAAe,CAAC,WAAW,EAAE,EAAE;wBACtC,KAAK,KAAK;4BACT,OAAO,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;wBACzD,KAAK,KAAK;4BACT,OAAO,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,GAAG,aAAa,CAAC,MAAM,CAAC;wBAChF,KAAK,KAAK;4BACT,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC;wBACnC,KAAK,KAAK;4BACT,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC;wBACnC,KAAK,OAAO;4BACX,OAAO,aAAa,CAAC,MAAM,CAAC;wBAC7B,KAAK,MAAM;4BACV,OAAO,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;wBAChD,KAAK,OAAO;4BACX,OAAO,aAAa,CAAC,CAAC,CAAC,CAAC;wBACzB;4BACC,OAAO,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;qBACzD;iBACD;gBAAC,OAAO,KAAK,EAAE;oBACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;oBACrD,OAAO,CAAC,CAAC;iBACT;aACD;iBAAM;gBACN,OAAO,CAAC,CAAC,CAAC;aACV;QACF,CAAC;KAAA;IAOY,mBAAmB,CAAC,QAAkB;;YAElD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,CAAC;YAG9D,MAAM,gBAAgB,GAAG,IAAI,GAAG,CAAS,QAAQ,CAAC,CAAC;YAGnD,MAAM,aAAa,GAAG,IAAI,GAAG,EAAoB,CAAC;YAGlD,KAAK,MAAM,GAAG,IAAI,aAAa,EAAE;gBAChC,IAAI,GAAG,CAAC,QAAQ,KAAK,SAAS,EAAE;oBAC/B,MAAM,SAAS,GACd,OAAO,GAAG,CAAC,UAAU,KAAK,QAAQ;wBACjC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAoB,CAAC;wBACtC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC;oBAEnB,MAAM,OAAO,GAAG,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,OAAO,KAAI,EAAE,CAAC;oBACzC,MAAM,YAAY,GAAG,IAAA,8DAA+B,EAAC,OAAO,CAAC,CAAC;oBAE9D,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;iBACjD;qBAAM;oBACN,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;iBACvC;aACD;YAGD,MAAM,OAAO,GAAG,IAAI,GAAG,EAAU,CAAC;YAGlC,MAAM,gBAAgB,GAAG,CAAC,OAAe,EAAE,EAAE;gBAE5C,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC;oBAAE,OAAO;gBAGjC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAGrB,MAAM,YAAY,GAAG,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;gBAGtD,KAAK,MAAM,OAAO,IAAI,YAAY,EAAE;oBACnC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;oBAG9B,gBAAgB,CAAC,OAAO,CAAC,CAAC;iBAC1B;YACF,CAAC,CAAC;YAGF,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;gBAC/B,gBAAgB,CAAC,OAAO,CAAC,CAAC;aAC1B;YAED,OAAO,gBAAgB,CAAC;QACzB,CAAC;KAAA;IAEY,6BAA6B,CACzC,UAAuB,EACvB,kBAA0B;;YAG1B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC;gBACvD,KAAK,EAAE,EAAE,SAAS,EAAE,kBAAkB,EAAE;aACxC,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,uBAAuB,EAAE;gBACjD,OAAO,UAAU,CAAC;aAClB;YAED,MAAM,sBAAsB,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,uBAAuB,CAAC;gBAC5E,CAAC,CAAC,OAAO,CAAC,uBAAuB;gBACjC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;YAG/C,OAAO,UAAU,CAAC,MAAM,CAAC,CAAC,GAAc,EAAE,EAAE;gBAC3C,MAAM,iBAAiB,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC;oBACrE,CAAC,CAAC,GAAG,CAAC,yBAAyB;oBAC/B,CAAC,CAAC,IAAI,CAAC,KAAK,CAAE,GAAG,CAAC,yBAAiC,IAAI,IAAI,CAAC,CAAC;gBAG9D,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAC9C,sBAAsB,CAAC,QAAQ,CAAC,aAAa,CAAC,CAC9C,CAAC;YACH,CAAC,CAAC,CAAC;QACJ,CAAC;KAAA;CACD,CAAA;AA//BY,oBAAoB;IADhC,IAAA,mBAAU,GAAE;qCAG2B,kCAAmB;QACrB,gCAAiB;QACjB,gCAAiB;GAJ1C,oBAAoB,CA+/BhC;AA//BY,oDAAoB"}