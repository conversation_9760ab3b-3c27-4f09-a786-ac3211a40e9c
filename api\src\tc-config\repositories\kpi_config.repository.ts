import { Injectable } from '@nestjs/common';
import { literal, Op } from 'sequelize';
import { BaseRepository } from 'src/shared/repositories';
import { CurrentContext, NotificationPayload } from 'src/shared/types';
import { KpiConfig } from '../models';
import { SequlizeOperator } from 'src/shared/helpers';

@Injectable()
export class KpiConfigRepository extends BaseRepository<KpiConfig> {

    constructor(private sequlizeOperator: SequlizeOperator) {
		super(KpiConfig);
	}

	public getAll(): Promise<KpiConfig[] | null> {
		return this.findAll();
	}
    
}