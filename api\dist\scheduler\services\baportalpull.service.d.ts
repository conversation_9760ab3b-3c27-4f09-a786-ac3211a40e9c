import { SyncLogRepository } from '../repositories';
import { KpiDataRepository } from 'src/tc-config/repositories';
import { KpiQueryDto } from '../dtos/kpi-query.dto';
import { BuSetupRepository } from 'src/tc-config/repositories';
import { BAPortalConnectionHelper } from 'src/shared/helpers';
export declare class BAPortalPullService {
    private readonly kpiDataRepository;
    private readonly baportalHelper;
    private readonly buSetupRepository;
    private readonly syncLogRepository;
    private readonly logger;
    private kpiQueries;
    private bu_list;
    constructor(kpiDataRepository: KpiDataRepository, baportalHelper: BAPortalConnectionHelper, buSetupRepository: BuSetupRepository, syncLogRepository: SyncLogRepository);
    runScheduler(): Promise<void>;
    private loadKpiQueries;
    pullDataForAllKpis(): Promise<void>;
    pullDataForKpi(kpiQuery: KpiQueryDto): Promise<number>;
    private processAndSaveResults;
    private formatDateWithoutTimezoneShift;
    runRetryScheduler(): Promise<void>;
    retryFailedKpis(): Promise<void>;
}
