import { ValidationArguments, ValidatorConstraint, ValidatorConstraintInterface } from 'class-validator';

@ValidatorConstraint()
export class WorkflowYearValidator implements ValidatorConstraintInterface {
    public async validate(year: number, _: ValidationArguments) {
        const currentYear = new Date().getFullYear();
        if(year) {
            return year === currentYear - 1 || year === currentYear || year === currentYear + 1;
        }
        return true;
    }
}