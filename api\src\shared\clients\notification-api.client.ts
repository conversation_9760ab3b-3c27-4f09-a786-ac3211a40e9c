import { Inject, Injectable } from '@nestjs/common';
import { INTERNAL_API } from '../constants';
import { HttpService } from '../services/http.service';
import { CancelInviteRequest, SendNotificationRequest } from '../types';

@Injectable()
export class NotificationApiClient {
	constructor(
		@Inject(INTERNAL_API.NOTIFICATION_API_PROVIDER)
		private readonly notificationApiProvider: HttpService,
	) {}

	public async sendNotification(payload: SendNotificationRequest): Promise<null> {
		const { data } = await this.notificationApiProvider.post('/notification/sendQueue', payload);
		return data;
	}

	public async cancelInvite(payload: CancelInviteRequest): Promise<{ result: boolean }> {
		const { data } = await this.notificationApiProvider.post('/notification/cancelInvite', payload);
		return data;
	}
}
