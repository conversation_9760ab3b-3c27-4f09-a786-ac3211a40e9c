"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var DatalakeConnectionHelper_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatalakeConnectionHelper = void 0;
const common_1 = require("@nestjs/common");
const config_service_1 = require("../../config/config.service");
const axios_1 = __importDefault(require("axios"));
let DatalakeConnectionHelper = DatalakeConnectionHelper_1 = class DatalakeConnectionHelper {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(DatalakeConnectionHelper_1.name);
        this.connections = new Map();
        this.initializeConnections();
    }
    initializeConnections() {
        const { sourceConfig } = this.configService.getAppConfig();
        if (!sourceConfig || !sourceConfig.databricks) {
            this.logger.warn('No Databricks configuration found');
            return;
        }
        try {
            const { jdbcUrl, username, password, database, schema } = sourceConfig.databricks;
            const jdbcMatch = jdbcUrl.match(/jdbc:databricks:\/\/([^:\/]+).*httpPath=([^;&]+)/);
            if (!jdbcMatch) {
                throw new Error('Invalid JDBC URL format. Expected format: ******************************************************************************');
            }
            const host = jdbcMatch[1];
            const httpPath = decodeURIComponent(jdbcMatch[2]);
            this.logger.log(`Connecting to Databricks host: ${host}, catalog: ${database}, schema: ${schema}`);
            let authHeader;
            if (username.toLowerCase() === 'token') {
                authHeader = `Bearer ${password}`;
            }
            else {
                authHeader = `Basic ${Buffer.from(`${username}:${password}`).toString('base64')}`;
            }
            const httpClient = axios_1.default.create({
                baseURL: `https://${host}`,
                timeout: 300000,
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': authHeader,
                },
            });
            const connection = {
                jdbcUrl,
                username,
                password,
                httpClient,
                host,
                httpPath,
                database,
                schema,
                isConnected: false,
            };
            this.testConnection(connection)
                .then(() => {
                connection.isConnected = true;
                this.logger.log(`Successfully connected to Databricks via JDBC: ${host}`);
                this.connections.set('default', connection);
            })
                .catch((error) => {
                this.logger.error(`Failed to connect to Databricks: ${error.message}`);
            });
        }
        catch (error) {
            this.logger.error(`Error initializing Databricks connection: ${error.message}`);
        }
    }
    testConnection(connection) {
        var _a, _b, _c, _d;
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const warehouseId = connection.httpPath.split('/').pop();
                if (!warehouseId) {
                    throw new Error('Warehouse ID not found in httpPath');
                }
                this.logger.log(`Testing connection to warehouse: ${warehouseId}`);
                const requestBody = {
                    statement: 'SELECT 1 as test',
                    warehouse_id: warehouseId,
                    wait_timeout: '30s',
                    format: 'JSON_ARRAY',
                    disposition: 'INLINE'
                };
                const response = yield connection.httpClient.post('/api/2.0/sql/statements', requestBody);
                if (response.status !== 200) {
                    this.logger.error(`Connection test HTTP status: ${response.status}`);
                    this.logger.error(`Response: ${JSON.stringify(response.data, null, 2)}`);
                    throw new Error(`Connection test failed with status: ${response.status}`);
                }
                const result = response.data;
                this.logger.log(`Connection test result: ${(_a = result.status) === null || _a === void 0 ? void 0 : _a.state}`);
                if (((_b = result.status) === null || _b === void 0 ? void 0 : _b.state) === 'FAILED') {
                    const errorMsg = ((_c = result.status.error) === null || _c === void 0 ? void 0 : _c.message) || 'Unknown error';
                    this.logger.error(`Connection test failed: ${errorMsg}`);
                    throw new Error(`Connection test failed: ${errorMsg}`);
                }
                if (((_d = result.status) === null || _d === void 0 ? void 0 : _d.state) === 'SUCCEEDED') {
                    this.logger.log('Connection test successful');
                }
            }
            catch (error) {
                if (error.response) {
                    this.logger.error(`HTTP Error ${error.response.status}: ${error.response.statusText}`);
                    this.logger.error(`Response data: ${JSON.stringify(error.response.data, null, 2)}`);
                }
                throw new Error(`Connection test failed: ${error.message}`);
            }
        });
    }
    getConnection(name = 'default') {
        if (!this.connections.has(name)) {
            this.logger.warn(`Databricks connection '${name}' not found`);
            return null;
        }
        return this.connections.get(name) || null;
    }
    executeQuery(query, connectionName = 'default') {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
        return __awaiter(this, void 0, void 0, function* () {
            const connection = this.getConnection(connectionName);
            if (!connection) {
                throw new Error(`Databricks connection '${connectionName}' not found`);
            }
            if (!connection.isConnected) {
                throw new Error(`Databricks connection '${connectionName}' is not connected`);
            }
            try {
                const warehouseId = connection.httpPath.split('/').pop();
                if (!warehouseId) {
                    throw new Error('Warehouse ID not found in httpPath');
                }
                this.logger.log(`Executing query on warehouse: ${warehouseId}, catalog: ${connection.database}, schema: ${connection.schema}`);
                this.logger.log(`Query: ${query.substring(0, 100)}...`);
                const requestBody = {
                    statement: query,
                    warehouse_id: warehouseId,
                    wait_timeout: '50s',
                    on_wait_timeout: 'CANCEL',
                    format: 'JSON_ARRAY',
                    disposition: 'INLINE'
                };
                if (connection.database) {
                    requestBody.catalog = connection.database;
                    this.logger.log(`Using catalog: ${connection.database}`);
                }
                if (connection.schema) {
                    requestBody.schema = connection.schema;
                    this.logger.log(`Using schema: ${connection.schema}`);
                }
                this.logger.log(`Request body: ${JSON.stringify(requestBody, null, 2)}`);
                const response = yield connection.httpClient.post('/api/2.0/sql/statements', requestBody);
                if (response.status !== 200) {
                    this.logger.error(`HTTP Status: ${response.status}`);
                    this.logger.error(`Response: ${JSON.stringify(response.data, null, 2)}`);
                    throw new Error(`Query execution failed with status: ${response.status}`);
                }
                const result = response.data;
                this.logger.log(`Query result status: ${(_a = result.status) === null || _a === void 0 ? void 0 : _a.state}`);
                if (((_b = result.status) === null || _b === void 0 ? void 0 : _b.state) === 'SUCCEEDED') {
                    const data = ((_c = result.result) === null || _c === void 0 ? void 0 : _c.data_array) || [];
                    this.logger.log(`Query returned ${data.length} rows`);
                    return data;
                }
                else if (((_d = result.status) === null || _d === void 0 ? void 0 : _d.state) === 'FAILED') {
                    const errorMsg = ((_e = result.status.error) === null || _e === void 0 ? void 0 : _e.message) || 'Unknown error';
                    this.logger.error(`Query failed: ${errorMsg}`);
                    throw new Error(`Query failed: ${errorMsg}`);
                }
                else if (((_f = result.status) === null || _f === void 0 ? void 0 : _f.state) === 'PENDING' || ((_g = result.status) === null || _g === void 0 ? void 0 : _g.state) === 'RUNNING') {
                    this.logger.warn(`Query is still ${result.status.state}, but wait_timeout should handle this`);
                    throw new Error(`Query timeout or still running: ${(_h = result.status) === null || _h === void 0 ? void 0 : _h.state}`);
                }
                else {
                    this.logger.error(`Unexpected query state: ${(_j = result.status) === null || _j === void 0 ? void 0 : _j.state}`);
                    throw new Error(`Query in unexpected state: ${(_k = result.status) === null || _k === void 0 ? void 0 : _k.state}`);
                }
            }
            catch (error) {
                if (error.response) {
                    this.logger.error(`HTTP Error ${error.response.status}: ${error.response.statusText}`);
                    this.logger.error(`Response data: ${JSON.stringify(error.response.data, null, 2)}`);
                }
                this.logger.error(`Error executing query on Databricks: ${error.message}`);
                throw error;
            }
        });
    }
};
DatalakeConnectionHelper = DatalakeConnectionHelper_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_service_1.ConfigService])
], DatalakeConnectionHelper);
exports.DatalakeConnectionHelper = DatalakeConnectionHelper;
//# sourceMappingURL=datalake-connection.helper.js.map