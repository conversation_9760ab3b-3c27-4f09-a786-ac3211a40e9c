"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var DatalakeConnectionHelper_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatalakeConnectionHelper = void 0;
const common_1 = require("@nestjs/common");
const config_service_1 = require("../../config/config.service");
const axios_1 = __importDefault(require("axios"));
let DatalakeConnectionHelper = DatalakeConnectionHelper_1 = class DatalakeConnectionHelper {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(DatalakeConnectionHelper_1.name);
        this.connections = new Map();
        this.initializeConnections();
    }
    initializeConnections() {
        const { sourceConfig } = this.configService.getAppConfig();
        if (!sourceConfig || !sourceConfig.databricks) {
            this.logger.warn('No Databricks configuration found');
            return;
        }
        try {
            const { jdbcUrl, username, password } = sourceConfig.databricks;
            const jdbcMatch = jdbcUrl.match(/jdbc:databricks:\/\/([^:\/]+).*httpPath=([^;&]+)/);
            if (!jdbcMatch) {
                throw new Error('Invalid JDBC URL format. Expected format: ******************************************************************************');
            }
            const host = jdbcMatch[1];
            const httpPath = decodeURIComponent(jdbcMatch[2]);
            let authHeader;
            if (username.toLowerCase() === 'token') {
                authHeader = `Bearer ${password}`;
            }
            else {
                authHeader = `Basic ${Buffer.from(`${username}:${password}`).toString('base64')}`;
            }
            const httpClient = axios_1.default.create({
                baseURL: `https://${host}`,
                timeout: 300000,
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': authHeader,
                },
            });
            const connection = {
                jdbcUrl,
                username,
                password,
                httpClient,
                host,
                httpPath,
                isConnected: false,
            };
            this.testConnection(connection)
                .then(() => {
                connection.isConnected = true;
                this.logger.log(`Successfully connected to Databricks via JDBC: ${host}`);
                this.connections.set('default', connection);
            })
                .catch((error) => {
                this.logger.error(`Failed to connect to Databricks: ${error.message}`);
            });
        }
        catch (error) {
            this.logger.error(`Error initializing Databricks connection: ${error.message}`);
        }
    }
    testConnection(connection) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const warehouseId = connection.httpPath.split('/').pop();
                if (!warehouseId) {
                    throw new Error('Warehouse ID not found in httpPath');
                }
                const response = yield connection.httpClient.post('/api/2.0/sql/statements', {
                    statement: 'SELECT 1 as test',
                    warehouse_id: warehouseId,
                    wait_timeout: '10s',
                });
                if (response.status !== 200) {
                    throw new Error(`Connection test failed with status: ${response.status}`);
                }
                const result = response.data;
                if (((_a = result.status) === null || _a === void 0 ? void 0 : _a.state) === 'FAILED') {
                    throw new Error(`Connection test failed: ${((_b = result.status.error) === null || _b === void 0 ? void 0 : _b.message) || 'Unknown error'}`);
                }
            }
            catch (error) {
                throw new Error(`Connection test failed: ${error.message}`);
            }
        });
    }
    getConnection(name = 'default') {
        if (!this.connections.has(name)) {
            this.logger.warn(`Databricks connection '${name}' not found`);
            return null;
        }
        return this.connections.get(name) || null;
    }
    executeQuery(query, connectionName = 'default') {
        var _a, _b, _c, _d, _e;
        return __awaiter(this, void 0, void 0, function* () {
            const connection = this.getConnection(connectionName);
            if (!connection) {
                throw new Error(`Databricks connection '${connectionName}' not found`);
            }
            if (!connection.isConnected) {
                throw new Error(`Databricks connection '${connectionName}' is not connected`);
            }
            try {
                const warehouseId = connection.httpPath.split('/').pop();
                if (!warehouseId) {
                    throw new Error('Warehouse ID not found in httpPath');
                }
                const response = yield connection.httpClient.post('/api/2.0/sql/statements', {
                    statement: query,
                    warehouse_id: warehouseId,
                    wait_timeout: '300s',
                    on_wait_timeout: 'CANCEL',
                });
                if (response.status !== 200) {
                    throw new Error(`Query execution failed with status: ${response.status}`);
                }
                const result = response.data;
                if (((_a = result.status) === null || _a === void 0 ? void 0 : _a.state) === 'SUCCEEDED') {
                    return ((_b = result.result) === null || _b === void 0 ? void 0 : _b.data_array) || [];
                }
                else if (((_c = result.status) === null || _c === void 0 ? void 0 : _c.state) === 'FAILED') {
                    throw new Error(`Query failed: ${((_d = result.status.error) === null || _d === void 0 ? void 0 : _d.message) || 'Unknown error'}`);
                }
                else {
                    throw new Error(`Query in unexpected state: ${(_e = result.status) === null || _e === void 0 ? void 0 : _e.state}`);
                }
            }
            catch (error) {
                this.logger.error(`Error executing query on Databricks: ${error.message}`);
                throw error;
            }
        });
    }
};
DatalakeConnectionHelper = DatalakeConnectionHelper_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_service_1.ConfigService])
], DatalakeConnectionHelper);
exports.DatalakeConnectionHelper = DatalakeConnectionHelper;
//# sourceMappingURL=datalake-connection.helper.js.map