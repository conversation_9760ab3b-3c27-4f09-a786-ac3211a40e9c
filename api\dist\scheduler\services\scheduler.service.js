"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __asyncValues = (this && this.__asyncValues) || function (o) {
    if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
    var m = o[Symbol.asyncIterator], i;
    return m ? m.call(o) : (o = typeof __values === "function" ? __values(o) : o[Symbol.iterator](), i = {}, verb("next"), verb("throw"), verb("return"), i[Symbol.asyncIterator] = function () { return this; }, i);
    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }
    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }
};
var SchedulerService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchedulerService = void 0;
const common_1 = require("@nestjs/common");
const repositories_1 = require("../repositories");
const repositories_2 = require("../../tc-config/repositories");
const helpers_1 = require("../../shared/helpers");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const repositories_3 = require("../../tc-config/repositories");
const _ = __importStar(require("lodash"));
const constants_1 = require("../../shared/constants");
const models_1 = require("../models");
let SchedulerService = SchedulerService_1 = class SchedulerService {
    constructor(kpiDataRepository, datalakeHelper, buSetupRepository, syncLogRepository) {
        this.kpiDataRepository = kpiDataRepository;
        this.datalakeHelper = datalakeHelper;
        this.buSetupRepository = buSetupRepository;
        this.syncLogRepository = syncLogRepository;
        this.logger = new common_1.Logger(SchedulerService_1.name);
        this.kpiQueries = [];
        this.bu_list = [];
    }
    runScheduler() {
        return __awaiter(this, void 0, void 0, function* () {
            this.logger.log('Starting data pull scheduler');
            try {
                yield this.loadKpiQueries();
                this.logger.log('Data pull completed successfully');
            }
            catch (error) {
                this.logger.error(`Error running data pull scheduler: ${error.message}`);
            }
        });
    }
    loadKpiQueries(is_retry = false) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const filePath = path.resolve(__dirname, '../../shared/files/kpi_pull_queries.json');
                const fileContent = fs.readFileSync(filePath, 'utf8');
                const data = JSON.parse(fileContent);
                if (Array.isArray(data.KPIs)) {
                    this.kpiQueries = data.KPIs;
                    this.logger.log(`Loaded ${this.kpiQueries.length} KPI queries from file`);
                    if (!is_retry) {
                        yield this.pullDataForAllKpis();
                    }
                    else {
                        yield this.retryFailedKpis();
                    }
                }
                else {
                    this.logger.error('Invalid KPI queries file format: KPIs array not found');
                }
            }
            catch (error) {
                this.logger.error(`Error loading KPI queries from file: ${error.message}`);
            }
        });
    }
    pullDataForAllKpis() {
        var _a, e_1, _b, _c;
        return __awaiter(this, void 0, void 0, function* () {
            try {
                this.logger.log(`Found ${this.kpiQueries.length} KPI queries to process`);
                this.bu_list = yield this.buSetupRepository.findAll();
                const today = new Date();
                const startYear = today.getMonth() === 0 ? today.getFullYear() - 1 : today.getFullYear();
                const startMonth = today.getMonth() === 0 ? 11 : today.getMonth() - 1;
                let start_date = this.formatDateWithoutTimezoneShift(new Date(startYear, startMonth, 1));
                let end_date = this.formatDateWithoutTimezoneShift(today);
                const successKpis = [];
                const errorKpis = [];
                let totalDataCount = 0;
                var sunc_log = new models_1.SyncLog();
                sunc_log.start_time = new Date();
                sunc_log.sync_status = 'IN_PROGRESS';
                sunc_log.data_date_range = { start_date: start_date, end_date: end_date };
                sunc_log.total_kpis = this.kpiQueries.length;
                sunc_log.success_kpis = 0;
                sunc_log.error_kpis = 0;
                sunc_log.success_kpi_list = null;
                sunc_log.error_kpi_list = null;
                sunc_log.total_data = 0;
                sunc_log.retry_count = 0;
                let syncLog = yield this.syncLogRepository.createSyncLog(sunc_log, constants_1.SYSTEM_USER);
                try {
                    for (var _d = true, _e = __asyncValues(this.kpiQueries), _f; _f = yield _e.next(), _a = _f.done, !_a;) {
                        _c = _f.value;
                        _d = false;
                        try {
                            const kpiQuery = _c;
                            try {
                                const dataCount = yield this.pullDataForKpi(kpiQuery, start_date, end_date);
                                totalDataCount += dataCount;
                                successKpis.push(kpiQuery.kpi_code);
                            }
                            catch (error) {
                                this.logger.error(`Error pulling data for KPI ${kpiQuery.kpi_code}: ${error.message}`);
                                errorKpis.push({
                                    kpi_code: kpiQuery.kpi_code,
                                    error: error.message,
                                });
                            }
                        }
                        finally {
                            _d = true;
                        }
                    }
                }
                catch (e_1_1) { e_1 = { error: e_1_1 }; }
                finally {
                    try {
                        if (!_d && !_a && (_b = _e.return)) yield _b.call(_e);
                    }
                    finally { if (e_1) throw e_1.error; }
                }
                syncLog = yield this.syncLogRepository.findOne({
                    where: { id: syncLog.id },
                });
                syncLog.total_data = totalDataCount;
                syncLog.success_kpis = successKpis.length;
                syncLog.error_kpis = errorKpis.length;
                syncLog.success_kpi_list = successKpis;
                syncLog.error_kpi_list = errorKpis;
                syncLog.end_time = new Date();
                syncLog.sync_status = errorKpis.length > 0 ? 'COMPLETED_WITH_ERRORS' : 'COMPLETED';
                yield this.syncLogRepository.updateSyncLog(syncLog.id, syncLog, constants_1.SYSTEM_USER);
                this.logger.log(`Data pull completed. Success: ${successKpis.length}, Errors: ${errorKpis.length}, Total data: ${totalDataCount}`);
            }
            catch (error) {
                this.logger.error(`Error pulling data for all KPIs: ${error.message}`);
            }
        });
    }
    pullDataForKpi(kpiQuery, start_date, end_date) {
        return __awaiter(this, void 0, void 0, function* () {
            let { kpi_code, query } = kpiQuery;
            this.logger.log(`Processing KPI: ${kpi_code}`);
            try {
                this.logger.log(`Date range for KPI ${kpi_code}: ${start_date} to ${end_date}`);
                query = query.replace(/@StartDate/g, "'" + start_date + "'");
                query = query.replace(/@EndDate/g, "'" + end_date + "'");
                const results = yield this.datalakeHelper.executeQuery(query);
                this.logger.log(`Retrieved ${results.length} records for KPI: ${kpi_code}`);
                return results.length;
            }
            catch (error) {
                throw new Error(`Error executing query for KPI ${kpi_code}: ${error.message}`);
            }
        });
    }
    processAndSaveResults(kpiCode, results, start_date, end_date) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!results || results.length === 0) {
                this.logger.log(`No results to process for KPI: ${kpiCode}`);
                return;
            }
            try {
                const startYear = start_date.split('-')[0];
                const startMonth = start_date.split('-')[1];
                const endYear = end_date.split('-')[0];
                const endMonth = end_date.split('-')[1];
                this.logger.log(`Deleting existing data for KPI: ${kpiCode} from ${startYear}-${startMonth} to ${endYear}-${endMonth}`);
                var record_count = yield this.kpiDataRepository.deleteAllKpiCodeByDateRange(kpiCode, startYear, endYear, startMonth, endMonth);
                this.logger.log(`Successfully deleted ${record_count} existing data for KPI: ${kpiCode}`);
            }
            catch (error) {
                this.logger.error(`Error deleting existing data for KPI ${kpiCode}: ${error.message}`);
            }
            const groupedResults = _.groupBy(results, x => x.Location_Code);
            for (const [locationCode, locationResults] of Object.entries(groupedResults)) {
                if (!locationCode) {
                    this.logger.warn(`Skipping null or undefined Location_Code for KPI: ${kpiCode}`);
                    continue;
                }
                const businessEntity = this.bu_list.find(bu => bu.entity_code === locationCode);
                if (!businessEntity || !businessEntity.entity_id || !businessEntity.entity_code) {
                    this.logger.warn(`Business entity not found for Location_Code: ${locationCode}, skipping ${locationResults.length} records`);
                    continue;
                }
                let kpi_data_list = [];
                for (const result of locationResults) {
                    const { Day, Month, Year, kpi_value } = result;
                    if (Day === undefined || Month === undefined || Year === undefined || kpi_value === undefined) {
                        this.logger.warn(`Skipping record with missing required fields for KPI: ${kpiCode}, Location: ${locationCode}`);
                        continue;
                    }
                    kpi_data_list.push({
                        business_entity_code: businessEntity.entity_code,
                        business_entity_id: businessEntity.entity_id,
                        kpi_code: kpiCode,
                        kpi_day: Day,
                        kpi_month: Month,
                        kpi_year: Year,
                        kpi_value: kpi_value,
                        created_by: 'SYSTEM',
                        updated_by: 'SYSTEM'
                    });
                }
                if (kpi_data_list.length === 0) {
                    this.logger.log(`No valid data to insert for KPI: ${kpiCode}, Location: ${locationCode}`);
                    continue;
                }
                try {
                    const batchSize = 100;
                    for (let i = 0; i < kpi_data_list.length; i += batchSize) {
                        const batch = kpi_data_list.slice(i, i + batchSize);
                        yield this.kpiDataRepository.insertMany(batch, constants_1.SYSTEM_USER);
                    }
                    this.logger.log(`Successfully inserted ${kpi_data_list.length} records for KPI: ${kpiCode}, Location: ${locationCode}`);
                }
                catch (error) {
                    this.logger.error(`Error saving KPI data for ${kpiCode}, Location: ${locationCode}: ${error.message}`);
                }
            }
        });
    }
    formatDateWithoutTimezoneShift(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }
    runRetryScheduler() {
        return __awaiter(this, void 0, void 0, function* () {
            this.logger.log('Starting data pull scheduler');
            try {
                yield this.loadKpiQueries(true);
                this.logger.log('Data pull completed successfully');
            }
            catch (error) {
                this.logger.error(`Error running data pull scheduler: ${error.message}`);
            }
        });
    }
    retryFailedKpis() {
        var _a, e_2, _b, _c;
        var _d;
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const latestSyncLog = yield this.syncLogRepository.findOne({
                    order: [['created_on', 'DESC']],
                });
                if (!latestSyncLog) {
                    this.logger.log('No sync logs found to retry');
                    return;
                }
                if (latestSyncLog.sync_status === 'COMPLETED_WITH_ERRORS' &&
                    latestSyncLog.error_kpis > 0 &&
                    (latestSyncLog.retry_count === null || latestSyncLog.retry_count <= 5)) {
                    this.logger.log(`Retrying failed KPIs from sync log ID: ${latestSyncLog.id}`);
                    this.bu_list = yield this.buSetupRepository.findAll();
                    const failedKpiCodes = latestSyncLog.error_kpi_list.map(error => typeof error === 'string' ? error : error.kpi_code);
                    const kpiQueriesToRetry = this.kpiQueries.filter(query => failedKpiCodes.includes(query.kpi_code));
                    if (kpiQueriesToRetry.length === 0) {
                        this.logger.warn('No matching KPI queries found for retry');
                        latestSyncLog.end_time = new Date();
                        latestSyncLog.sync_status = 'COMPLETED';
                        yield this.syncLogRepository.updateSyncLog(latestSyncLog.id, latestSyncLog, constants_1.SYSTEM_USER);
                        return;
                    }
                    this.logger.log(`Found ${kpiQueriesToRetry.length} KPI queries to retry`);
                    const successKpis = (_d = latestSyncLog.success_kpi_list) !== null && _d !== void 0 ? _d : [];
                    const errorKpis = [];
                    let totalDataCount = latestSyncLog.total_data;
                    try {
                        for (var _e = true, kpiQueriesToRetry_1 = __asyncValues(kpiQueriesToRetry), kpiQueriesToRetry_1_1; kpiQueriesToRetry_1_1 = yield kpiQueriesToRetry_1.next(), _a = kpiQueriesToRetry_1_1.done, !_a;) {
                            _c = kpiQueriesToRetry_1_1.value;
                            _e = false;
                            try {
                                const kpiQuery = _c;
                                try {
                                    const dataCount = yield this.pullDataForKpi(kpiQuery, latestSyncLog.data_date_range.start_date, latestSyncLog.data_date_range.end_date);
                                    totalDataCount += dataCount;
                                    successKpis.push(kpiQuery.kpi_code);
                                }
                                catch (error) {
                                    this.logger.error(`Error retrying KPI ${kpiQuery.kpi_code}: ${error.message}`);
                                    errorKpis.push({
                                        kpi_code: kpiQuery.kpi_code,
                                        error: error.message,
                                    });
                                }
                            }
                            finally {
                                _e = true;
                            }
                        }
                    }
                    catch (e_2_1) { e_2 = { error: e_2_1 }; }
                    finally {
                        try {
                            if (!_e && !_a && (_b = kpiQueriesToRetry_1.return)) yield _b.call(kpiQueriesToRetry_1);
                        }
                        finally { if (e_2) throw e_2.error; }
                    }
                    latestSyncLog.total_data = totalDataCount;
                    latestSyncLog.success_kpis = successKpis.length;
                    latestSyncLog.error_kpis = errorKpis.length;
                    latestSyncLog.success_kpi_list = successKpis;
                    latestSyncLog.error_kpi_list = errorKpis;
                    latestSyncLog.end_time = new Date();
                    latestSyncLog.retry_count = latestSyncLog.retry_count ? latestSyncLog.retry_count + 1 : 1;
                    latestSyncLog.sync_status = errorKpis.length > 0 ? 'COMPLETED_WITH_ERRORS' : 'COMPLETED';
                    yield this.syncLogRepository.updateSyncLog(latestSyncLog.id, latestSyncLog, constants_1.SYSTEM_USER);
                    this.logger.log(`Retry completed. Success: ${successKpis.length}, Errors: ${errorKpis.length}, Total data: ${totalDataCount}`);
                }
                else {
                    if (latestSyncLog.sync_status !== 'COMPLETED_WITH_ERRORS') {
                        this.logger.log(`Latest sync log (ID: ${latestSyncLog.id}) has status: ${latestSyncLog.sync_status}, no retry needed`);
                    }
                    else if (latestSyncLog.retry_count > 5) {
                        this.logger.log(`Latest sync log (ID: ${latestSyncLog.id}) has reached maximum retry count (${latestSyncLog.retry_count})`);
                    }
                    else if (latestSyncLog.error_kpis === 0 ||
                        !latestSyncLog.error_kpi_list ||
                        latestSyncLog.error_kpi_list.length === 0) {
                        this.logger.log(`Latest sync log (ID: ${latestSyncLog.id}) has no failed KPIs to retry`);
                    }
                }
            }
            catch (error) {
                this.logger.error(`Error retrying failed KPIs: ${error.message}`);
            }
        });
    }
};
SchedulerService = SchedulerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [repositories_2.KpiDataRepository,
        helpers_1.DatalakeConnectionHelper,
        repositories_3.BuSetupRepository,
        repositories_1.SyncLogRepository])
], SchedulerService);
exports.SchedulerService = SchedulerService;
//# sourceMappingURL=scheduler.service.js.map