export interface SendNotificationRequest {
	entity_id: number;
	entity_type: string;
	subject: string;
	is_approval_email: boolean;
	approval_task_id?: number;
	receiver?: string;
	cc?: string;
	bcc?: string;
	body?: string;
	additional_data?: any;
	attachments?: AttachmentData[];
	is_calendar_invite?: boolean;
	start_date?: Date;
	end_date?: Date;
	is_teams_meeting?: boolean;
}

export interface AttachmentData {
	name: string;
	data: Buffer;
	relUrl: string;
}

export interface CancelInviteRequest {
	entity_id: number;
	entity_type: string;
	invite_id: string;
	meta_data_1?: string;
	meta_data_2?: string;
	meta_data_3?: string;
	additional_info?: any;
}

export interface NotificationTemplateResponse {
    subject?: string;
    body?: string;
    placeholders?: string[];
}