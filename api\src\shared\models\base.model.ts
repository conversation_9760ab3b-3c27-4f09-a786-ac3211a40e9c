import { Column, CreatedAt, DataType, Model, Sequelize, UpdatedAt } from 'sequelize-typescript';

/**
 *  Base Model which provides common fields, such
 *  as `id`, `createdOn`, `createdBy`, `deleted`, `active`, `updatedOn` and `updatedBy`.
 *  Should be inherited by all entities.
 *
 */
export abstract class BaseModel<T> extends Model<T> {
	@Column({ type: DataType.BIGINT, autoIncrement: true, primaryKey: true })
	public id: number;

	@CreatedAt
	@Column({
		field: 'created_on',
		allowNull: false,
		type: 'TIMESTAMP',
		defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
	})
	public createdOn: Date;

	@Column({ field: 'created_by', type: DataType.STRING, allowNull: false })
	public createdBy: string;

	@UpdatedAt
	@Column({
		field: 'updated_on',
		allowNull: false,
		type: 'TIMESTAMP',
		defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
	})
	public updatedOn: Date;

	@Column({ field: 'updated_by', type: DataType.STRING, allowNull: false })
	public updatedBy: string;

	@Column({ field: 'deleted', type: DataType.BOOLEAN, defaultValue: false, allowNull: false })
	public deleted: boolean;

	@Column({ field: 'active', type: DataType.BOOLEAN, defaultValue: true, allowNull: false })
	public active: boolean;
}
