import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsNotEmpty, IsNumber, IsObject, ValidateNested } from 'class-validator';

export class KpiValuesUpdateDto {
    @ApiProperty({ name: 'model_id', type: Number, description: 'Model ID' })
    @Expose({ name: 'model_id' })
    @IsNotEmpty()
    @IsNumber()
    public model_id: number;

    @ApiProperty({ name: 'scenario_id', type: Number, description: 'Scenario ID' })
    @Expose({ name: 'scenario_id' })
    @IsNotEmpty()
    @IsNumber()
    public scenario_id: number;

    @ApiProperty({ 
        name: 'kpi_values', 
        type: Object, 
        description: 'Object with KPI codes as keys and their values',
        example: { 'KPI_001': 100, 'KPI_002': 200 }
    })
    @Expose({ name: 'kpi_values' })
    @IsNotEmpty()
    @IsObject()
    public kpi_values: Record<string, any>;
}
