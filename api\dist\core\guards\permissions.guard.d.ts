import { CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AdminApiClient } from 'src/shared/clients';
import { SharedPermissionService } from 'src/shared/services';
export declare class PermissionsGuard implements CanActivate {
    private readonly reflector;
    private readonly adminApiClient;
    private readonly permissionService;
    constructor(reflector: Reflector, adminApiClient: AdminApiClient, permissionService: SharedPermissionService);
    canActivate(context: ExecutionContext): Promise<boolean>;
}
