import { TASK_ACTION } from '../enums';

export const TASK_ACTION_WITH_APPROVAL_TYPE_MAPPING = {
    [TASK_ACTION.AUTO_APPROVE]: 'APPROVAL',
    [TASK_ACTION.APPROVE]: 'APPROVAL',
    [TASK_ACTION.DELEGATE]: 'DELEGATE',
    [TASK_ACTION.MORE_DETAIL]: 'MOR<PERSON><PERSON><PERSON><PERSON>',
    [TASK_ACTION.DISCARD]: 'DISCARD',
    [TASK_ACTION.MORE_DETAIL_SUBMITTED]: 'APPROVAL',
    [TASK_ACTION.REASSIGNE]: 'REASSIGN',
    [TASK_ACTION.REJECT]: 'APPROVAL',
    [TASK_ACTION.RESUBMIT]: 'APPROVAL',
    [TASK_ACTION.SEND_BACK]: 'APPROVAL'
};