"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueueLogRepository = void 0;
const common_1 = require("@nestjs/common");
const sequelize_1 = require("sequelize");
const helpers_1 = require("../../shared/helpers");
const repositories_1 = require("../../shared/repositories");
const models_1 = require("../models");
let QueueLogRepository = class QueueLogRepository extends repositories_1.BaseRepository {
    constructor() {
        super(models_1.QueueLog);
    }
    createQueueLogEntry(payload, currentContext) {
        const entity = new models_1.QueueLog(payload);
        return this.save(entity, currentContext);
    }
    getUnprocessedQueueLogs() {
        return this.findAll({ where: { processed: false } });
    }
    getQueueLogsByLogConditionAfterLastRunAt(entityIds, actions, finalApproval, rule, lastRunAt) {
        const condition = [
            { finalApproval: finalApproval }
        ];
        if (rule) {
            condition.push((0, sequelize_1.literal)((0, helpers_1.convertRuleExpressionToSqlQuery)(rule, 'data')));
        }
        if (actions === null || actions === void 0 ? void 0 : actions.length) {
            condition.push({ action: { [sequelize_1.Op.in]: actions } });
        }
        if (entityIds === null || entityIds === void 0 ? void 0 : entityIds.length) {
            condition.push({ entityId: { [sequelize_1.Op.in]: entityIds } });
        }
        if (lastRunAt) {
            condition.push({ createdAt: { [sequelize_1.Op.gt]: lastRunAt } });
        }
        return this.findAll({
            where: {
                [sequelize_1.Op.and]: condition
            }
        });
    }
    markQueueLogAsProcessed(id, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.update({ processed: true }, currentContext, { where: { id } });
        });
    }
};
QueueLogRepository = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], QueueLogRepository);
exports.QueueLogRepository = QueueLogRepository;
//# sourceMappingURL=queue-log.repository.js.map