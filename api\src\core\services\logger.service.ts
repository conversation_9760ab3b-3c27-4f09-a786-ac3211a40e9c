import { ConsoleLogger, Injectable } from '@nestjs/common';
import { ConfigService } from 'src/config/config.service';

@Injectable()
export class LoggerService {
	constructor(private readonly logger: ConsoleLogger, private readonly config: ConfigService) {
		this.logger = new ConsoleLogger();
		this.logger.setLogLevels(this.config.getLogLevel());
	}

	/**
	 * Write a 'log' level log.
	 */
	log(message: any, context?: string) {
		this.logger.log(message, context);
	}

	/**
	 * Write an 'error' level log.
	 */
	error(message: any, stack?: string, context?: string) {
		this.logger.error(message, stack, context);
	}

	/**
	 * Write a 'warn' level log.
	 */
	warn(message: any, context?: string) {
		this.logger.warn(message, context);
	}

	/**
	 * Write a 'debug' level log.
	 */
	debug(message: any, context?: string) {
		this.logger.debug(message, context);
	}

	/**
	 * Write a 'verbose' level log.
	 */
	verbose(message: any, context?: string) {
		this.logger.verbose(message, context);
	}
}
