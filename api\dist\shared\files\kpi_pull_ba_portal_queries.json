{"KPIs": [{"kpi_name": "Working Hours per Day", "kpi_code": "working_hours_day", "query": "SELECT TERMINAL_ID as , WORKING_HOURS_PER_DAY as kpi_value FROM TP_GENERAL_INFO"}, {"kpi_name": "Working Days per Year", "kpi_code": "working_days_year", "query": "SELECT TERMINAL_ID, WORKING_DAYS_PER_YEAR as kpi_value FROM TP_GENERAL_INFO"}, {"kpi_name": "TGS (full and empty together)", "kpi_code": "tgs_full_empty", "query": "SELECT TERMINAL_ID, SUM(COUNT) AS kpi_value FROM TP_YARD_EQUIPMENTS GROUP BY TERMINAL_ID"}, {"kpi_name": "Yard Equipment Height", "kpi_code": "yard_equipment_height", "query": "SELECT TERMINAL_ID, SUM(EQUIPMENT_HEIGHT) AS kpi_value from TP_YARD_EQUIPMENTS GROUP BY TERMINAL_ID"}, {"kpi_name": "Gate IN Lanes", "kpi_code": "no_of_gates_in_lanes", "query": "SELECT TERMINAL_ID, N0_OF_GATE_IN_LANES AS kpi_value FROM TP_GATES"}, {"kpi_name": "Gate OUT Lanes", "kpi_code": "no_of_gates_out_lanes", "query": "SELECT TERMINAL_ID, NO_OF_GATE_OUT_LANES AS kpi_value FROM TP_GATES"}]}