import { BuSetupRepository, KpiConfigRepository, KpiDataRepository } from '../repositories';
import { KpiCalculatorService } from './kpi-calculator.service';
import { KpiConfigResponseDto } from '../dtos';
import { ModelRepository } from 'src/tc-submit/repositories';
export declare class TcConfigService {
    private readonly buSetupRepository;
    private readonly kpiConfigRepository;
    private readonly kpiDataRepository;
    private readonly kpiCalculatorService;
    private readonly modelRepository;
    constructor(buSetupRepository: BuSetupRepository, kpiConfigRepository: KpiConfigRepository, kpiDataRepository: KpiDataRepository, kpiCalculatorService: KpiCalculatorService, modelRepository: ModelRepository);
    getAllKpiConfigs(model_id: number): Promise<KpiConfigResponseDto[]>;
    getKpiConfigByCode(business_entity_id: number, systemCodes: string[], year: number, inputValues?: Record<string, any>): Promise<any>;
    getAllKpisAndValues(business_entity_id: number, inputValues?: Record<string, any>, overrideFetchMechanism?: string, startDate?: Date, endDate?: Date): Promise<any>;
    getKpisByTags(entityId: number, tags: string[], year: number, inputValues?: Record<string, any>): Promise<Record<string, any>>;
    getKpiConfigsByTags(tags: string[]): Promise<KpiConfigResponseDto[]>;
}
