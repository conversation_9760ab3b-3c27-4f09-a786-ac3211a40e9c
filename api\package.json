{"name": "backend", "version": "0.0.1", "description": "Backend API Server", "author": "DP World", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "npm run prebuild && nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "db:migrate": "npx sequelize-cli db:migrate", "db:seed": "sh scripts/seed.script.sh", "documentation:serve": "npx @compodoc/compodoc -p tsconfig.json --serve", "scheduler": "nest build -- --config=scheduler-nest-cli.json", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "lint": "lint-staged"}, "lint-staged": {"*.{json,md,html,js,jsx,ts,tsx}": ["prettier --write"]}, "dependencies": {"@azure/msal-node": "^1.12.1", "@compodoc/compodoc": "^1.1.19", "@fortawesome/fontawesome-free": "^6.3.0", "@nestjs/common": "^9.0.0", "@nestjs/core": "^9.0.0", "@nestjs/passport": "^9.0.0", "@nestjs/platform-express": "^9.0.0", "@nestjs/sequelize": "^9.0.0", "@nestjs/swagger": "^6.0.4", "@redis/client": "^1.2.0", "@types/lodash": "^4.14.182", "axios": "^0.27.2", "class-transformer": "^0.5.1", "class-validator": "^0.13.2", "cls-hooked": "^4.2.2", "dotenv": "^16.0.1", "exceljs": "^4.3.0", "express-basic-auth": "^1.2.1", "handlebars": "^4.7.7", "helmet": "^5.1.1", "i": "^0.3.7", "json-rules-engine": "^6.1.2", "lodash": "^4.17.21", "mathjs": "^11.8.0", "moment": "^2.29.4", "mssql": "^10.0.1", "npm": "^8.15.1", "passport": "^0.6.0", "passport-azure-ad": "^4.3.4", "passport-http": "^0.3.0", "passport-local": "^1.0.0", "pg": "^8.7.3", "pg-hstore": "^2.3.4", "platform": "^1.3.6", "puppeteer": "^19.7.1", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "sequelize": "^6.21.3", "sequelize-typescript": "^2.1.3", "sequelize-typescript-model-migration": "^1.0.13", "tedious": "^16.6.1"}, "devDependencies": {"@nestjs/cli": "^9.0.0", "@nestjs/schematics": "^9.0.0", "@nestjs/testing": "^9.0.0", "@types/express": "^4.17.13", "@types/jest": "28.1.4", "@types/node": "^16.11.46", "@types/sequelize": "^4.28.14", "@types/supertest": "^2.0.11", "@types/validator": "^13.7.4", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.1", "jest": "28.1.2", "lint-staged": "^13.0.3", "prettier": "2.7.1", "sequelize-cli": "^6.4.1", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "28.0.5", "ts-loader": "^9.2.3", "ts-node": "^10.9.1", "tsconfig-paths": "4.0.0", "typescript": "^4.3.5"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}