import { TcConfigService } from '../services/tcconfig.service';
import { KpiConfigResponseDto, TagKpiConfigDto } from '../dtos';
import { RequestContext } from 'src/shared/types';
export declare class TcConfigController {
    private readonly tcConfigService;
    constructor(tcConfigService: TcConfigService);
    getKpiConfigsByTags(tags: string[]): Promise<KpiConfigResponseDto[]>;
    getAllKpiConfigs(model_id: number): Promise<KpiConfigResponseDto[]>;
    calculateKpisByTags(req: RequestContext, request: TagKpiConfigDto): Promise<Record<string, any>>;
}
