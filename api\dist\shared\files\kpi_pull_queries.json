{"KPIs": [{"kpi_name": "Quay Moves", "kpi_code": "quay_moves", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_quay_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Quay TEU", "kpi_code": "quay_teu", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(TEU) as kpi_value FROM rpt_pt_tos.terminal_capacity_quay_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Gate Moves", "kpi_code": "gate_moves", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_gate_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Gate TEU", "kpi_code": "gate_teu", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(TEU) as kpi_value FROM rpt_pt_tos.terminal_capacity_gate_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Yard TEU", "kpi_code": "yard_teu", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(TEU) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Total Reefer Move", "kpi_code": "quay_reefer_move", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_quay_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY= 'IMPORT' and STATUS = 'FULL' and COMMODITY_TYPE = 'REEFER' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Total Reefer TEU", "kpi_code": "quay_reefer_teu", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(TEU) as kpi_value FROM rpt_pt_tos.terminal_capacity_quay_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY= 'IMPORT' and STATUS = 'FULL' and COMMODITY_TYPE = 'REEFER'  GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Quay Container Import (SUM)", "kpi_code": "quay_container_visit_container_count_import", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(TEU) as kpi_value FROM rpt_pt_tos.terminal_capacity_quay_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY= 'IMPORT' and STATUS != 'BREAK BULK'  GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Quay Container Export (SUM)", "kpi_code": "quay_container_visit_container_count_export", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(TEU) as kpi_value FROM rpt_pt_tos.terminal_capacity_quay_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY= 'EXPORT' and STATUS != 'BREAK BULK'  GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Quay Container Transshipment (SUM)", "kpi_code": "quay_container_visit_container_count_transshipment", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(TEU) as kpi_value FROM rpt_pt_tos.terminal_capacity_quay_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY= 'TRANSHIPMENT' and STATUS != 'BREAK BULK'  GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Quay Container Restow (SUM)", "kpi_code": "quay_container_visit_container_count_restow", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(TEU) as kpi_value FROM rpt_pt_tos.terminal_capacity_quay_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY= 'RESTOW' and STATUS != 'BREAK BULK'  GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Quay Throughput - Import - Laden", "kpi_code": "quay_throughput_import_laden", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_quay_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY= 'IMPORT' and STATUS = 'FULL' and COMMODITY_TYPE != 'REEFER'  GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Quay Throughput - Import - Empty", "kpi_code": "quay_throughput_import_empty", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_quay_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY= 'IMPORT' and STATUS = 'EMPTY'  GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Quay Throughput - Import - Reefer", "kpi_code": "quay_throughput_import_reefer", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_quay_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY= 'IMPORT' and STATUS = 'FULL' and COMMODITY_TYPE = 'REEFER'  GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Quay Throughput - Import - Laden - Dwell Days", "kpi_code": "quay_throughput_import_laden_dwell_days", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, (SUM(DWELL_TM) / SUM(CONT_COUNT)) AS kpi_value FROM rpt_pt_tos.terminal_capacity_dwell_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY = 'IMPORT' AND STATUS = 'FULL' AND COMMODITY_TYPE != 'REEFER' AND TYPE = 'Quay' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Quay Throughput - Import - Empty - Dwell Days", "kpi_code": "quay_throughput_import_empty_dwell_days", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, (SUM(DWELL_TM) / SUM(CONT_COUNT)) AS kpi_value FROM rpt_pt_tos.terminal_capacity_dwell_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY = 'IMPORT' AND STATUS = 'EMPTY' AND TYPE = 'Quay' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Quay Throughput - Import - Reefer - Dwell Days", "kpi_code": "quay_throughput_import_reefer_dwell_days", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, (SUM(DWELL_TM) / SUM(CONT_COUNT)) AS kpi_value FROM rpt_pt_tos.terminal_capacity_dwell_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY = 'IMPORT' AND STATUS = 'FULL' AND COMMODITY_TYPE = 'REEFER' AND TYPE = 'Quay' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Quay Throughput - Export - Laden", "kpi_code": "quay_throughput_export_laden", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_quay_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY= 'EXPORT' and STATUS = 'FULL' and COMMODITY_TYPE != 'REEFER'  GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Quay Throughput - Export - Empty", "kpi_code": "quay_throughput_export_empty", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_quay_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY= 'EXPORT' and STATUS = 'EMPTY'  GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Quay Throughput - Export - Reefer", "kpi_code": "quay_throughput_export_reefer", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_quay_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY= 'EXPORT' and STATUS = 'FULL' and COMMODITY_TYPE = 'REEFER'  GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Quay Throughput - Export - Laden - Dwell Days", "kpi_code": "quay_throughput_export_laden_dwell_days", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, (SUM(DWELL_TM) / SUM(CONT_COUNT)) AS kpi_value FROM rpt_pt_tos.terminal_capacity_dwell_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY = 'EXPORT' AND STATUS = 'FULL' AND COMMODITY_TYPE != 'REEFER' AND TYPE = 'Quay' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Quay Throughput - Export - Empty - Dwell Days", "kpi_code": "quay_throughput_export_empty_dwell_days", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, (SUM(DWELL_TM) / SUM(CONT_COUNT)) AS kpi_value FROM rpt_pt_tos.terminal_capacity_dwell_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY = 'EXPORT' AND STATUS = 'EMPTY' AND TYPE = 'Quay' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Quay Throughput - Export - Reefer - Dwell Days", "kpi_code": "quay_throughput_export_reefer_dwell_days", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, (SUM(DWELL_TM) / SUM(CONT_COUNT)) AS kpi_value FROM rpt_pt_tos.terminal_capacity_dwell_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY = 'EXPORT' AND STATUS = 'FULL' AND COMMODITY_TYPE = 'REEFER' AND TYPE = 'Quay' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Quay Throughput - Transship - Laden", "kpi_code": "quay_throughput_transship_laden", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_quay_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY= 'TRANSHIPMENT' and STATUS = 'FULL' and COMMODITY_TYPE != 'REEFER'  GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Quay Throughput - Transship - Empty", "kpi_code": "quay_throughput_transship_empty", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_quay_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY= 'TRANSHIPMENT' and STATUS = 'EMPTY'  GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Quay Throughput - Transship - Reefer", "kpi_code": "quay_throughput_transship_reefer", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_quay_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY= 'TRANSHIPMENT' and STATUS = 'FULL' and COMMODITY_TYPE = 'REEFER'  GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Quay Throughput - Transship - Laden - Dwell Days", "kpi_code": "quay_throughput_transship_laden_dwell_days", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, (SUM(DWELL_TM) / SUM(CONT_COUNT)) AS kpi_value FROM rpt_pt_tos.terminal_capacity_dwell_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY = 'TRANSHIPMENT' AND STATUS = 'FULL' AND COMMODITY_TYPE != 'REEFER' AND TYPE = 'Quay' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Quay Throughput - Transship - Empty - Dwell Days", "kpi_code": "quay_throughput_transship_empty_dwell_days", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, (SUM(DWELL_TM) / SUM(CONT_COUNT)) AS kpi_value FROM rpt_pt_tos.terminal_capacity_dwell_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY = 'TRANSHIPMENT' AND STATUS = 'EMPTY' AND TYPE = 'Quay' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Quay Throughput - Transship - Reefer - Dwell Days", "kpi_code": "quay_throughput_transship_reefer_dwell_days", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, (SUM(DWELL_TM) / SUM(CONT_COUNT)) AS kpi_value FROM rpt_pt_tos.terminal_capacity_dwell_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY = 'TRANSHIPMENT' AND STATUS = 'FULL' AND COMMODITY_TYPE = 'REEFER' AND TYPE = 'Quay' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Quay Throughput - Restow - Laden", "kpi_code": "quay_throughput_restow_laden", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_quay_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY= 'RESTOW' and STATUS = 'FULL' and COMMODITY_TYPE != 'REEFER'  GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Quay Throughput - Restow - Empty", "kpi_code": "quay_throughput_restow_empty", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_quay_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY= 'RESTOW' and STATUS = 'EMPTY'  GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Quay Throughput - Restow - Reefer", "kpi_code": "quay_throughput_restow_reefer", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_quay_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY= 'RESTOW' and STATUS = 'FULL' and COMMODITY_TYPE = 'REEFER'  GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Quay Throughput - Restow - Laden - Dwell Days", "kpi_code": "quay_throughput_restow_laden_dwell_days", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, (SUM(DWELL_TM) / SUM(CONT_COUNT)) AS kpi_value FROM rpt_pt_tos.terminal_capacity_dwell_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY = 'RESTOW' AND STATUS = 'FULL' AND COMMODITY_TYPE != 'REEFER' AND TYPE = 'Quay' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Quay Throughput - Restow - Empty - Dwell Days", "kpi_code": "quay_throughput_restow_empty_dwell_days", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, (SUM(DWELL_TM) / SUM(CONT_COUNT)) AS kpi_value FROM rpt_pt_tos.terminal_capacity_dwell_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY = 'RESTOW' AND STATUS = 'EMPTY' AND TYPE = 'Quay' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Quay Throughput - Restow - Reefer - Dwell Days", "kpi_code": "quay_throughput_restow_reefer_dwell_days", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, (SUM(DWELL_TM) / SUM(CONT_COUNT)) AS kpi_value FROM rpt_pt_tos.terminal_capacity_dwell_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY = 'RESTOW' AND STATUS = 'FULL' AND COMMODITY_TYPE = 'REEFER' AND TYPE = 'Quay' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Non-Quay Container Visit - Split % - Gate to Gate", "kpi_code": "non_quay_container_visit_split_gate_to_gate", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(GATE_TO_GATE) as kpi_value FROM rpt_pt_tos.terminal_capacity_nonquay_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Non-Quay Container Visit - Split % - Gate to Rail", "kpi_code": "non_quay_container_visit_split_gate_to_rail", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(GATE_TO_RAIL) as kpi_value FROM rpt_pt_tos.terminal_capacity_nonquay_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Non-Quay Container Visit - Split % - Rail to Gate", "kpi_code": "non_quay_container_visit_split_rail_to_gate", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(RAIL_TO_GATE) as kpi_value FROM rpt_pt_tos.terminal_capacity_nonquay_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Non-Quay Container Visit - Split % - Rail to Rail", "kpi_code": "non_quay_container_visit_split_rail_to_rail", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(RAIL_TO_RAIL) as kpi_value FROM rpt_pt_tos.terminal_capacity_nonquay_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Non-Quay Container - Laden", "kpi_code": "non_quay_container_laden", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, (SUM(GATE_TO_GATE)+SUM(GATE_TO_RAIL)+SUM(RAIL_TO_GATE)+SUM(RAIL_TO_RAIL)) as kpi_value FROM rpt_pt_tos.terminal_capacity_nonquay_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND STATUS = 'FULL' and COMMODITY_TYPE != 'REEFER'  GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Non-Quay Container - Empty", "kpi_code": "non_quay_container_empty", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, (SUM(GATE_TO_GATE)+SUM(GATE_TO_RAIL)+SUM(RAIL_TO_GATE)+SUM(RAIL_TO_RAIL)) as kpi_value FROM rpt_pt_tos.terminal_capacity_nonquay_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND STATUS = 'EMPTY' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Non-Quay Container - Reefer", "kpi_code": "non_quay_container_reefer", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, (SUM(GATE_TO_GATE)+SUM(GATE_TO_RAIL)+SUM(RAIL_TO_GATE)+SUM(RAIL_TO_RAIL)) as kpi_value FROM rpt_pt_tos.terminal_capacity_nonquay_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND STATUS = 'FULL' and COMMODITY_TYPE = 'REEFER'  GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Yard Essential Moves - Laden", "kpi_code": "yard_essential_moves_laden", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND ESSENTIAL = 'Y' and MOVE_DIRECTION = 'Vertical' and STATUS = 'FULL' and COMMODITY_TYPE != 'REEFER'  GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Yard Essential Moves - Empty", "kpi_code": "yard_essential_moves_empty", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND ESSENTIAL = 'Y' and MOVE_DIRECTION = 'Vertical' and STATUS = 'EMPTY' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Yard Essential Moves - Reefer", "kpi_code": "yard_essential_moves_reefer", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND ESSENTIAL = 'Y' and MOVE_DIRECTION = 'Vertical' and STATUS = 'FULL' and COMMODITY_TYPE = 'REEFER'  GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Inspection - Import - Laden", "kpi_code": "inspection_import_laden", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY = 'IMPORT' AND  EVENT like '%INSP%' and MOVE_DIRECTION = 'Vertical' and STATUS = 'FULL' and COMMODITY_TYPE != 'REEFER'  GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Inspection - Import - Reefer", "kpi_code": "inspection_import_reefer", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY = 'IMPORT' AND EVENT like '%INSP%' and MOVE_DIRECTION = 'Vertical' and STATUS = 'FULL' and COMMODITY_TYPE = 'REEFER'  GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Inspection - Export - Laden", "kpi_code": "inspection_export_laden", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY = 'EXPORT' AND EVENT like '%INSP%' and MOVE_DIRECTION = 'Vertical' and STATUS = 'FULL' and COMMODITY_TYPE != 'REEFER'  GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Inspection - Export - Reefer", "kpi_code": "inspection_export_reefer", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY = 'EXPORT' AND EVENT like '%INSP%' and MOVE_DIRECTION = 'Vertical' and STATUS = 'FULL' and COMMODITY_TYPE = 'REEFER'  GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Inspection - Transhipment - Laden", "kpi_code": "inspection_transhipment_laden", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY = 'TRANSHIPMENT' AND EVENT like '%INSP%' and MOVE_DIRECTION = 'Vertical' and STATUS = 'FULL' and COMMODITY_TYPE != 'REEFER'  GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Inspection - Storage - Empty", "kpi_code": "inspection_storage_empty", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY = 'STORAGE' AND EVENT like '%INSP%' and MOVE_DIRECTION = 'Vertical' and STATUS = 'EMPTY' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Yard - Shuffle - <PERSON>den", "kpi_code": "yard_shuffle_laden", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY = 'TRANSHIPMENT' AND EVENT in ('YARD_MOVE_SHUFFLE','YARD_SHUFFLE_FOR_LOAD','YARD_SHUFFLE_FOR_RLOAD','YARD_SHUFFLE_FOR_DLVR','YARD_SHUFFLE_FOR_INTERNAL_MOVE') and MOVE_DIRECTION = 'Vertical' and STATUS = 'FULL' and COMMODITY_TYPE != 'REEFER' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Yard - Shuffle - Empty", "kpi_code": "yard_shuffle_empty", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY = 'TRANSHIPMENT' AND EVENT in ('YARD_MOVE_SHUFFLE','YARD_SHUFFLE_FOR_LOAD','YARD_SHUFFLE_FOR_RLOAD','YARD_SHUFFLE_FOR_DLVR','YARD_SHUFFLE_FOR_INTERNAL_MOVE') and MOVE_DIRECTION = 'Vertical' and STATUS = 'EMPTY' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Yard - Housekeeping - Laden", "kpi_code": "yard_housekeeping_laden", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND EVENT in ('YARD_INTERNAL_MOVE_PUT','DLVR_LANE_TO_TRUCK','YARD_MOVE_TO_INSP_PUT','YARD_INTERNAL_MOVE_FETCH','TRUCK_TO_DLVR_LANE','YARD_INTERNAL_MOVE_TRANSPORT','YARD_TRANSPORT _TO_INSP','YARD_TRANSPORT_FROM_INSP','YARD_MOVE_FROM_INSP_PUT','YARD_MOVE_FROM_INSP_FETCH','YARD_MOVE_TO_INSP_FETCH') and MOVE_DIRECTION = 'Vertical' and STATUS = 'FULL' and COMMODITY_TYPE != 'REEFER' GROUP BY Location_Code, YEAR(DATE), <PERSON>ON<PERSON>(DATE), DAY(DATE)"}, {"kpi_name": "Yard - Housekeeping - Empty", "kpi_code": "yard_housekeeping_empty", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND EVENT in ('YARD_INTERNAL_MOVE_PUT','DLVR_LANE_TO_TRUCK','YARD_MOVE_TO_INSP_PUT','YARD_INTERNAL_MOVE_FETCH','TRUCK_TO_DLVR_LANE','YARD_INTERNAL_MOVE_TRANSPORT','YARD_TRANSPORT _TO_INSP','YARD_TRANSPORT_FROM_INSP','YARD_MOVE_FROM_INSP_PUT','YARD_MOVE_FROM_INSP_FETCH','YARD_MOVE_TO_INSP_FETCH') and MOVE_DIRECTION = 'Vertical' and STATUS = 'EMPTY' GROUP BY Location_Code, YEAR(DATE), <PERSON><PERSON><PERSON>(DATE), DAY(DATE)"}, {"kpi_name": "Yard - Housekeeping - Reefer", "kpi_code": "yard_housekeeping_reefer", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND EVENT in ('YARD_INTERNAL_MOVE_PUT','DLVR_LANE_TO_TRUCK','YARD_MOVE_TO_INSP_PUT','YARD_INTERNAL_MOVE_FETCH','TRUCK_TO_DLVR_LANE','YARD_INTERNAL_MOVE_TRANSPORT','YARD_TRANSPORT _TO_INSP','YARD_TRANSPORT_FROM_INSP','YARD_MOVE_FROM_INSP_PUT','YARD_MOVE_FROM_INSP_FETCH','YARD_MOVE_TO_INSP_FETCH') and MOVE_DIRECTION = 'Vertical' and STATUS = 'FULL' and COMMODITY_TYPE = 'REEFER' GROUP BY Location_Code, YEAR(DATE), <PERSON>ON<PERSON>(DATE), DAY(DATE)"}, {"kpi_name": "Non Essential Yard Moves - Laden", "kpi_code": "non_essential_yard_moves_laden", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND ESSENTIAL = 'N' and MOVE_DIRECTION = 'Vertical' and STATUS = 'FULL' and COMMODITY_TYPE != 'REEFER' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Non Essential Yard Moves - Empty", "kpi_code": "non_essential_yard_moves_empty", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND ESSENTIAL = 'N' and MOVE_DIRECTION = 'Vertical' and STATUS = 'EMPTY' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Non Essential Yard Moves - Reefer", "kpi_code": "non_essential_yard_moves_reefer", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND ESSENTIAL = 'N' and MOVE_DIRECTION = 'Vertical' and STATUS = 'EMPTY' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Quay Moves - QC", "kpi_code": "quay_moves_qc", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_quay_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND EQUIPMENT_TYPE = 'QC' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Yard Moved - Laden - ARMG", "kpi_code": "yard_moved_equipment_laden_armg", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND EQUIPMENT_TYPE = 'ARMG' and MOVE_DIRECTION = 'Vertical' AND STATUS = 'FULL' AND COMMODITY_TYPE != 'REEFER' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Yard Moved - Empty - ARMG", "kpi_code": "yard_moved_equipment_empty_armg", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND EQUIPMENT_TYPE = 'ARMG' and MOVE_DIRECTION = 'Vertical' AND STATUS = 'EMPTY' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Yard Moved - Refeer - ARMG", "kpi_code": "yard_moved_equipment_refeer_armg", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND EQUIPMENT_TYPE = 'ARMG' and MOVE_DIRECTION = 'Vertical' AND STATUS = 'FULL' AND COMMODITY_TYPE = 'REEFER' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Yard Moved - Laden - ECH", "kpi_code": "yard_moved_equipment_laden_ech", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND EQUIPMENT_TYPE = 'ECH' and MOVE_DIRECTION = 'Vertical' AND STATUS = 'FULL' AND COMMODITY_TYPE != 'REEFER' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Yard Moved - Empty - ECH", "kpi_code": "yard_moved_equipment_empty_ech", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND EQUIPMENT_TYPE = 'ECH' and MOVE_DIRECTION = 'Vertical' AND STATUS = 'EMPTY' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Yard Moved - Refeer - ECH", "kpi_code": "yard_moved_equipment_refeer_ech", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND EQUIPMENT_TYPE = 'ECH' and MOVE_DIRECTION = 'Vertical' AND STATUS = 'FULL' AND COMMODITY_TYPE = 'REEFER' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Yard Moved - Laden - FL", "kpi_code": "yard_moved_equipment_laden_fl", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND EQUIPMENT_TYPE = 'FL' and MOVE_DIRECTION = 'Vertical' AND STATUS = 'FULL' AND COMMODITY_TYPE != 'REEFER' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Yard Moved - Empty - FL", "kpi_code": "yard_moved_equipment_empty_fl", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND EQUIPMENT_TYPE = 'FL' and MOVE_DIRECTION = 'Vertical' AND STATUS = 'EMPTY' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Yard Moved - Refeer - FL", "kpi_code": "yard_moved_equipment_refeer_fl", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND EQUIPMENT_TYPE = 'FL' and MOVE_DIRECTION = 'Vertical' AND STATUS = 'FULL' AND COMMODITY_TYPE = 'REEFER' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Yard Moved - Laden - RS - TL", "kpi_code": "yard_moved_equipment_laden_rs_tl", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND EQUIPMENT_TYPE in ('RS','TL') and MOVE_DIRECTION = 'Vertical' AND STATUS = 'FULL' AND COMMODITY_TYPE != 'REEFER' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Yard Moved - Empty - RS - TL", "kpi_code": "yard_moved_equipment_empty_rs_tl", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND EQUIPMENT_TYPE in ('RS','TL') and MOVE_DIRECTION = 'Vertical' AND STATUS = 'EMPTY' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Yard Moved - Refeer - RS - TL", "kpi_code": "yard_moved_equipment_refeer_rs_tl", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND EQUIPMENT_TYPE in ('RS','TL') and MOVE_DIRECTION = 'Vertical' AND STATUS = 'FULL' AND COMMODITY_TYPE = 'REEFER' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Yard Moved - Laden - RTG", "kpi_code": "yard_moved_equipment_laden_rtg", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND EQUIPMENT_TYPE = ('RTG') and MOVE_DIRECTION = 'Vertical' AND STATUS = 'FULL' AND COMMODITY_TYPE != 'REEFER' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Yard Moved - Empty - RTG", "kpi_code": "yard_moved_equipment_empty_rtg", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND EQUIPMENT_TYPE = ('RTG') and MOVE_DIRECTION = 'Vertical' AND STATUS = 'EMPTY' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Yard Moved - Refeer - RTG", "kpi_code": "yard_moved_equipment_refeer_rtg", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND EQUIPMENT_TYPE = ('RTG') and MOVE_DIRECTION = 'Vertical' AND STATUS = 'FULL' AND COMMODITY_TYPE = 'REEFER' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Yard Moved - Laden - QC", "kpi_code": "yard_moved_equipment_laden_qc", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND EQUIPMENT_TYPE = ('QC') and MOVE_DIRECTION = 'Vertical' AND STATUS = 'FULL' AND COMMODITY_TYPE != 'REEFER' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Yard Moved - Empty - QC", "kpi_code": "yard_moved_equipment_empty_qc", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND EQUIPMENT_TYPE = ('QC') and MOVE_DIRECTION = 'Vertical' AND STATUS = 'EMPTY' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Yard Moved - Refeer - QC", "kpi_code": "yard_moved_equipment_refeer_qc", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND EQUIPMENT_TYPE = ('QC') and MOVE_DIRECTION = 'Vertical' AND STATUS = 'FULL' AND COMMODITY_TYPE = 'REEFER' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Yard Moved - Laden - TT", "kpi_code": "yard_moved_equipment_laden_tt", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND EQUIPMENT_TYPE = ('TT') and MOVE_DIRECTION = 'Vertical' AND STATUS = 'FULL' AND COMMODITY_TYPE != 'REEFER' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Yard Moved - Empty - TT", "kpi_code": "yard_moved_equipment_empty_tt", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND EQUIPMENT_TYPE = ('TT') and MOVE_DIRECTION = 'Vertical' AND STATUS = 'EMPTY' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Yard Moved - Refeer - TT", "kpi_code": "yard_moved_equipment_refeer_tt", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND EQUIPMENT_TYPE = ('TT') and MOVE_DIRECTION = 'Vertical' AND STATUS = 'FULL' AND COMMODITY_TYPE = 'REEFER' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Yard Moved - Laden - ASC", "kpi_code": "yard_moved_equipment_laden_asc", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND EQUIPMENT_TYPE = ('ASC') and MOVE_DIRECTION = 'Vertical' AND STATUS = 'FULL' AND COMMODITY_TYPE != 'REEFER' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Yard Moved - Empty - ASC", "kpi_code": "yard_moved_equipment_empty_asc", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND EQUIPMENT_TYPE = ('ASC') and MOVE_DIRECTION = 'Vertical' AND STATUS = 'EMPTY' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Yard Moved - Refeer - ASC", "kpi_code": "yard_moved_equipment_refeer_asc", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND EQUIPMENT_TYPE = ('ASC') and MOVE_DIRECTION = 'Vertical' AND STATUS = 'FULL' AND COMMODITY_TYPE = 'REEFER' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Yard Moved - Laden - SC", "kpi_code": "yard_moved_equipment_laden_sc", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND EQUIPMENT_TYPE = ('SC') and MOVE_DIRECTION = 'Vertical' AND STATUS = 'FULL' AND COMMODITY_TYPE != 'REEFER' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Yard Moved - Empty - SC", "kpi_code": "yard_moved_equipment_empty_sc", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND EQUIPMENT_TYPE = ('SC') and MOVE_DIRECTION = 'Vertical' AND STATUS = 'EMPTY' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Yard Moved - Refeer - SC", "kpi_code": "yard_moved_equipment_refeer_sc", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND EQUIPMENT_TYPE = ('SC') and MOVE_DIRECTION = 'Vertical' AND STATUS = 'FULL' AND COMMODITY_TYPE = 'REEFER' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Import from Vessel to Gate", "kpi_code": "import_from_vessel_to_gate", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(VSL_TO_GATE) as kpi_value FROM rpt_pt_tos.terminal_capacity_nonquay_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Import from Vessel To Rail", "kpi_code": "import_from_vessel_to_rail", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(VSL_TO_RAIL) as kpi_value FROM rpt_pt_tos.terminal_capacity_nonquay_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Export to Vessel from Gate", "kpi_code": "export_to_vessel_from_gate", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(GATE_TO_VSL) as kpi_value FROM rpt_pt_tos.terminal_capacity_nonquay_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Export to Vessel from Rail", "kpi_code": "export_to_vessel_from_rail", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(RAIL_TO_VSL) as kpi_value FROM rpt_pt_tos.terminal_capacity_nonquay_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Total Yard Moves", "kpi_code": "total_yard_moves", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(MOVES) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Total Yard Transshipment", "kpi_code": "total_yard_transshipment", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, SUM(TEU) as kpi_value FROM rpt_pt_tos.terminal_capacity_yard_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY= 'TRANSHIPMENT' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}, {"kpi_name": "Total Dwell Time", "kpi_code": "total_dwell_time", "query": "SELECT Location_Code, YEAR(DATE) AS Year, MONTH(DATE) AS Month, DAY(DATE) AS Day, (SUM(DWELL_TM) / SUM(CONT_COUNT)) AS kpi_value FROM rpt_pt_tos.terminal_capacity_dwell_kpis WHERE DATE >= @StartDate AND DATE <= @EndDate AND CATEGORY = 'RESTOW' AND STATUS = 'FULL' AND COMMODITY_TYPE = 'REEFER' AND TYPE = 'Quay' GROUP BY Location_Code, YEAR(DATE), MONTH(DATE), DAY(DATE)"}]}