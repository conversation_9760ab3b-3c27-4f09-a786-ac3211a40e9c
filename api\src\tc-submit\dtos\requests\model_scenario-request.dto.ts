import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsArray, IsNotEmpty, IsOptional } from 'class-validator';

export class ModelScenarioRequestDto {

	@ApiProperty({ name: 'model_id', type: Number })
	@Expose({ name: 'model_id' })
	@IsNotEmpty()
	public model_id: number;

	@ApiProperty({ name: 'is_current_state', type: Boolean })
	@Expose({ name: 'is_current_state' })
	@IsNotEmpty()
	public is_current_state: boolean;

	@ApiProperty({ name: 'scenario_number', type: Number })
	@Expose({ name: 'scenario_number' })
	@IsNotEmpty()
	public scenario_number:number;

	@ApiProperty({ name: 'kpi_data', type: String })
	@Expose({ name: 'kpi_data' })
    @IsOptional()
	public kpi_data: JSON | null;
}
