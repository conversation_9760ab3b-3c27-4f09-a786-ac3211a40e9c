import { QueueLog } from 'src/queue/models';
import { SyncLog } from 'src/scheduler/models/sync_log.model';
import { BuSetup, KpiConfig, KpiData } from 'src/tc-config/models';
import { Model, ModelScenario, ModelScenarioData } from 'src/tc-submit/models';
export declare const getSequelizeOrmConfig: (enableSSL: any) => {
    ssl: boolean;
    dialectOptions: {
        ssl: {
            require: boolean;
        };
    };
    synchronize: boolean;
    autoLoadModels: boolean;
    models: (typeof SyncLog | typeof QueueLog | typeof BuSetup | typeof KpiConfig | typeof KpiData | typeof Model | typeof ModelScenario | typeof ModelScenarioData)[];
};
