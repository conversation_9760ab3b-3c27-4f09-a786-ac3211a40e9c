import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { <PERSON><PERSON><PERSON>y, IsNotEmpty, IsOptional } from 'class-validator';

export class ModelScenarioResponseDto {
	@ApiProperty({ type: Number })
	@Expose()
	public model_id: number;

	@ApiProperty({ type: Boolean })
	@Expose()
	public is_current_state: boolean;

	@ApiProperty({ type: Number })
	@Expose()
	public scenario_number:number;

	@ApiProperty({ type: String })
    @Expose()
	public kpi_data: JSON | null;
}
