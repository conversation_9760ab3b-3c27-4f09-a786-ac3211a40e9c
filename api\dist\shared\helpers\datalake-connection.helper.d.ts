import { ConfigService } from 'src/config/config.service';
import { AxiosInstance } from 'axios';
interface DatabricksConnection {
    jdbcUrl: string;
    username: string;
    password: string;
    httpClient: AxiosInstance;
    host: string;
    httpPath: string;
    isConnected: boolean;
}
export declare class DatalakeConnectionHelper {
    private readonly configService;
    private readonly logger;
    private connections;
    constructor(configService: ConfigService);
    private initializeConnections;
    private testConnection;
    getConnection(name?: string): DatabricksConnection | null;
    executeQuery(query: string, connectionName?: string): Promise<any[]>;
}
export {};
