"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SharedAttachmentService = void 0;
const common_1 = require("@nestjs/common");
const lodash_1 = require("lodash");
const attachment_api_client_1 = require("../clients/attachment-api.client");
const attachment_response_dto_1 = require("../dtos/attachment-response.dto");
const helpers_1 = require("../helpers");
const url_creator_1 = require("../helpers/url-creator");
let SharedAttachmentService = class SharedAttachmentService {
    constructor(attachmentApiClient) {
        this.attachmentApiClient = attachmentApiClient;
    }
    addBulkAttachment(attachments, entityId, entityType, relPath, userId) {
        return __awaiter(this, void 0, void 0, function* () {
            const newUploadPayload = attachments.map((attachmentData) => {
                attachmentData.file_base64 = attachmentData.file_base64.split('base64,')[1];
                return {
                    entity_id: entityId,
                    entity_type: entityType,
                    attachment_rel_path: (0, url_creator_1.replaceUrlVariable)(relPath, { entity_id: entityId }),
                    file_data: Buffer.from(attachmentData.file_base64, 'base64'),
                    created_by: userId.toLowerCase(),
                    attachment_name: attachmentData.attachment_name,
                    attachment_content_type: attachmentData.attachment_content_type,
                    attachment_content_size: attachmentData.size,
                    description: attachmentData.description,
                    meta_data_1: entityId.toString(),
                    meta_data_2: attachmentData.fileimage,
                    meta_data_3: attachmentData.size,
                };
            });
            if (attachments.length) {
                return this.attachmentApiClient.addBulkAttachments(newUploadPayload);
            }
        });
    }
    updateBulkAttachment(attachments) {
        return __awaiter(this, void 0, void 0, function* () {
            attachments.forEach((attachmentData) => __awaiter(this, void 0, void 0, function* () {
                const requestData = {
                    id: (0, lodash_1.toNumber)(attachmentData.id),
                    description: attachmentData.description,
                };
                yield this.attachmentApiClient.updateAttachments(requestData);
            }));
            return null;
        });
    }
    deleteBulkAttachment(attachments) {
        return __awaiter(this, void 0, void 0, function* () {
            attachments.forEach((attachmentData) => __awaiter(this, void 0, void 0, function* () {
                yield this.attachmentApiClient.deleteAttachmentByFileId(attachmentData.file_id);
            }));
            return null;
        });
    }
    supportingDocumentsActivity(supportingDocuments, entityId, entityType, relativePath, userId) {
        return __awaiter(this, void 0, void 0, function* () {
            let uploadedDocumentList = [];
            const newAttachmentRequest = supportingDocuments.filter((newAttachment) => {
                return newAttachment.IsNew;
            });
            const updateMetadataRequest = supportingDocuments.filter((updateRequest) => {
                return !updateRequest.IsNew && updateRequest.IsEdited;
            });
            if (newAttachmentRequest.length) {
                yield this.addBulkAttachment(newAttachmentRequest, entityId, entityType, relativePath, userId);
            }
            if (updateMetadataRequest.length) {
                yield this.updateBulkAttachment(updateMetadataRequest);
            }
            if (newAttachmentRequest.length) {
                const uploadedDocumentData = yield this.attachmentApiClient.getAllAttachments(entityId, entityType);
                uploadedDocumentList = (0, helpers_1.multiObjectToInstance)(attachment_response_dto_1.AttachmentResponseDto, uploadedDocumentData);
                uploadedDocumentList.forEach(uploadData => {
                    uploadData.IsNew = false;
                    uploadData.IsEdited = false;
                });
            }
            return uploadedDocumentList;
        });
    }
    deleteSupportingDocumentsActivity(supportingDocuments) {
        return __awaiter(this, void 0, void 0, function* () {
            const deleteAttachmentRequest = supportingDocuments.filter((newAttachment) => {
                return !newAttachment.IsNew;
            });
            if (deleteAttachmentRequest.length) {
                yield this.deleteBulkAttachment(deleteAttachmentRequest);
            }
            return null;
        });
    }
};
SharedAttachmentService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [attachment_api_client_1.AttachmentApiClient])
], SharedAttachmentService);
exports.SharedAttachmentService = SharedAttachmentService;
//# sourceMappingURL=shared-attachment.service.js.map