CREATE OR REPLACE FUNCTION dev.get_afe_proposal_data(bigint)
 RETURNS TABLE(
 	project_name character varying, 
 	reference_number character varying, 
 	total_expenditure text, 
 	request_type character varying, 
 	business_unit character varying,
	budget_type text,
	justification text,
	user_status character varying,
	submitted_on text,
	project_expenditures text,
	cost_center_expenditures text
 )
 LANGUAGE plpgsql
AS $function$
BEGIN
    RETURN QUERY
    SELECT 
   		afp.name as "project_name", 
   		afp.project_reference_number as "reference_number",
   		trim(LEADING from to_char(total_amount, '999,999,999,999,999.99'))  as "total_expenditure",
   		art.title as "request_type",
   		afp.entity_title  as "business_unit",
   		case 
	   		when afp.budget_type = 'BUDGETED' then 'Budgeted'
	   		when afp.budget_type = 'UNBUDGETED' then 'Budgeted'
	   		when afp.budget_type = 'MIXED' then 'Budgeted & Unbudgeted'
	   		else 'N/A'
	   	end as "budget_type",
	   	afp."data" -> 'projectDetails'->>'projectJustification'::text as "justification",
	   	afp.user_status  as "user_status",
	   	to_char(afp.created_on, 'Mon DD YYYY HH:MIAM') as "submitted_on",
	   	(
	   		SELECT string_agg(object_title || ' - ' || trim(LEADING from to_char(amount, '999,999,999,999,999.99')), '; ') 
			FROM dev.afe_proposal_amount_splits 
			WHERE "type" = 'PROJECT_COMPONENT_SPLIT' and afe_proposal_id = $1 and object_title is not null and amount is not null
		) as "project_expenditures",
		(
	   		SELECT string_agg(object_title || ' - ' || trim(LEADING from to_char(amount, '999,999,999,999,999.99')), '; ') 
			FROM dev.afe_proposal_amount_splits 
			WHERE "type" = 'COST_CENTER_SPLIT' and afe_proposal_id = $1 and object_title is not null and  object_title != '' and amount is not null
		) as "cost_center_expenditures"
   	FROM 
   		dev.afe_proposals as afp
   	inner join dev.afe_request_types as art 
   		on afp.afe_request_type_id = art.id 
   	WHERE afp.id = $1;
END;
$function$;