import { RequestContext } from 'src/shared/types';
import { TcSubmitService } from '../services';
import { CreateScenarioDto, KpiCalculateDto, KpiValuesUpdateDto, ModelChartDto, ModelDto, ModelListingDto, RefreshKpiDto } from '../dtos';
export declare class TcSubmitController {
    private readonly submitService;
    constructor(submitService: TcSubmitService);
    createModelWithAllScenario(request: RequestContext, modelDto: ModelDto): Promise<any>;
    getAllActiveModels(request: RequestContext): Promise<any>;
    getAllModels(request: RequestContext, modelDto: ModelListingDto): Promise<any>;
    getModelById(request: RequestContext, id: number): Promise<any>;
    saveModelChartKpi(request: RequestContext, modelDto: ModelChartDto): Promise<any>;
    saveModelTabularKpi(request: RequestContext, modelDto: ModelChartDto): Promise<any>;
    getModelScenariosById(request: RequestContext, id: number): Promise<any>;
    updateKpiValues(request: RequestContext, updateDto: KpiValuesUpdateDto): Promise<any>;
    createNewScenario(request: RequestContext, createDto: CreateScenarioDto): Promise<any>;
    publishModel(request: RequestContext, publishDto: {
        model_id: number;
    }): Promise<any>;
    refreshKpiValues(request: RequestContext, refreshDto: RefreshKpiDto): Promise<any>;
    calculateKpiCodes(request: RequestContext, kpiCalculateDto: KpiCalculateDto): Promise<any>;
    refreshModel(request: RequestContext, refreshDto: {
        model_id: number;
    }): Promise<any>;
    updateModelFetchMechanism(request: RequestContext, refreshDto: {
        model_id: number;
        override_fetch_mechanism_type: string;
        pull_from_date?: Date;
        pull_to_date?: Date;
    }): Promise<any>;
}
