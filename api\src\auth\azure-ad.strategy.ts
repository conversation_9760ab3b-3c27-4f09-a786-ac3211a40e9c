import { BearerStrategy } from 'passport-azure-ad';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '../config/config.service';

@Injectable()
export class AzureADStrategy extends PassportStrategy(BearerStrategy, 'oauth-bearer') {
	constructor(configService: ConfigService) {
		const { azureAD } = configService.getAppConfig();
		const { authority, tennantId, clientId, version, discovery, audience, scope } = azureAD;
		super({
			identityMetadata: `${authority}/${tennantId}/${version}/${discovery}`,
			issuer: `${authority}/${tennantId}/${version}`,
			audience: audience,
			validateIssuer: false,
			clientID: clientId,
			loggingLevel: 'error',
			scope: scope,
			loggingNoPII: false,
			clockSkew: 320,
		});
	}

	public async validate(payload: unknown): Promise<unknown> {
		return payload;
	}
}
