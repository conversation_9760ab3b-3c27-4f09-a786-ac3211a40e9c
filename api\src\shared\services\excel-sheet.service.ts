import { Injectable } from '@nestjs/common';
import * as Excel from 'exceljs';
import { isBoolean } from 'lodash';
import { HttpStatus } from '../enums';
import { HttpException } from '../exceptions';
import { ANALYSIS_CODE_IMPORT_EXCEL_KEYS, ANALYSIS_CODE_IMPORT_EXCEL_POSITION, COLUMN_POSITION, COST_CENTER_IMPORT_EXCEL_KEYS, COST_CENTER_IMPORT_EXCEL_POSITION, NATURAL_ACCOUNT_IMPORT_EXCEL_KEYS, NATURAL_ACCOUNT_IMPORT_EXCEL_POSITION } from '../mappings';

@Injectable()
export class ExcelSheetService {
    constructor() { }

    public async createExcelSheet(
        data: { [key: string]: any }[],
        sheetName: string,
        dataValidations: { column: string, dataList: any[], startRow?: number,lastRow?: number, allowBlank?: boolean }[] = []
    ): Promise<Excel.Buffer> {
        const workbook = new Excel.Workbook();
        const worksheet = workbook.addWorksheet(sheetName);

        // Get the keys of the first JSON object as column names
        const columns = Object.keys(data[0]);

        // Add the columns to the worksheet
        columns.forEach((column, index) => {
            worksheet.getColumn(index + 1).values = [column];
            const headerCell = worksheet.getCell(1, index + 1)
            headerCell.value = column;
            headerCell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '120553' },
                bgColor: { argb: '120553' },
            };
            headerCell.font = {
                color: { argb: 'FFFFFF' },
                bold: true,
            };

        });

        // Add the values to the worksheet
        data.forEach((data, index) => {
            columns.forEach((column, colIndex) => {
                worksheet.getCell(index + 2, colIndex + 1).value = data[column];
            });
        });

        //Adjust the column width.
        worksheet.columns.forEach((column, _) => {
            let maxLength = 0;
            column['eachCell']({ includeEmpty: true }, (cell) => {
                // Set border of each cell 
                cell.border = {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' }
                };
                const columnLength = cell.value ? cell.value.toString().length : 5;
                if (columnLength > maxLength) {
                    maxLength = columnLength;
                }
            });
            column.width = maxLength < 5 ? 5 : maxLength;
        });

        
        dataValidations.forEach((dataValidation) => {
            for(let i = (dataValidation?.startRow || 2); i <= (dataValidation?.lastRow || 100); i++) {
                worksheet.getCell(dataValidation.column + i).dataValidation = {
                type: 'list',
                allowBlank: dataValidation?.allowBlank || true,
                formulae: [`"${dataValidation.dataList.join(",")}"`]
                };
            }
        });

        return workbook.xlsx.writeBuffer()
    }

    public async createPolicyExcelSheet(
        data: { [key: string]: any }[],
        sheetName: string,
    ): Promise<Excel.Buffer> {
        const workbook = new Excel.Workbook();
        const worksheet = workbook.addWorksheet(sheetName);

        // Get the keys of the first JSON object as column names
        const columns = Object.keys(data[0]);

        // Add the columns to the worksheet
        let index = 0;
        columns.forEach((column) => {
            if(column !== 'mergeColCount' && column !== 'isExceptionStep') {
                worksheet.getColumn(index + 1).values = [column];
                const headerCell = worksheet.getCell(1, index + 1)
                headerCell.value = column;
                headerCell.fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: '120553' },
                    bgColor: { argb: '120553' },
                };
                headerCell.font = {
                    color: { argb: 'FFFFFF' },
                    bold: true,
                };
                index = index + 1;
            }
        });

        // Add the values to the worksheet
        let indexData = 0;
        data.forEach((data) => {
            let colIndex = 0;
            columns.forEach((column) => {
                if(column !== 'mergeColCount' && column !== 'isExceptionStep') {
                    worksheet.getCell(indexData + 2, colIndex + 1).value = data[column];

                    if(data['isExceptionStep']) {
                        const headerCell = worksheet.getCell(indexData + 2, colIndex + 1)
                        headerCell.fill = {
                            type: 'pattern',
                            pattern: 'solid',
                            fgColor: { argb: 'FFE7E7' },
                            bgColor: { argb: 'FFE7E7' },
                        };
                        headerCell.font = {
                            color: { argb: '000000' },
                        };
                    }

                    colIndex = colIndex + 1;
                }
            });

            if(data['mergeColCount'] > 0) {
                worksheet.mergeCells((indexData + 2), 2, (indexData + 2), (data['mergeColCount'] + 2) );
                const headerCell = worksheet.getCell((indexData + 2), 2)
                headerCell.fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: 'D697FF' },
                    bgColor: { argb: 'D697FF' }
                };
                headerCell.font = {
                    color: { argb: '000000' }
                };

                worksheet.getCell((indexData + 2), 2).alignment = { 
                    vertical: 'middle', 
                    horizontal: 'center' 
                };

            }

            indexData = indexData + 1;
        });

        //Adjust the column width.
        worksheet.columns.forEach((column, _) => {
            let maxLength = 0;
            column['eachCell']({ includeEmpty: true }, (cell) => {
                // Set border of each cell 
                cell.border = {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' }
                };
                const columnLength = cell.value ? cell.value.toString().length : 5;
                if (columnLength > maxLength) {
                    maxLength = columnLength;
                }
            });
            column.width = maxLength < 5 ? 5 : maxLength;
        });

        return workbook.xlsx.writeBuffer()
    }

    public async readAnalysisCodeSheet(bufferData: Uint8Array) {
        let workbook = new Excel.Workbook();
        let excelData = [];
        let excelKeys = [];
        let requestTypes = ['capex', 'opex', 'both'];
        let totalColumns = 4;

        await workbook.xlsx.load(bufferData).then((workbook)=> {
            workbook.eachSheet((sheet) => {
              sheet.eachRow((row, rowIndex) => {

                if(row.cellCount !== totalColumns) {
                    throw new HttpException('Uploaded file is not valid. File must contain only 4 columns.', HttpStatus.BAD_REQUEST);
                }
                4
                for(let i = 1; i <= totalColumns; i++) {
                    if(row.values[i] === undefined || !row.values[i].toString()) {
                        throw new HttpException('Value of column ' + COLUMN_POSITION[i] + ' must not be empty. Issue at [' + COLUMN_POSITION[i] + rowIndex + ']', HttpStatus.BAD_REQUEST);
                    }
                }

                if(rowIndex === 1) {
                    row.eachCell((colValue, colIndex) => {
                        const colKey = ANALYSIS_CODE_IMPORT_EXCEL_KEYS[colValue.value.toString().toUpperCase()];
                        if(colKey) {

                            if(ANALYSIS_CODE_IMPORT_EXCEL_POSITION[colKey] !== colIndex){
                                throw new HttpException('Column '+ colValue.value + ' position is not valid. Correct column for '+ colValue.value + ' is ' + COLUMN_POSITION[ANALYSIS_CODE_IMPORT_EXCEL_POSITION[colKey]] + '.', HttpStatus.BAD_REQUEST);
                            }

                            excelKeys.push(ANALYSIS_CODE_IMPORT_EXCEL_KEYS[colValue.value.toString().toUpperCase()]);
                        } else {
                            throw new HttpException('' + colValue.value + ' is not a valid value. Issue at [' + COLUMN_POSITION[colIndex] + rowIndex + ']', HttpStatus.BAD_REQUEST);
                        }
                    });
                    if(excelKeys.length !== 4) {
                        throw new HttpException('Uploaded file is not valid. File must contain only 4 columns.', HttpStatus.BAD_REQUEST);
                    }
                } else {
                    let rowData = {};
                    row.eachCell((colValue, colNumber) => {

                        if(colNumber === 2) {
                            if(colValue.value.toString().length !== 6) {
                                throw new HttpException('Value of column ' + COLUMN_POSITION[colNumber] + ' must be of length 6. Issue at [' + COLUMN_POSITION[colNumber] + rowIndex + ']', HttpStatus.BAD_REQUEST);
                            }
                        }
                        if(colNumber === 3) {
                            if(!requestTypes.includes(colValue.value.toString().toLowerCase())) {
                                throw new HttpException('The value of column ' + COLUMN_POSITION[colNumber] + ' should only contain the ' + requestTypes.join(', ') + ' values. Issue at [' + COLUMN_POSITION[colNumber] + rowIndex + ']', HttpStatus.BAD_REQUEST);
                            }
                        }
                        if(colNumber === 4) {
                            if(!isBoolean(colValue.value)) {
                                throw new HttpException('Value of column '+ COLUMN_POSITION[colNumber] + ' must be boolean. Issue at [' + COLUMN_POSITION[colNumber] + rowIndex + ']', HttpStatus.BAD_REQUEST);
                            }
                        }
                        
                        rowData[excelKeys[colNumber-1]] = colValue.value;
                    });
                    excelData.push(rowData);
                }
              });
            });
        });

        return excelData;
    }
    
    public async readNaturalAccountSheet(bufferData: Uint8Array) {
        let workbook = new Excel.Workbook();
        let excelData = [];
        let excelKeys = [];
        let requestTypes = ['capex', 'opex', 'both'];
        let totalColumns = 4;

        await workbook.xlsx.load(bufferData).then((workbook)=> {
            workbook.eachSheet((sheet) => {
              sheet.eachRow((row, rowIndex) => {

                if(row.cellCount !== totalColumns) {
                    throw new HttpException('Uploaded file is not valid. File must contain only 4 columns.', HttpStatus.BAD_REQUEST);
                }

                for(let i = 1; i <= totalColumns; i++) {
                    if(row.values[i] === undefined || !row.values[i].toString()) {
                        throw new HttpException('Value of column ' + COLUMN_POSITION[i] + ' must not be empty. Issue at [' + COLUMN_POSITION[i] + rowIndex + ']', HttpStatus.BAD_REQUEST);
                    }
                }

                if(rowIndex === 1) {

                    row.eachCell((colValue, colIndex) => {
                        const colKey = NATURAL_ACCOUNT_IMPORT_EXCEL_KEYS[colValue.value.toString().toUpperCase()];
                        if(colKey) {

                            if(NATURAL_ACCOUNT_IMPORT_EXCEL_POSITION[colKey] !== colIndex){
                                throw new HttpException('Column '+ colValue.value + ' position is not valid. Correct column for '+ colValue.value + ' is ' + COLUMN_POSITION[NATURAL_ACCOUNT_IMPORT_EXCEL_POSITION[colKey]] + '.', HttpStatus.BAD_REQUEST);
                            }

                            excelKeys.push(NATURAL_ACCOUNT_IMPORT_EXCEL_KEYS[colValue.value.toString().toUpperCase()]);
                        } else {
                            throw new HttpException('' + colValue.value + ' is not a valid value. Issue at [' + COLUMN_POSITION[colIndex] + rowIndex + ']', HttpStatus.BAD_REQUEST);
                        }
                    });
                    
                } else {
                    let rowData = {};
                    row.eachCell((colValue, colNumber) => {

                        if(colNumber === 2) {
                            if(colValue.value.toString().length !== 8) {
                                throw new HttpException('Value of column ' + COLUMN_POSITION[colNumber] + ' must be of length 8. Issue at [' + COLUMN_POSITION[colNumber] + rowIndex + ']', HttpStatus.BAD_REQUEST);
                            }
                        }
                        if(colNumber === 3) {
                            if(!requestTypes.includes(colValue.value.toString().toLowerCase())) {
                                throw new HttpException('The value of column ' + COLUMN_POSITION[colNumber] + ' should only contain the ' + requestTypes.join(', ') + ' values. Issue at [' + COLUMN_POSITION[colNumber] + rowIndex + ']', HttpStatus.BAD_REQUEST);
                            }
                        }
                        if(colNumber === 4) {
                            if(!isBoolean(colValue.value)) {
                                throw new HttpException('Value of column '+ COLUMN_POSITION[colNumber] + ' must be boolean. Issue at [' + COLUMN_POSITION[colNumber] + rowIndex + ']', HttpStatus.BAD_REQUEST);
                            }
                        }
                        
                        rowData[excelKeys[colNumber-1]] = colValue.value;
                    });
                    excelData.push(rowData);
                }
              });
            });
        });

        return excelData;
    }

    public async readCostCenterSheet(bufferData: Uint8Array) {
        let workbook = new Excel.Workbook();
        let excelData = [];
        let excelKeys = [];
        let totalColumns = 6;

        await workbook.xlsx.load(bufferData).then((workbook)=> {
            workbook.eachSheet((sheet) => {
              sheet.eachRow((row, rowIndex) => {

                if(row.cellCount !== totalColumns) {
                    throw new HttpException('Uploaded file is not valid. File must contain only 4 columns.', HttpStatus.BAD_REQUEST);
                }

                for(let i = 1; i <= totalColumns; i++) {
                    if(row.values[i] === undefined || !row.values[i].toString()) {
                        throw new HttpException('Value of column ' + COLUMN_POSITION[i] + ' must not be empty. Issue at [' + COLUMN_POSITION[i] + rowIndex + ']', HttpStatus.BAD_REQUEST);
                    }
                }

                if(rowIndex === 1) {

                    row.eachCell((colValue, colIndex) => {
                        const colKey = COST_CENTER_IMPORT_EXCEL_KEYS[colValue.value.toString().toUpperCase()];
                        if(colKey) {

                            if(COST_CENTER_IMPORT_EXCEL_POSITION[colKey] !== colIndex){
                                throw new HttpException('Column '+ colValue.value + ' position is not valid. Correct column for '+ colValue.value + ' is ' + COLUMN_POSITION[COST_CENTER_IMPORT_EXCEL_POSITION[colKey]] + '.', HttpStatus.BAD_REQUEST);
                            }

                            excelKeys.push(COST_CENTER_IMPORT_EXCEL_KEYS[colValue.value.toString().toUpperCase()]);
                        } else {
                            throw new HttpException('' + colValue.value + ' is not a valid value. Issue at [' + COLUMN_POSITION[colIndex] + rowIndex + ']', HttpStatus.BAD_REQUEST);
                        }
                    });
                    
                } else {
                    let rowData = {};
                    row.eachCell((colValue, colNumber) => {

                        if(colNumber === 1) {
                            if(colValue.value.toString().length !== 4) {
                                throw new HttpException('Value of column ' + COLUMN_POSITION[colNumber] + ' must be of length 4. Issue at [' + COLUMN_POSITION[colNumber] + rowIndex + ']', HttpStatus.BAD_REQUEST);
                            }
                        }
                        
                        if(colNumber === 3 || colNumber === 6) {
                            if(!isBoolean(colValue.value)) {
                                throw new HttpException('Value of column '+ COLUMN_POSITION[colNumber] + ' must be boolean. Issue at [' + COLUMN_POSITION[colNumber] + rowIndex + ']', HttpStatus.BAD_REQUEST);
                            }
                        }
                        
                        rowData[excelKeys[colNumber-1]] = colValue.value;
                    });
                    excelData.push(rowData);
                }
              });
            });
        });

        return excelData;
    }
}