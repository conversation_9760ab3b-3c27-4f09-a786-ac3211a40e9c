
import { Injectable } from '@nestjs/common';
import { AdminApiClient, NotificationApiClient } from '../clients';
import { templatePlaceholderReplacer } from '../helpers/template-placeholder-replacer.helper';
import { SendNotificationRequest } from '../types';

@Injectable()
export class SharedNotificationService {
    constructor(
        private readonly adminApiClient: AdminApiClient,
        private readonly notificationApiClient: NotificationApiClient,
    ) { }

    /**
    * Send notification 
    * @param schedulerId 
    * @param recipients 
    * @param templateName 
    * @param placeholderValues 
    */
    public async sendNotification(
        entityId: number,
        entityType: string,
        recipients: { to: string[], cc?: string[], bcc?: string[] },
        templateName: string,
        placeholderValues: { [key: string]: string },
        isApprovalEmail: boolean = false,
        approvalTaskId?: number
    ): Promise<void> {
        const { to, cc, bcc } = recipients;
        const templateDetails = await this.adminApiClient.getNotificationTemplate(templateName);
        const { body, subject } = templateDetails;

        const subjectWithReplacedPlaceholderValues = templatePlaceholderReplacer(subject, placeholderValues);
        const bodyWithReplacedPlaceholderValues = templatePlaceholderReplacer(body, placeholderValues);
        const payload: SendNotificationRequest = {
            entity_id: entityId,
            entity_type: entityType,
            subject: subjectWithReplacedPlaceholderValues,
            is_approval_email: isApprovalEmail,
            receiver: to.join(';'),
            ...(!!cc && { cc: cc.join(';') }),
            ...(!!bcc && { bcc: bcc.join(';') }),
            ...(!!approvalTaskId && { approval_task_id: approvalTaskId }),
            body: bodyWithReplacedPlaceholderValues,
        };
        await this.notificationApiClient.sendNotification(payload);
    }
}