import { HttpService } from '../services/http.service';
import { BusinessEntity, BusinessEntityWithChildIds, BusinessEntityWithChildren, NotificationTemplateResponse, PermissionsResponse, User, UserRoles } from '../types';
export declare class AdminApiClient {
    private readonly adminApi;
    constructor(adminApi: HttpService);
    getBusinessEntityDetailsById(entityId: number): Promise<BusinessEntity>;
    getAllBusinessHierarchy(): Promise<BusinessEntityWithChildren>;
    getListOfUserPermissions(username: string): Promise<PermissionsResponse[]>;
    getBusinessEntityRoles(entityLevel: string): Promise<any>;
    getChildernListOfBusinessEntity(entityId: number): Promise<number[]>;
    getAllBusinessHierarchyByUserAndPermission(username: string, permission: string, parentId: number, lastLevel: string): Promise<any>;
    getParentIdsOfEntity(entityId: number): Promise<number[]>;
    getParentsOfEntity(entityId: number): Promise<BusinessEntityWithChildren[]>;
    getPath(entities: any, entityId: string): BusinessEntityWithChildren[];
    getNavBarByPosition(position: string): Promise<Record<string, any>>;
    hasPermissionToUser(username: string, permission: string, entityId?: number): Promise<boolean>;
    hasUserRole(username: string, role: string, entityId?: number): Promise<boolean>;
    getUserRoles(loginId: string): Promise<UserRoles[]>;
    getBusinessEntityLevels(): Promise<Record<string, any>>;
    getParentEntityOfAnEntityOfGivenLevel(entityId: number, level: string): Promise<BusinessEntityWithChildIds>;
    getUsersByRoleOfAnEntity(role: string, entityId: number): Promise<User[]>;
    getBusinessEntitiesByUserAndPermission(username: string, permission?: string): Promise<BusinessEntityWithChildIds[]>;
    getNotificationTemplate(templateName: string): Promise<NotificationTemplateResponse>;
}
