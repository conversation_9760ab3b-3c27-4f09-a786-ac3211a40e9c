import * as jsonRulesEngine from 'json-rules-engine';
import { RuleCondition } from '../types';
import { RuleEngineData } from '../types/rule-engine-data.type';

export const ruleValidator = async (
	expression: RuleCondition,
	facts: RuleEngineData,
): Promise<boolean> => {
	//For reference: https://github.com/cachecontrol/json-rules-engine
	try {
		let engine = new jsonRulesEngine.Engine();
		engine.addRule({
			conditions: expression,
			event: {
				// define the event to fire when the conditions evaluate truthy
				type: 'expression_validation',
				params: {
					message: true,
				},
			},
		});
		const { events } = await engine.run(facts);
		return !!events.length;
	} catch (_err) {
		return false;
	}
};
