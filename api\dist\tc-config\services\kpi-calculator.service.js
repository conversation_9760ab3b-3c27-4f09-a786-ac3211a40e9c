"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KpiCalculatorService = void 0;
const common_1 = require("@nestjs/common");
const kpi_formula_evaluator_helper_1 = require("../helpers/kpi-formula-evaluator.helper");
const repositories_1 = require("../repositories");
const sequlize_operator_helper_1 = require("../../shared/helpers/sequlize-operator.helper");
const sequelize_1 = require("sequelize");
let KpiCalculatorService = class KpiCalculatorService {
    constructor(kpiConfigRepository, kpiDataRepository, buSetupRepository) {
        this.kpiConfigRepository = kpiConfigRepository;
        this.kpiDataRepository = kpiDataRepository;
        this.buSetupRepository = buSetupRepository;
    }
    saveKpiValues(entityId, month, kpiValues, userId) {
        return __awaiter(this, void 0, void 0, function* () {
            const kpiEntries = Object.entries(kpiValues);
            for (const [kpiCode, kpiValue] of kpiEntries) {
                if (kpiValue === null || kpiValue === undefined)
                    continue;
                const existingData = yield this.kpiDataRepository.findOne({
                    where: {
                        kpi_code: kpiCode,
                        kpi_month: month,
                    },
                });
                if (existingData) {
                }
                else {
                }
            }
        });
    }
    getAllKpisAndValues(business_entity_id, inputValues = {}, overrideFetchMechanism, startDate, endDate) {
        return __awaiter(this, void 0, void 0, function* () {
            let allKpiConfigs = yield this.kpiConfigRepository.getAll();
            allKpiConfigs = yield this.filterKpisByExcludedEquipment(allKpiConfigs, business_entity_id);
            const kpiValues = yield this.calculateKpiValues(allKpiConfigs, business_entity_id, inputValues, overrideFetchMechanism, startDate, endDate);
            const allKpiCodes = allKpiConfigs.map(kpi => kpi.system_code);
            const result = {};
            for (const kpiCode of allKpiCodes) {
                result[kpiCode] = kpiValues[kpiCode];
            }
            return result;
        });
    }
    getKpiDataBySystemCodes(business_entity_id, systemCodes, inputValues = {}, overrideFetchMechanism, startDate, endDate) {
        return __awaiter(this, void 0, void 0, function* () {
            const allKpiConfigs = yield this.kpiConfigRepository.getAll();
            const requestedKpiConfigs = allKpiConfigs.filter(kpi => systemCodes.includes(kpi.system_code));
            if (requestedKpiConfigs.length === 0) {
                throw new Error(`No KPIs found with the provided system codes`);
            }
            const requiredKpiCodes = yield this.findAllDependencies(systemCodes);
            const requiredKpiConfigs = allKpiConfigs.filter(kpi => requiredKpiCodes.has(kpi.system_code));
            const kpiValues = yield this.calculateKpiValues(requiredKpiConfigs, business_entity_id, inputValues, overrideFetchMechanism, startDate, endDate);
            const result = [];
            for (const systemCode of systemCodes) {
                const kpiConfig = requestedKpiConfigs.find(kpi => kpi.system_code === systemCode);
                if (!kpiConfig)
                    continue;
                const kpiDependencies = new Set([systemCode]);
                if (kpiConfig.kpi_type === 'formula') {
                    const kpiConfigObj = typeof kpiConfig.kpi_config === 'string'
                        ? JSON.parse(kpiConfig.kpi_config)
                        : kpiConfig.kpi_config;
                    const formula = (kpiConfigObj === null || kpiConfigObj === void 0 ? void 0 : kpiConfigObj.formula) || '';
                    const directDependencies = (0, kpi_formula_evaluator_helper_1.extractDependentKpisFromFormula)(formula);
                    directDependencies.forEach(depCode => kpiDependencies.add(depCode));
                }
                result.push({
                    kpi_code: systemCode,
                    kpi_config: kpiConfig,
                    current_value: kpiValues[systemCode],
                    dependencies: Array.from(kpiDependencies).filter(code => code !== systemCode),
                    dependency_values: Object.fromEntries(Object.entries(kpiValues).filter(([key]) => kpiDependencies.has(key) && key !== systemCode)),
                });
            }
            return result;
        });
    }
    getKpisByTags(business_entity_id, tags, year, inputValues = {}) {
        return __awaiter(this, void 0, void 0, function* () {
            const allKpiConfigs = yield this.kpiConfigRepository.getAll();
            const filteredKpiConfigs = allKpiConfigs.filter(kpi => {
                const kpiTags = Array.isArray(kpi.tags)
                    ? kpi.tags
                    : typeof kpi.tags === 'string'
                        ? JSON.parse(kpi.tags)
                        : [];
                return tags.some(tag => kpiTags.includes(tag));
            });
            const filteredKpiCodes = filteredKpiConfigs.map(kpi => kpi.system_code);
            const dependentKpiCodes = new Set();
            const formulaKpis = filteredKpiConfigs.filter(kpi => kpi.kpi_type === 'formula');
            for (const kpi of formulaKpis) {
                this.findFormulaDependencies(kpi, allKpiConfigs, dependentKpiCodes);
            }
            const additionalDependencies = Array.from(dependentKpiCodes).filter(code => !filteredKpiCodes.includes(code));
            const dependentKpiConfigs = allKpiConfigs.filter(kpi => additionalDependencies.includes(kpi.system_code));
            const combinedKpiConfigs = [...filteredKpiConfigs, ...dependentKpiConfigs];
            const kpiValues = yield this.calculateKpiValues(combinedKpiConfigs, business_entity_id, inputValues);
            const result = {};
            for (const kpiCode of filteredKpiCodes) {
                result[kpiCode] = kpiValues[kpiCode];
            }
            return result;
        });
    }
    findFormulaDependencies(kpiConfig, allKpiConfigs, dependencySet) {
        const kpiConfigObj = typeof kpiConfig.kpi_config === 'string'
            ? JSON.parse(kpiConfig.kpi_config)
            : kpiConfig.kpi_config;
        const formula = (kpiConfigObj === null || kpiConfigObj === void 0 ? void 0 : kpiConfigObj.formula) || '';
        const dependencies = (0, kpi_formula_evaluator_helper_1.extractDependentKpisFromFormula)(formula);
        dependencies.forEach(depCode => dependencySet.add(depCode));
        let newDependenciesFound = true;
        while (newDependenciesFound) {
            newDependenciesFound = false;
            for (const depCode of dependencySet) {
                const depConfig = allKpiConfigs.find(kpi => kpi.system_code === depCode);
                if (depConfig && depConfig.kpi_type === 'formula') {
                    const depConfigObj = typeof depConfig.kpi_config === 'string'
                        ? JSON.parse(depConfig.kpi_config)
                        : depConfig.kpi_config;
                    const depFormula = (depConfigObj === null || depConfigObj === void 0 ? void 0 : depConfigObj.formula) || '';
                    const nestedDeps = (0, kpi_formula_evaluator_helper_1.extractDependentKpisFromFormula)(depFormula);
                    for (const nestedDep of nestedDeps) {
                        if (!dependencySet.has(nestedDep)) {
                            dependencySet.add(nestedDep);
                            newDependenciesFound = true;
                        }
                    }
                }
            }
        }
    }
    calculateKpiValues(kpiConfigs, business_entity_id, inputValues = {}, overrideFetchMechanism, startDate, endDate) {
        return __awaiter(this, void 0, void 0, function* () {
            const kpiValues = {};
            const inputKpis = kpiConfigs.filter(kpi => kpi.kpi_type === 'input');
            for (const kpi of inputKpis) {
                kpiValues[kpi.system_code] = yield this.calculateInputKpiValue(kpi, inputValues);
            }
            const pullDataKpis = kpiConfigs.filter(kpi => kpi.kpi_type === 'pull_data');
            for (const kpi of pullDataKpis) {
                kpiValues[kpi.system_code] = yield this.calculatePullDataKpiValue(kpi, business_entity_id, overrideFetchMechanism, startDate, endDate);
            }
            const formulaKpis = kpiConfigs.filter(kpi => kpi.kpi_type === 'formula');
            yield this.calculateFormulaKpis(formulaKpis, business_entity_id, kpiValues);
            return kpiValues;
        });
    }
    calculateInputKpiValue(kpi, inputValues) {
        return __awaiter(this, void 0, void 0, function* () {
            let value = inputValues[kpi.system_code];
            if (value === undefined || value === null || isNaN(Number(value))) {
                console.warn(`Invalid value for KPI ${kpi.system_code}: ${value}`);
                return -1;
            }
            return value;
        });
    }
    calculatePullDataKpiValue(kpi, business_entity_id, overrideFetchMechanism, startDate, endDate) {
        return __awaiter(this, void 0, void 0, function* () {
            const kpiConfigObj = typeof kpi.kpi_config === 'string' ? JSON.parse(kpi.kpi_config) : kpi.kpi_config;
            const value = yield this.getKpiDataForFetchMechanism(kpi.system_code, business_entity_id, kpiConfigObj, overrideFetchMechanism, startDate, endDate);
            return this.roundKpiValue(value, kpi);
        });
    }
    detectCircularDependencies(kpiConfigs) {
        const dependencyMap = new Map();
        for (const kpi of kpiConfigs) {
            if (kpi.kpi_type === 'formula') {
                const kpiConfigObj = typeof kpi.kpi_config === 'string'
                    ? JSON.parse(kpi.kpi_config)
                    : kpi.kpi_config;
                const formula = (kpiConfigObj === null || kpiConfigObj === void 0 ? void 0 : kpiConfigObj.formula) || '';
                const dependencies = (0, kpi_formula_evaluator_helper_1.extractDependentKpisFromFormula)(formula);
                dependencyMap.set(kpi.system_code, new Set(dependencies));
            }
            else {
                dependencyMap.set(kpi.system_code, new Set());
            }
        }
        const circularDependencies = new Set();
        const visited = new Set();
        const recursionStack = new Set();
        const dfs = (kpiCode, path = []) => {
            if (visited.has(kpiCode) && !recursionStack.has(kpiCode)) {
                return false;
            }
            if (recursionStack.has(kpiCode)) {
                const cycleStart = path.indexOf(kpiCode);
                const cycle = path.slice(cycleStart).concat(kpiCode);
                console.warn(`Circular dependency detected: ${cycle.join(' -> ')}`);
                cycle.forEach(code => circularDependencies.add(code));
                return true;
            }
            visited.add(kpiCode);
            recursionStack.add(kpiCode);
            path.push(kpiCode);
            const dependencies = dependencyMap.get(kpiCode) || new Set();
            let hasCircular = false;
            for (const dep of dependencies) {
                if (dfs(dep, [...path])) {
                    hasCircular = true;
                }
            }
            recursionStack.delete(kpiCode);
            return hasCircular;
        };
        for (const kpi of kpiConfigs) {
            if (!visited.has(kpi.system_code)) {
                dfs(kpi.system_code);
            }
        }
        return { dependencyMap, circularDependencies };
    }
    calculateFormulaKpis(formulaKpis, business_entity_id, kpiValues) {
        return __awaiter(this, void 0, void 0, function* () {
            const allKpiConfigs = yield this.kpiConfigRepository.getAll();
            const { dependencyMap, circularDependencies } = this.detectCircularDependencies(allKpiConfigs);
            if (circularDependencies.size > 0) {
                console.warn(`Found ${circularDependencies.size} KPIs with circular dependencies: ${Array.from(circularDependencies).join(', ')}`);
            }
            const calculatedKpis = new Set(Object.keys(kpiValues).filter(key => kpiValues[key] !== undefined));
            const pendingKpis = new Set(formulaKpis.map(kpi => kpi.system_code));
            const graph = new Map();
            const inDegree = new Map();
            for (const kpiCode of pendingKpis) {
                const dependencies = Array.from(dependencyMap.get(kpiCode) || new Set());
                graph.set(kpiCode, dependencies);
                const pendingDependencies = dependencies.filter(dep => pendingKpis.has(dep) && !calculatedKpis.has(dep));
                inDegree.set(kpiCode, pendingDependencies.length);
            }
            for (const kpiCode of pendingKpis) {
                if (circularDependencies.has(kpiCode)) {
                    const kpi = formulaKpis.find(k => k.system_code === kpiCode);
                    if (!kpi)
                        continue;
                    console.warn(`Resolving circular dependency for KPI ${kpiCode} using default value -1`);
                    kpiValues[kpiCode] = -1;
                    pendingKpis.delete(kpiCode);
                    calculatedKpis.add(kpiCode);
                    for (const [dependent, dependencies] of graph.entries()) {
                        if (dependencies.includes(kpiCode) && inDegree.has(dependent)) {
                            inDegree.set(dependent, inDegree.get(dependent) - 1);
                        }
                    }
                    console.info(`Resolved circular dependency for KPI ${kpiCode} using default value: -1`);
                }
            }
            const queue = [];
            for (const [kpiCode, degree] of inDegree.entries()) {
                if (degree === 0 && pendingKpis.has(kpiCode)) {
                    queue.push(kpiCode);
                }
            }
            let iterations = 0;
            const maxIterations = pendingKpis.size * 2;
            while (queue.length > 0 && pendingKpis.size > 0 && iterations < maxIterations) {
                iterations++;
                const kpiCode = queue.shift();
                if (!kpiCode || !pendingKpis.has(kpiCode))
                    continue;
                const kpi = formulaKpis.find(k => k.system_code === kpiCode);
                if (!kpi)
                    continue;
                const allDependencies = Array.from(dependencyMap.get(kpiCode) || new Set());
                const allDependenciesCalculated = allDependencies.every((depCode) => calculatedKpis.has(depCode) || kpiValues[depCode] !== undefined);
                if (allDependenciesCalculated) {
                    const kpiConfigObj = typeof kpi.kpi_config === 'string'
                        ? JSON.parse(kpi.kpi_config)
                        : kpi.kpi_config;
                    const formula = (kpiConfigObj === null || kpiConfigObj === void 0 ? void 0 : kpiConfigObj.formula) || '';
                    console.debug(`Calculating formula for KPI ${kpiCode}: ${formula}`);
                    console.debug(`Dependencies for KPI ${kpiCode}:`, Object.fromEntries(allDependencies.map((dep) => [dep, kpiValues[dep]])));
                    let result = (0, kpi_formula_evaluator_helper_1.evaluateKpiFormula)(formula, kpiValues);
                    if (result === -1 || isNaN(result)) {
                        console.warn(`Formula evaluation failed for KPI ${kpiCode}. Using default value -1.`);
                        result = -1;
                    }
                    result = this.roundKpiValue(result, kpi);
                    console.debug(`Calculated value for KPI ${kpiCode}: ${result}`);
                    kpiValues[kpiCode] = result;
                    pendingKpis.delete(kpiCode);
                    calculatedKpis.add(kpiCode);
                    for (const [dependent, dependencies] of graph.entries()) {
                        if (dependencies.includes(kpiCode) && inDegree.has(dependent)) {
                            const newDegree = inDegree.get(dependent) - 1;
                            inDegree.set(dependent, newDegree);
                            if (newDegree === 0 && pendingKpis.has(dependent)) {
                                queue.push(dependent);
                            }
                        }
                    }
                }
                else {
                    const missingDependencies = allDependencies.filter((depCode) => !calculatedKpis.has(depCode) && kpiValues[depCode] === undefined);
                    console.debug(`KPI ${kpiCode} is waiting for dependencies: ${missingDependencies.join(', ')}`);
                    const pendingDependencies = missingDependencies.filter((dep) => pendingKpis.has(dep));
                    if (pendingDependencies.length > 0) {
                        queue.push(kpiCode);
                    }
                    else {
                        console.warn(`KPI ${kpiCode} has missing external dependencies: ${missingDependencies.join(', ')}. Using default value -1.`);
                        kpiValues[kpiCode] = -1;
                        pendingKpis.delete(kpiCode);
                        calculatedKpis.add(kpiCode);
                        for (const [dependent, dependencies] of graph.entries()) {
                            if (dependencies.includes(kpiCode) && inDegree.has(dependent)) {
                                const newDegree = inDegree.get(dependent) - 1;
                                inDegree.set(dependent, newDegree);
                                if (newDegree === 0 && pendingKpis.has(dependent)) {
                                    queue.push(dependent);
                                }
                            }
                        }
                    }
                }
            }
            if (iterations >= maxIterations && pendingKpis.size > 0) {
                console.warn(`Reached maximum iterations (${maxIterations}) while calculating KPIs. ` +
                    `Unable to calculate ${pendingKpis.size} KPIs: ${Array.from(pendingKpis).join(', ')}`);
                for (const kpiCode of pendingKpis) {
                    kpiValues[kpiCode] = -1;
                }
            }
        });
    }
    roundKpiValue(value, kpi) {
        if (value === undefined || value === null || isNaN(Number(value))) {
            console.warn(`Invalid value for KPI ${kpi.system_code}: ${value}`);
            return -1;
        }
        const numValue = Number(value);
        try {
            const kpiConfig = typeof kpi.kpi_config === 'string' ? JSON.parse(kpi.kpi_config) : kpi.kpi_config;
            const decimalPlaces = (kpiConfig === null || kpiConfig === void 0 ? void 0 : kpiConfig.decimal_places) !== undefined ? parseInt(kpiConfig.decimal_places, 10) : 0;
            if (isNaN(decimalPlaces))
                return numValue;
            const factor = Math.pow(10, decimalPlaces);
            return Math.round(numValue * factor) / factor;
        }
        catch (error) {
            console.error('Error rounding KPI value:', error);
            return numValue;
        }
    }
    aggregateKpiValues(values, method) {
        const validValues = values.filter(v => v !== undefined);
        if (validValues.length === 0)
            return 0;
        switch (method.toUpperCase()) {
            case 'SUM':
                return validValues.reduce((sum, val) => sum + val, 0);
            case 'AVG':
                return validValues.reduce((sum, val) => sum + val, 0) / validValues.length;
            case 'MAX':
                return Math.max(...validValues);
            case 'MIN':
                return Math.min(...validValues);
            case 'COUNT':
                return validValues.length;
            case 'LAST':
                return validValues[validValues.length - 1];
            case 'FIRST':
                return validValues[0];
            default:
                return validValues.reduce((sum, val) => sum + val, 0);
        }
    }
    getKpiDataForFetchMechanism(kpiCode, business_entity_id, kpiConfig, overrideFetchMechanism, startDate, endDate) {
        return __awaiter(this, void 0, void 0, function* () {
            const currentYear = new Date().getFullYear();
            const currentMonth = new Date().getMonth() + 1;
            const aggregateMethod = (kpiConfig === null || kpiConfig === void 0 ? void 0 : kpiConfig.aggregate_method) || 'SUM';
            const monthlyAggregateMethod = (kpiConfig === null || kpiConfig === void 0 ? void 0 : kpiConfig.monthly_aggregate_method) || 'SUM';
            const dataType = (kpiConfig === null || kpiConfig === void 0 ? void 0 : kpiConfig.data_type) || 'Month';
            const source_kpi = (kpiConfig === null || kpiConfig === void 0 ? void 0 : kpiConfig.source_kpi) || kpiCode;
            let fetchMechanism = overrideFetchMechanism && overrideFetchMechanism !== 'default'
                ? overrideFetchMechanism
                : (kpiConfig === null || kpiConfig === void 0 ? void 0 : kpiConfig.fetch_mechanism) || 'year_to_date';
            const sequlizeOperator = new sequlize_operator_helper_1.SequlizeOperator();
            let whereCondition = {
                kpi_code: source_kpi,
                business_entity_id: business_entity_id,
            };
            switch (fetchMechanism) {
                case 'last_12_month':
                    const last12MonthsStart = new Date();
                    last12MonthsStart.setMonth(last12MonthsStart.getMonth() - 12);
                    const startYear12 = last12MonthsStart.getFullYear();
                    const startMonth12 = last12MonthsStart.getMonth() + 1;
                    whereCondition = Object.assign({ kpi_code: source_kpi, business_entity_id: business_entity_id }, sequlizeOperator.orOperator([
                        {
                            kpi_year: sequlizeOperator.betweenOperator(startYear12 + 1, currentYear - 1),
                        },
                        {
                            kpi_year: startYear12,
                            kpi_month: sequlizeOperator.greaterThanEqualOperator(startMonth12),
                        },
                        {
                            kpi_year: currentYear,
                            kpi_month: sequlizeOperator.lessThanEqualOperator(currentMonth),
                        },
                    ]));
                    break;
                case 'year_to_date':
                    whereCondition = {
                        kpi_code: source_kpi,
                        business_entity_id: business_entity_id,
                        kpi_year: currentYear,
                        kpi_month: sequlizeOperator.lessThanEqualOperator(currentMonth),
                    };
                    break;
                case 'last_year':
                    whereCondition = {
                        kpi_code: source_kpi,
                        business_entity_id: business_entity_id,
                        kpi_year: currentYear - 1,
                    };
                    break;
                case 'custom':
                    if (!startDate || !endDate) {
                        throw new Error('Start date and end date are required for custom fetch mechanism');
                    }
                    const startYear = new Date(startDate).getFullYear();
                    const startMonth = new Date(startDate).getMonth() + 1;
                    const endYear = new Date(endDate).getFullYear();
                    const endMonth = new Date(endDate).getMonth() + 1;
                    if (startYear === endYear) {
                        whereCondition = {
                            kpi_code: source_kpi,
                            business_entity_id: business_entity_id,
                            kpi_year: startYear,
                            kpi_month: sequlizeOperator.betweenOperator(startMonth, endMonth),
                        };
                    }
                    else {
                        whereCondition = Object.assign({ kpi_code: source_kpi, business_entity_id: business_entity_id }, sequlizeOperator.orOperator([
                            {
                                kpi_year: sequlizeOperator.betweenOperator(startYear + 1, endYear - 1),
                            },
                            {
                                kpi_year: startYear,
                                kpi_month: sequlizeOperator.greaterThanEqualOperator(startMonth),
                            },
                            {
                                kpi_year: endYear,
                                kpi_month: sequlizeOperator.lessThanEqualOperator(endMonth),
                            },
                        ]));
                    }
                    break;
                case 'last_value':
                    const lastValue = yield this.kpiDataRepository.findOne({
                        where: {
                            kpi_code: source_kpi,
                            business_entity_id: business_entity_id,
                        },
                        order: [
                            ['kpi_year', 'DESC'],
                            ['kpi_month', 'DESC'],
                            ['kpi_day', 'DESC'],
                        ],
                    });
                    return (lastValue === null || lastValue === void 0 ? void 0 : lastValue.kpi_value) !== undefined ? Number(lastValue.kpi_value) : 0;
                default:
                    whereCondition = {
                        kpi_code: source_kpi,
                        business_entity_id: business_entity_id,
                        kpi_year: currentYear,
                    };
            }
            if (dataType === 'Year' || dataType === 'Day') {
                const result = yield this.kpiDataRepository.findOneRaw({
                    attributes: [[sequelize_1.Sequelize.fn(aggregateMethod, sequelize_1.Sequelize.col('kpi_value')), 'kpi_value']],
                    where: whereCondition,
                });
                return (result === null || result === void 0 ? void 0 : result.kpi_value) !== undefined ? Number(result.kpi_value) : 0;
            }
            else if (dataType === 'Month') {
                try {
                    const monthlyData = yield this.kpiDataRepository.findAllRaw({
                        attributes: [
                            'kpi_month',
                            [sequelize_1.Sequelize.fn(monthlyAggregateMethod, sequelize_1.Sequelize.col('kpi_value')), 'monthly_value'],
                        ],
                        where: whereCondition,
                        group: ['kpi_month'],
                    });
                    if (!monthlyData || monthlyData.length === 0) {
                        return 0;
                    }
                    const monthlyValues = monthlyData.map(item => {
                        const value = item.monthly_value;
                        return value !== undefined && value !== null ? Number(value) : 0;
                    });
                    switch (aggregateMethod.toUpperCase()) {
                        case 'SUM':
                            return monthlyValues.reduce((sum, val) => sum + val, 0);
                        case 'AVG':
                            return monthlyValues.reduce((sum, val) => sum + val, 0) / monthlyValues.length;
                        case 'MAX':
                            return Math.max(...monthlyValues);
                        case 'MIN':
                            return Math.min(...monthlyValues);
                        case 'COUNT':
                            return monthlyValues.length;
                        case 'LAST':
                            return monthlyValues[monthlyValues.length - 1];
                        case 'FIRST':
                            return monthlyValues[0];
                        default:
                            return monthlyValues.reduce((sum, val) => sum + val, 0);
                    }
                }
                catch (error) {
                    console.error('Error fetching monthly data:', error);
                    return 0;
                }
            }
            else {
                return -1;
            }
        });
    }
    findAllDependencies(kpiCodes) {
        return __awaiter(this, void 0, void 0, function* () {
            const allKpiConfigs = yield this.kpiConfigRepository.getAll();
            const requiredKpiCodes = new Set(kpiCodes);
            const dependencyMap = new Map();
            for (const kpi of allKpiConfigs) {
                if (kpi.kpi_type === 'formula') {
                    const kpiConfig = typeof kpi.kpi_config === 'string'
                        ? JSON.parse(kpi.kpi_config)
                        : kpi.kpi_config;
                    const formula = (kpiConfig === null || kpiConfig === void 0 ? void 0 : kpiConfig.formula) || '';
                    const dependencies = (0, kpi_formula_evaluator_helper_1.extractDependentKpisFromFormula)(formula);
                    dependencyMap.set(kpi.system_code, dependencies);
                }
                else {
                    dependencyMap.set(kpi.system_code, []);
                }
            }
            const visited = new Set();
            const findDependencies = (kpiCode) => {
                if (visited.has(kpiCode))
                    return;
                visited.add(kpiCode);
                const dependencies = dependencyMap.get(kpiCode) || [];
                for (const depCode of dependencies) {
                    requiredKpiCodes.add(depCode);
                    findDependencies(depCode);
                }
            };
            for (const kpiCode of kpiCodes) {
                findDependencies(kpiCode);
            }
            return requiredKpiCodes;
        });
    }
    filterKpisByExcludedEquipment(kpiConfigs, business_entity_id) {
        return __awaiter(this, void 0, void 0, function* () {
            const buSetup = yield this.buSetupRepository.findOneRaw({
                where: { entity_id: business_entity_id },
            });
            if (!buSetup || !buSetup.excluded_equipment_type) {
                return kpiConfigs;
            }
            const excludedEquipmentTypes = Array.isArray(buSetup.excluded_equipment_type)
                ? buSetup.excluded_equipment_type
                : JSON.parse(buSetup.excluded_equipment_type);
            return kpiConfigs.filter((kpi) => {
                const kpiEquipmentTypes = Array.isArray(kpi.applicable_equipment_type)
                    ? kpi.applicable_equipment_type
                    : JSON.parse(kpi.applicable_equipment_type || '[]');
                return !kpiEquipmentTypes.some(equipmentType => excludedEquipmentTypes.includes(equipmentType));
            });
        });
    }
};
KpiCalculatorService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [repositories_1.KpiConfigRepository,
        repositories_1.KpiDataRepository,
        repositories_1.BuSetupRepository])
], KpiCalculatorService);
exports.KpiCalculatorService = KpiCalculatorService;
//# sourceMappingURL=kpi-calculator.service.js.map