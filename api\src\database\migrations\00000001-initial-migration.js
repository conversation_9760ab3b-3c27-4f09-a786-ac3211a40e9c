'use strict';
const dotenv = require('dotenv');
const fs = require('fs');
const env = dotenv.parse(fs.readFileSync('.env'));

module.exports = {
	up: async (queryInterface, Sequelize) => {
		await queryInterface.createTable(
			{ schema: env['DB_SCHEMA'], tableName: 'afe_budget_type_mappings' },
			{
				id: {
					type: Sequelize.DataTypes.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				afe_type_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: true,
				},
				afe_request_type_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: false,
				},
				budget_type_ids: {
					type: Sequelize.DataTypes.JSONB,
					allowNull: true,
				},
				created_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				created_by: {
					type: Sequelize.DataTypes.STRING,
				},
				updated_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				updated_by: {
					type: Sequelize.DataTypes.STRING,
				},
				deleted: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				active: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: true,
				},
			},
		);
		await queryInterface.createTable(
			{ schema: env['DB_SCHEMA'], tableName: 'afe_budget_types' },
			{
				id: {
					type: Sequelize.DataTypes.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				title: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				mixed: {
					type: Sequelize.DataTypes.BOOLEAN,
				},
				created_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				created_by: {
					type: Sequelize.DataTypes.STRING,
				},
				updated_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				updated_by: {
					type: Sequelize.DataTypes.STRING,
					allowNull: true,
				},
				deleted: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				active: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: true,
				},
			},
		);
		await queryInterface.createTable(
			{ schema: env['DB_SCHEMA'], tableName: 'afe_commitment_length' },
			{
				id: {
					type: Sequelize.DataTypes.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				title: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				year: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: false,
				},
				created_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				created_by: {
					type: Sequelize.DataTypes.STRING,
				},
				updated_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				updated_by: {
					type: Sequelize.DataTypes.STRING,
				},
				deleted: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				active: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: true,
				},
			},
		);
		await queryInterface.createTable(
			{ schema: env['DB_SCHEMA'], tableName: 'afe_nature_type_mappings' },
			{
				id: {
					type: Sequelize.DataTypes.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				afe_request_type_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: false,
				},
				afe_nature_type_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: false,
				},
				included_entity_ids: {
					type: Sequelize.DataTypes.JSONB,
					allowNull: true,
				},
				excluded_entity_ids: {
					type: Sequelize.DataTypes.JSONB,
					allowNull: true,
				},
				created_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				created_by: {
					type: Sequelize.DataTypes.STRING,
				},
				updated_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				updated_by: {
					type: Sequelize.DataTypes.STRING,
				},
				deleted: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				active: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: true,
				},
			},
		);
		await queryInterface.createTable(
			{ schema: env['DB_SCHEMA'], tableName: 'afe_nature_types' },
			{
				id: {
					type: Sequelize.DataTypes.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				title: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				created_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				created_by: {
					type: Sequelize.DataTypes.STRING,
				},
				updated_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				updated_by: {
					type: Sequelize.DataTypes.STRING,
				},
				deleted: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				active: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: true,
				},
			},
		);
		await queryInterface.createTable(
			{ schema: env['DB_SCHEMA'], tableName: 'afe_request_types' },
			{
				id: {
					type: Sequelize.DataTypes.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				title: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				supplimental_allowed: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				created_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				created_by: {
					type: Sequelize.DataTypes.STRING,
				},
				updated_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				updated_by: {
					type: Sequelize.DataTypes.STRING,
				},
				deleted: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				active: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: true,
				},
			},
		);
		await queryInterface.createTable(
			{ schema: env['DB_SCHEMA'], tableName: 'afe_types' },
			{
				id: {
					type: Sequelize.DataTypes.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				title: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				request_type_ids: {
					type: Sequelize.DataTypes.JSONB,
					allowNull: true,
				},
				created_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				created_by: {
					type: Sequelize.DataTypes.STRING,
				},
				updated_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				updated_by: {
					type: Sequelize.DataTypes.STRING,
				},
				deleted: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				active: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: true,
				},
			},
		);
		await queryInterface.createTable(
			{ schema: env['DB_SCHEMA'], tableName: 'afe_proposals' },
			{
				id: {
					type: Sequelize.DataTypes.BIGINT,
					autoIncrement: true,
					primaryKey: true,
				},
				name: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				category: {
					type: Sequelize.DataTypes.ENUM('NEW', 'SUPPLEMENTAL'),
					allowNull: false,
				},
				afe_request_type_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: false,
				},
				version: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: false,
					defaultValue: 1,
				},
				is_new_version_in_progress: {
					type: Sequelize.DataTypes.BOOLEAN,
					allowNull: false,
					defaultValue: false,
				},
				submitter_id: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				is_approved_by_board: {
					type: Sequelize.DataTypes.BOOLEAN,
					allowNull: false,
				},
				budget_type: {
					type: Sequelize.DataTypes.ENUM('BUDGETED', 'UNBUDGETED', 'MIXED'),
					allowNull: true,
				},
				currency_type: {
					type: Sequelize.DataTypes.ENUM('USD', 'AED'),
					allowNull: false,
				},
				total_amount: {
					type: Sequelize.DataTypes.FLOAT,
					allowNull: false,
				},
				additional_currency_amount: {
					type: Sequelize.DataTypes.JSONB,
					allowNull: true,
				},
				entity_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: false,
				},
				entity_code: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				entity_title: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				year_of_commitment: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: true,
				},
				data: {
					type: Sequelize.DataTypes.JSONB,
					allowNull: false,
				},
				global_procurement_ques_ans: {
					type: Sequelize.DataTypes.JSONB,
					allowNull: true,
				},
				supplemental_data: {
					type: Sequelize.DataTypes.JSONB,
					allowNull: true,
				},
				parent_afe_id: {
					type: Sequelize.DataTypes.BIGINT,
					allowNull: true,
				},
				project_reference_number: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				internal_status: {
					type: Sequelize.DataTypes.ENUM('SUBMITTED', 'IN_PROGRESS', 'APPROVED', 'REJECTED', 'SENT_BACK', 'CANCELLED'),
					allowNull: false,
					defaultValue: 'SUBMITTED',
				},
				user_status: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
					defaultValue: 'Submitted',
				},
				market_value_currency: {
					type: Sequelize.DataTypes.ENUM('USD', 'AED'),
					allowNull: true,
				},
				market_value: {
					type: Sequelize.DataTypes.FLOAT,
					allowNull: true,
				},
				market_value_additional_currency_amount: {
					type: Sequelize.DataTypes.JSONB,
					allowNull: true,
				},
				readers: {
					type: Sequelize.DataTypes.JSONB,
					allowNull: true,
				},
				subscribers: {
					type: Sequelize.DataTypes.JSONB,
					allowNull: true,
				},
				created_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				created_by: {
					type: Sequelize.DataTypes.STRING,
				},
				updated_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				updated_by: {
					type: Sequelize.DataTypes.STRING,
				},
				deleted: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				active: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: true,
				},
			},
		);
		await queryInterface.createTable(
			{ schema: env['DB_SCHEMA'], tableName: 'afe_proposal_amount_splits' },
			{
				id: {
					type: Sequelize.DataTypes.BIGINT,
					autoIncrement: true,
					primaryKey: true,
				},
				object_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: true,
				},
				object_title: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				afe_proposal_id: {
					type: Sequelize.DataTypes.BIGINT,
					allowNull: false,
				},
				type: {
					type: Sequelize.DataTypes.ENUM(
						'COST_CENTER_SPLIT',
						'PROJECT_COMPONENT_SPLIT',
						'BUDGET_REFERENCE_SPLIT',
						'NATURAL_ACCOUNT_SPLIT',
						'BUDGET_TYPE_SPLIT',
						'GL_CODE_SPLIT',
						'ANALYSIS_CODE_SPLIT',
					),
					allowNull: false,
				},
				currency_type: {
					type: Sequelize.DataTypes.ENUM('USD', 'AED'),
					allowNull: false,
				},
				amount: {
					type: Sequelize.DataTypes.FLOAT,
					allowNull: false,
				},
				additional_currency_amount: {
					type: Sequelize.DataTypes.JSONB,
					allowNull: true,
				},
				budget_type: {
					type: Sequelize.DataTypes.ENUM('BUDGETED', 'UNBUDGETED', 'MIXED'),
					allowNull: true,
				},
				created_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				created_by: {
					type: Sequelize.DataTypes.STRING,
				},
				updated_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				updated_by: {
					type: Sequelize.DataTypes.STRING,
				},
				deleted: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				active: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: true,
				},
			},
		);
		await queryInterface.createTable(
			{ schema: env['DB_SCHEMA'], tableName: 'afe_proposal_approvers' },
			{
				id: {
					type: Sequelize.DataTypes.BIGINT,
					autoIncrement: true,
					primaryKey: true,
				},
				afe_proposal_id: {
					type: Sequelize.DataTypes.BIGINT,
					allowNull: true,
				},
				title: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				assigned_level: {
					type: Sequelize.DataTypes.STRING,
					allowNull: true,
				},
				assigned_to: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				assgined_type: {
					type: Sequelize.DataTypes.ENUM('ROLE', 'COST_CENTER', 'USER'),
					allowNull: false,
				},
				assigned_entity_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: true,
				},
				associated_column: {
					type: Sequelize.DataTypes.ENUM('DEPARTMENT_HEAD', 'SECTION_HEAD'),
					allowNull: true,
				},
				parallel_identifier: {
					type: Sequelize.DataTypes.STRING,
					allowNull: true,
				},
				approval_sequence: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: false,
				},
				assigned_at: {
					type: 'TIMESTAMP',
					allowNull: false,
				},
				user_detail: {
					type: Sequelize.DataTypes.JSONB,
					allowNull: true,
				},
				comment: {
					type: Sequelize.DataTypes.TEXT,
					allowNull: true,
				},
				action_by: {
					type: Sequelize.DataTypes.STRING,
					allowNull: true,
				},
				action_date: {
					type: 'TIMESTAMP',
					allowNull: true,
				},
				other_info: {
					type: Sequelize.DataTypes.JSONB,
					allowNull: true,
				},
				workflow_master_steps_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: true,
				},
				original_approver_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: true,
				},
				action_status: {
					type: Sequelize.DataTypes.ENUM(
						'NOT_INITIATED',
						'IN_PROGRESS',
						'APPROVED',
						'AUTO_APPROVED',
						'REJECTED',
						'SEND_BACK',
						'DELEGATED',
						'REASSIGNED',
						'MORE_DETAIL',
						'MORE_DETAIL_SUBMITTED',
						'DISCARDED',
						'RESUBMITTED'
					),
					defaultValue: 'NOT_INITIATED',
					allowNull: false,
				},
				created_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				created_by: {
					type: Sequelize.DataTypes.STRING,
				},
				updated_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				updated_by: {
					type: Sequelize.DataTypes.STRING,
				},
				deleted: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				active: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: true,
				},
			},
		);
		await queryInterface.createTable(
			{ schema: env['DB_SCHEMA'], tableName: 'afe_proposal_limit_deduction' },
			{
				id: {
					type: Sequelize.DataTypes.BIGINT,
					autoIncrement: true,
					primaryKey: true,
				},
				afe_proposal_id: {
					type: Sequelize.DataTypes.BIGINT,
					allowNull: true,
				},
				entity_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: true,
				},
				entity_code: {
					type: Sequelize.DataTypes.STRING,
					allowNull: true,
				},
				entity_title: {
					type: Sequelize.DataTypes.STRING,
					allowNull: true,
				},
				workflow_master_setting_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: false,
				},
				workflow_master_step_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: false,
				},
				cost_center_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: true,
				},
				amount: {
					type: Sequelize.DataTypes.FLOAT,
					allowNull: false,
				},
				status: {
					type: Sequelize.DataTypes.ENUM('IN_PROGRESS', 'APPROVED', 'REJECTED', 'CANCELLED'),
					allowNull: false,
				},
				parent_id: {
					type: Sequelize.DataTypes.BIGINT,
					allowNull: true,
				},
				data: {
					type: Sequelize.DataTypes.JSONB,
					allowNull: false,
				},
				created_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				created_by: {
					type: Sequelize.DataTypes.STRING,
				},
				updated_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				updated_by: {
					type: Sequelize.DataTypes.STRING,
				},
				deleted: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				active: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: true,
				},
			},
		);
		await queryInterface.createTable(
			{ schema: env['DB_SCHEMA'], tableName: 'questions' },
			{
				id: {
					type: Sequelize.DataTypes.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				question: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				compulsory: {
					type: Sequelize.DataTypes.BOOLEAN,
				},
				included_entity_ids: {
					type: Sequelize.DataTypes.JSONB,
					allowNull: true,
				},
				afe_request_type_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: false,
				},
				created_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				created_by: {
					type: Sequelize.DataTypes.STRING,
				},
				updated_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				updated_by: {
					type: Sequelize.DataTypes.STRING,
				},
				deleted: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				active: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: true,
				},
			},
		);
		await queryInterface.createTable(
			{ schema: env['DB_SCHEMA'], tableName: 'company_codes' },
			{
				id: {
					type: Sequelize.DataTypes.BIGINT,
					autoIncrement: true,
					primaryKey: true,
				},
				code: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				name: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				entity_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: false,
				},
				entity_code: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				entity_type: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				created_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				created_by: {
					type: Sequelize.DataTypes.STRING,
				},
				updated_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				updated_by: {
					type: Sequelize.DataTypes.STRING,
				},
				deleted: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				active: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: true,
				},
			},
		);
		await queryInterface.createTable(
			{ schema: env['DB_SCHEMA'], tableName: 'cost_centers' },
			{
				id: {
					type: Sequelize.DataTypes.BIGINT,
					autoIncrement: true,
					primaryKey: true,
				},
				code: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				name: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				company_code_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: false,
				},
				department_head: {
					type: Sequelize.DataTypes.JSONB,
					allowNull: true,
				},
				section_head: {
					type: Sequelize.DataTypes.JSONB,
					allowNull: true,
				},
				operating: {
					type: Sequelize.DataTypes.BOOLEAN,
				},
				created_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				created_by: {
					type: Sequelize.DataTypes.STRING,
				},
				updated_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				updated_by: {
					type: Sequelize.DataTypes.STRING,
				},
				deleted: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				active: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: true,
				},
			},
		);
		await queryInterface.createTable(
			{ schema: env['DB_SCHEMA'], tableName: 'currency_types' },
			{
				id: {
					type: Sequelize.DataTypes.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				currency: {
					type: Sequelize.DataTypes.ENUM('USD', 'AED'),
					allowNull: false,
				},
				conversion_rate: {
					type: Sequelize.DataTypes.FLOAT,
					allowNull: false,
				},
				primary: {
					type: Sequelize.DataTypes.BOOLEAN,
				},
				created_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				created_by: {
					type: Sequelize.DataTypes.STRING,
				},
				updated_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				updated_by: {
					type: Sequelize.DataTypes.STRING,
				},
				deleted: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				active: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: true,
				},
			},
		);
		await queryInterface.createTable(
			{ schema: env['DB_SCHEMA'], tableName: 'natural_account_numbers' },
			{
				id: {
					type: Sequelize.DataTypes.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				title: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				number: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				company_code_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: true,
				},
				request_type_ids: {
					type: Sequelize.DataTypes.JSONB,
					allowNull: false,
				},
				created_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				created_by: {
					type: Sequelize.DataTypes.STRING,
				},
				updated_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				updated_by: {
					type: Sequelize.DataTypes.STRING,
				},
				deleted: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				active: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: true,
				},
			},
		);
		await queryInterface.createTable(
			{ schema: env['DB_SCHEMA'], tableName: 'analysis_codes' },
			{
				id: {
					type: Sequelize.DataTypes.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				title: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				code: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				company_code_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: true,
				},
				request_type_ids: {
					type: Sequelize.DataTypes.JSONB,
					allowNull: false,
				},
				created_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				created_by: {
					type: Sequelize.DataTypes.STRING,
				},
				updated_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				updated_by: {
					type: Sequelize.DataTypes.STRING,
				},
				deleted: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				active: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: true,
				},
			},
		);
		await queryInterface.createTable(
			{ schema: env['DB_SCHEMA'], tableName: 'entity_setups' },
			{
				id: {
					type: Sequelize.DataTypes.BIGINT,
					autoIncrement: true,
					primaryKey: true,
				},
				entity_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: false,
				},
				entity_code: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				currency_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: true,
				},
				created_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				created_by: {
					type: Sequelize.DataTypes.STRING,
				},
				updated_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				updated_by: {
					type: Sequelize.DataTypes.STRING,
				},
				deleted: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				active: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: true,
				},
			},
		);
		await queryInterface.createTable(
			{ schema: env['DB_SCHEMA'], tableName: 'project_components' },
			{
				id: {
					type: Sequelize.DataTypes.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				title: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				has_child: {
					allowNull: false,
					type: Sequelize.DataTypes.BOOLEAN,
				},
				length_of_commitment_enabled: {
					allowNull: false,
					type: Sequelize.DataTypes.BOOLEAN,
				},
				included_entity_ids: {
					type: Sequelize.DataTypes.JSONB,
					allowNull: true,
				},
				excluded_entity_ids: {
					type: Sequelize.DataTypes.JSONB,
					allowNull: true,
				},
				afe_request_type_ids: {
					type: Sequelize.DataTypes.JSONB,
					allowNull: false,
				},
				parent_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: true,
				},
				created_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				created_by: {
					type: Sequelize.DataTypes.STRING,
				},
				updated_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				updated_by: {
					type: Sequelize.DataTypes.STRING,
				},
				deleted: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				active: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: true,
				},
			},
		);
		await queryInterface.createTable(
			{ schema: env['DB_SCHEMA'], tableName: 'vaction_setup' },
			{
				id: {
					type: Sequelize.DataTypes.BIGINT,
					autoIncrement: true,
					primaryKey: true,
				},
				user_id: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				start_date: {
					type: 'TIMESTAMP',
					allowNull: false,
				},
				end_date: {
					type: 'TIMESTAMP',
					allowNull: false,
				},
				delegate_user_id: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				delegate_type: {
					type: Sequelize.DataTypes.ENUM('ROLE_DELEGATION', 'USER_DELEGATION'),
					allowNull: false,
				},
				source_role: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				source_location_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: false,
				},
				source_location_code: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				created_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				created_by: {
					type: Sequelize.DataTypes.STRING,
				},
				updated_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				updated_by: {
					type: Sequelize.DataTypes.STRING,
				},
				deleted: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				active: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: true,
				},
			},
		);
		await queryInterface.createTable(
			{ schema: env['DB_SCHEMA'], tableName: 'workflow_master_settings' },
			{
				id: {
					type: Sequelize.DataTypes.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				request_type_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: true,
				},
				budget_type: {
					type: Sequelize.DataTypes.ENUM('BUDGETED', 'UNBUDGETED', 'MIXED'),
					allowNull: true,
				},
				project_component_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: true,
				},
				entity_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: true,
				},
				entity_code: {
					type: Sequelize.DataTypes.STRING,
					allowNull: true,
				},
				entity_title: {
					type: Sequelize.DataTypes.STRING,
					allowNull: true,
				},
				entity_type: {
					type: Sequelize.DataTypes.STRING,
					allowNull: true,
				},
				parent_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: true,
				},
				year: {
					type: Sequelize.DataTypes.INTEGER,
				},
				is_commitment_length_applicable: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				is_aggregate_limit_applicable: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				published: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				version: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: false,
					defaultValue: 1
				},
				unpublished_version: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: true
				},
				created_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				created_by: {
					type: Sequelize.DataTypes.STRING,
				},
				updated_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				updated_by: {
					type: Sequelize.DataTypes.STRING,
				},
				deleted: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				active: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: true,
				},
			},
		);
		await queryInterface.createTable(
			{ schema: env['DB_SCHEMA'], tableName: 'workflow_master_steps' },
			{
				id: {
					type: Sequelize.DataTypes.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				workflow_master_step_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: true,
				},
				title: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				workflow_master_setting_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: true,
				},
				associate_level: {
					type: Sequelize.DataTypes.STRING,
					allowNull: true,
				},
				associate_role: {
					type: Sequelize.DataTypes.STRING,
					allowNull: true,
				},
				associate_type: {
					type: Sequelize.DataTypes.ENUM('ROLE', 'COST_CENTER', 'USER'),
					allowNull: true,
				},
				associated_column: {
					type: Sequelize.DataTypes.ENUM('DEPARTMENT_HEAD', 'SECTION_HEAD'),
					allowNull: true,
				},
				associated_user: {
					type: Sequelize.DataTypes.STRING,
					allowNull: true,
				},
				parallel_identifier: {
					type: Sequelize.DataTypes.STRING,
					allowNull: true,
				},
				single_limit: {
					type: Sequelize.DataTypes.FLOAT,
					allowNull: true,
				},
				aggregate_limit: {
					type: Sequelize.DataTypes.FLOAT,
					allowNull: true,
				},
				length_of_commitment: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: true,
				},
				version: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: false,
					defaultValue: 1
				},
				workflow_year: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: false,
				},
				skip_limit_rule_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: true,
				},
				can_workflow_start: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				inherited: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: true,
				},
				is_mandatory: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				can_removed_at_child_level: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				can_share_limit_to_child: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				remove_child_limit: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				shared_limit_master_step_child_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: true,
				},
				approval_sequence: {
					type: Sequelize.DataTypes.INTEGER,
				},
				rule_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: true,
				},
				created_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				created_by: {
					type: Sequelize.DataTypes.STRING,
				},
				updated_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				updated_by: {
					type: Sequelize.DataTypes.STRING,
				},
				deleted: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				active: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: true,
				},
			},
		);
		await queryInterface.createTable(
			{ schema: env['DB_SCHEMA'], tableName: 'workflow_shared_bucket_limits' },
			{
				id: {
					type: Sequelize.DataTypes.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				entity_code: {
					type: Sequelize.DataTypes.STRING,
				},
				bucket_entity_id: {
					type: Sequelize.DataTypes.INTEGER,
				},
				bucket_entity_code: {
					type: Sequelize.DataTypes.STRING,
				},
				workflow_master_setting_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: true,
				},
				workflow_master_step_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: true,
				},
				entity_id: {
					type: Sequelize.DataTypes.INTEGER,
				},
				created_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				created_by: {
					type: Sequelize.DataTypes.STRING,
				},
				updated_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				updated_by: {
					type: Sequelize.DataTypes.STRING,
				},
				deleted: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				active: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: true,
				},
			},
		);
		await queryInterface.createTable(
			{ schema: env['DB_SCHEMA'], tableName: 'workflow_rules' },
			{
				id: {
					type: Sequelize.DataTypes.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				title: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				description: {
					type: Sequelize.DataTypes.TEXT,
					allowNull: false,
				},
				rule: {
					type: Sequelize.DataTypes.JSONB,
					allowNull: false,
				},
				created_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				created_by: {
					type: Sequelize.DataTypes.STRING,
				},
				updated_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				updated_by: {
					type: Sequelize.DataTypes.STRING,
				},
				deleted: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				active: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: true,
				},
			},
		);
		await queryInterface.createTable(
			{ schema: env['DB_SCHEMA'], tableName: 'workflow_shared_child_limits' },
			{
				id: {
					type: Sequelize.DataTypes.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				entity_id: {
					type: Sequelize.DataTypes.INTEGER,
				},
				entity_code: {
					type: Sequelize.DataTypes.STRING,
				},
				entity_title: {
					type: Sequelize.DataTypes.STRING,
				},
				workflow_master_step_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: true,
				},
				workflow_master_parent_step_id: {
					type: Sequelize.DataTypes.INTEGER,
				},
				single_limit: {
					type: Sequelize.DataTypes.INTEGER,
				},
				aggregate_limit: {
					type: Sequelize.DataTypes.INTEGER,
				},
				created_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				created_by: {
					type: Sequelize.DataTypes.STRING,
				},
				updated_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				updated_by: {
					type: Sequelize.DataTypes.STRING,
				},
				deleted: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				active: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: true,
				},
			},
		);

		//NEW TABLE FOR DRAFT AFE
		await queryInterface.createTable(
			{ schema: env['DB_SCHEMA'], tableName: 'afe_drafts' },
			{
				id: {
					type: Sequelize.DataTypes.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				project_name: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				entity_name: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				data: {
					type: Sequelize.DataTypes.JSONB,
				},
				created_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				created_by: {
					type: Sequelize.DataTypes.STRING,
				},
				updated_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				updated_by: {
					type: Sequelize.DataTypes.STRING,
				},
				deleted: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				active: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: true,
				},
			},
		);

		await queryInterface.createTable(
			{ schema: env['DB_SCHEMA'], tableName: 'parallel_identifiers' },
			{
				id: {
					type: Sequelize.DataTypes.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				title: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				identifier: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				created_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				created_by: {
					type: Sequelize.DataTypes.STRING,
				},
				updated_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				updated_by: {
					type: Sequelize.DataTypes.STRING,
				},
				deleted: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				active: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: true,
				},
			},
		);

		await queryInterface.createTable(
			{ schema: env['DB_SCHEMA'], tableName: 'settings' },
			{
				id: {
					type: Sequelize.DataTypes.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				key: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				settings: {
					type: Sequelize.DataTypes.HSTORE,
					allowNull: false,
				},
				created_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				created_by: {
					type: Sequelize.DataTypes.STRING,
				},
				updated_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				updated_by: {
					type: Sequelize.DataTypes.STRING,
				},
				deleted: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				active: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: true,
				},
			},
		);

		await queryInterface.createTable(
			{ schema: env['DB_SCHEMA'], tableName: 'notifications' },
			{
				id: {
					type: Sequelize.DataTypes.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				title: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				description: {
					type: Sequelize.DataTypes.STRING,
					allowNull: true,
				},
				url: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				expire_at: {
					type: 'TIMESTAMP',
					allowNull: false,
				},
				viewed_by: {
					type: Sequelize.DataTypes.JSONB,
					allowNull: true,
				},
				subscribers: {
					type: Sequelize.DataTypes.JSONB,
					allowNull: true,
				},
				type: {
					type: Sequelize.DataTypes.ENUM('INFO', 'WARNING', 'SUCCESS', 'REJECT'),
					allowNull: false,
				},
				created_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				created_by: {
					type: Sequelize.DataTypes.STRING,
				},
				updated_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				updated_by: {
					type: Sequelize.DataTypes.STRING,
				},
				deleted: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				active: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: true,
				},
			},
		);

		await queryInterface.createTable(
			{ schema: env['DB_SCHEMA'], tableName: 'afe_sub_types' },
			{
				id: {
					type: Sequelize.DataTypes.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				title: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				afe_type_ids: {
					type: Sequelize.DataTypes.JSONB,
					allowNull: true,
				},
				included_entity_ids: {
					type: Sequelize.DataTypes.JSONB,
					allowNull: true,
				},
				excluded_entity_ids: {
					type: Sequelize.DataTypes.JSONB,
					allowNull: true,
				},
				created_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				created_by: {
					type: Sequelize.DataTypes.STRING,
				},
				updated_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				updated_by: {
					type: Sequelize.DataTypes.STRING,
				},
				deleted: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				active: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: true,
				},
			},
		);

		await queryInterface.createTable(
			{ schema: env['DB_SCHEMA'], tableName: 'schedulers' },
			{
				id: {
					type: Sequelize.DataTypes.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				title: {
					type: Sequelize.DataTypes.STRING,
					allowNull: false,
				},
				rule: {
					type: Sequelize.DataTypes.JSONB,
					allowNull: false,
				},
				entities: {
					type: Sequelize.DataTypes.JSONB,
					allowNull: true,
				},
				actions: {
					type: Sequelize.DataTypes.JSONB,
					allowNull: true,
				},
				finalApproval: {
					type: Sequelize.DataTypes.BOOLEAN,
					allowNull: false,
					defaultValue: false
				},
				recipients: {
					type: Sequelize.DataTypes.JSONB,
					allowNull: false,
				},
				type: {
					type: Sequelize.DataTypes.ENUM('LIVE', 'DAILY', 'WEEKLY'),
					allowNull: false,
				},
				created_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				created_by: {
					type: Sequelize.DataTypes.STRING,
				},
				updated_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				updated_by: {
					type: Sequelize.DataTypes.STRING,
				},
				deleted: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				active: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: true,
				},
			},
		);

		await queryInterface.createTable(
			{ schema: env['DB_SCHEMA'], tableName: 'queue_logs' },
			{
				id: {
					type: Sequelize.DataTypes.BIGINT,
					autoIncrement: true,
					primaryKey: true,
				},
				entity_id: {
					type: Sequelize.DataTypes.INTEGER,
					allowNull: true,
				},
				action: {
					type: Sequelize.DataTypes.ENUM('SUBMITTED', 'REJECTED', 'CANCELLED', 'APPROVED'),
					allowNull: true,
				},
				finalApproval: {
					type: Sequelize.DataTypes.BOOLEAN,
					allowNull: false,
					defaultValue: false
				},
				data: {
					type: Sequelize.DataTypes.JSONB,
					allowNull: false,
				},
				processed: {
					type: Sequelize.DataTypes.BOOLEAN,
					allowNull: false,
					defaultValue: false
				},
				created_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				created_by: {
					type: Sequelize.DataTypes.STRING,
				},
				updated_on: {
					type: 'TIMESTAMP',
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
				},
				updated_by: {
					type: Sequelize.DataTypes.STRING,
				},
				deleted: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: false,
				},
				active: {
					type: Sequelize.DataTypes.BOOLEAN,
					defaultValue: true,
				},
			},
		);

		await queryInterface.addConstraint(
			{ schema: env['DB_SCHEMA'], tableName: 'afe_budget_type_mappings' },
			{
				references: {
					table: 'afe_budget_types',
					field: 'id',
				},
				onDelete: 'NO ACTION',
				onUpdate: 'CASCADE',
				fields: ['afe_type_id'],
				type: 'foreign key',
				name: 'fk_afe_budget_type_mappings_afe_type',
			},
		);
		await queryInterface.addConstraint(
			{ schema: env['DB_SCHEMA'], tableName: 'afe_budget_type_mappings' },
			{
				references: {
					table: 'afe_request_types',
					field: 'id',
				},
				onDelete: 'NO ACTION',
				onUpdate: 'CASCADE',
				fields: ['afe_request_type_id'],
				type: 'foreign key',
				name: 'fk_afe_budget_type_mappings_afe_request_type',
			},
		);
		await queryInterface.addConstraint(
			{ schema: env['DB_SCHEMA'], tableName: 'afe_nature_type_mappings' },
			{
				references: {
					table: 'afe_request_types',
					field: 'id',
				},
				onDelete: 'NO ACTION',
				onUpdate: 'CASCADE',
				fields: ['afe_request_type_id'],
				type: 'foreign key',
				name: 'fk_afe_nature_type_mappings_afe_request_type',
			},
		);
		await queryInterface.addConstraint(
			{ schema: env['DB_SCHEMA'], tableName: 'afe_nature_type_mappings' },
			{
				references: {
					table: 'afe_nature_types',
					field: 'id',
				},
				onDelete: 'NO ACTION',
				onUpdate: 'CASCADE',
				fields: ['afe_nature_type_id'],
				type: 'foreign key',
				name: 'fk_afe_nature_type_mappings_afe_nature_type',
			},
		);
		await queryInterface.addConstraint(
			{ schema: env['DB_SCHEMA'], tableName: 'afe_proposals' },
			{
				references: {
					table: 'afe_request_types',
					field: 'id',
				},
				onDelete: 'CASCADE',
				onUpdate: 'CASCADE',
				fields: ['afe_request_type_id'],
				type: 'foreign key',
				name: 'fk_afe_proposals_afe_request_type_id',
			},
		);
		await queryInterface.addConstraint(
			{ schema: env['DB_SCHEMA'], tableName: 'afe_proposals' },
			{
				references: {
					table: 'afe_proposals',
					field: 'id',
				},
				onDelete: 'SET NULL',
				onUpdate: 'CASCADE',
				fields: ['parent_afe_id'],
				type: 'foreign key',
				name: 'fk_afe_proposals_parent_afe',
			},
		);
		await queryInterface.addConstraint(
			{ schema: env['DB_SCHEMA'], tableName: 'afe_proposal_amount_splits' },
			{
				references: {
					table: 'afe_proposals',
					field: 'id',
				},
				onDelete: 'CASCADE',
				onUpdate: 'CASCADE',
				fields: ['afe_proposal_id'],
				type: 'foreign key',
				name: 'fk_afe_proposal_amount_splits_afe_proposal',
			},
		);
		await queryInterface.addConstraint(
			{ schema: env['DB_SCHEMA'], tableName: 'afe_proposal_approvers' },
			{
				references: {
					table: 'afe_proposals',
					field: 'id',
				},
				onDelete: 'CASCADE',
				onUpdate: 'CASCADE',
				fields: ['afe_proposal_id'],
				type: 'foreign key',
				name: 'fk_afe_proposal_approvers_afe_proposal',
			},
		);
		await queryInterface.addConstraint(
			{ schema: env['DB_SCHEMA'], tableName: 'afe_proposal_approvers' },
			{
				references: {
					table: 'afe_proposal_approvers',
					field: 'id',
				},
				onDelete: 'CASCADE',
				onUpdate: 'CASCADE',
				fields: ['original_approver_id'],
				type: 'foreign key',
				name: 'fk_afe_proposal_approvers_afe_proposal_original_approver',
			},
		);
		await queryInterface.addConstraint(
			{ schema: env['DB_SCHEMA'], tableName: 'afe_proposal_limit_deduction' },
			{
				references: {
					table: 'afe_proposals',
					field: 'id',
				},
				onDelete: 'CASCADE',
				onUpdate: 'CASCADE',
				fields: ['afe_proposal_id'],
				type: 'foreign key',
				name: 'fk_afe_proposal_limit_deduction_afe_proposal',
			},
		);
		await queryInterface.addConstraint(
			{ schema: env['DB_SCHEMA'], tableName: 'afe_proposal_limit_deduction' },
			{
				references: {
					table: 'workflow_master_settings',
					field: 'id',
				},
				onDelete: 'CASCADE',
				onUpdate: 'CASCADE',
				fields: ['workflow_master_setting_id'],
				type: 'foreign key',
				name: 'fk_afe_proposal_limit_deduction_workflow_master_settings',
			},
		);
		await queryInterface.addConstraint(
			{ schema: env['DB_SCHEMA'], tableName: 'afe_proposal_limit_deduction' },
			{
				references: {
					table: 'afe_proposal_limit_deduction',
					field: 'id',
				},
				onDelete: 'SET NULL',
				onUpdate: 'CASCADE',
				fields: ['parent_id'],
				type: 'foreign key',
				name: 'fk_afe_proposal_limit_deduction_parent',
			},
		);
		await queryInterface.addConstraint(
			{ schema: env['DB_SCHEMA'], tableName: 'questions' },
			{
				references: {
					table: 'afe_request_types',
					field: 'id',
				},
				onDelete: 'CASCADE',
				onUpdate: 'CASCADE',
				fields: ['afe_request_type_id'],
				type: 'foreign key',
				name: 'fk_questions_afe_request_type_id',
			},
		);
		await queryInterface.addConstraint(
			{ schema: env['DB_SCHEMA'], tableName: 'entity_setups' },
			{
				references: {
					table: 'currency_types',
					field: 'id',
				},
				onDelete: 'CASCADE',
				onUpdate: 'CASCADE',
				fields: ['currency_id'],
				type: 'foreign key',
				name: 'fk_entity_setups_currency',
			},
		);
		await queryInterface.addConstraint(
			{ schema: env['DB_SCHEMA'], tableName: 'project_components' },
			{
				references: {
					table: 'project_components',
					field: 'id',
				},
				onDelete: 'SET NULL',
				onUpdate: 'CASCADE',
				fields: ['parent_id'],
				type: 'foreign key',
				name: 'fk_project_components_parent',
			},
		);

		await queryInterface.addConstraint(
			{ schema: env['DB_SCHEMA'], tableName: 'workflow_master_settings' },
			{
				references: {
					table: 'afe_request_types',
					field: 'id',
				},
				onDelete: 'SET NULL',
				onUpdate: 'CASCADE',
				fields: ['request_type_id'],
				type: 'foreign key',
				name: 'fk_workflow_master_settings_afe_request_type',
			},
		);

		await queryInterface.addConstraint(
			{ schema: env['DB_SCHEMA'], tableName: 'workflow_master_settings' },
			{
				references: {
					table: 'project_components',
					field: 'id',
				},
				onDelete: 'SET NULL',
				onUpdate: 'CASCADE',
				fields: ['project_component_id'],
				type: 'foreign key',
				name: 'fk_workflow_master_settings_project_component',
			},
		);

		await queryInterface.addConstraint(
			{ schema: env['DB_SCHEMA'], tableName: 'workflow_master_settings' },
			{
				references: {
					table: 'workflow_master_settings',
					field: 'id',
				},
				onDelete: 'SET NULL',
				onUpdate: 'CASCADE',
				fields: ['parent_id'],
				type: 'foreign key',
				name: 'fk_workflow_master_settings_parent',
			},
		);

		await queryInterface.addConstraint(
			{ schema: env['DB_SCHEMA'], tableName: 'workflow_master_steps' },
			{
				references: {
					table: 'workflow_master_settings',
					field: 'id',
				},
				onDelete: 'NO ACTION',
				onUpdate: 'CASCADE',
				fields: ['workflow_master_setting_id'],
				type: 'foreign key',
				name: 'fk_workflow_master_steps_workflow_master_setting',
			},
		);
		await queryInterface.addConstraint(
			{ schema: env['DB_SCHEMA'], tableName: 'workflow_master_steps' },
			{
				references: {
					table: 'workflow_rules',
					field: 'id',
				},
				onDelete: 'NO ACTION',
				onUpdate: 'CASCADE',
				fields: ['rule_id'],
				type: 'foreign key',
				name: 'fk_workflow_master_steps_workflow_rule',
			},
		);
		await queryInterface.addConstraint(
			{ schema: env['DB_SCHEMA'], tableName: 'workflow_master_steps' },
			{
				references: {
					table: 'workflow_rules',
					field: 'id',
				},
				onDelete: 'NO ACTION',
				onUpdate: 'CASCADE',
				fields: ['skip_limit_rule_id'],
				type: 'foreign key',
				name: 'fk_workflow_master_steps_skip_limit_rule_id',
			},
		);
		await queryInterface.addConstraint(
			{ schema: env['DB_SCHEMA'], tableName: 'workflow_master_steps' },
			{
				references: {
					table: 'workflow_master_steps',
					field: 'id',
				},
				onDelete: 'NO ACTION',
				onUpdate: 'CASCADE',
				fields: ['shared_limit_master_step_child_id'],
				type: 'foreign key',
				name: 'fk_workflow_master_steps_shared_limit_master_step_child',
			},
		);
		await queryInterface.addConstraint(
			{ schema: env['DB_SCHEMA'], tableName: 'workflow_shared_bucket_limits' },
			{
				references: {
					table: 'workflow_master_settings',
					field: 'id',
				},
				onDelete: 'NO ACTION',
				onUpdate: 'CASCADE',
				fields: ['workflow_master_setting_id'],
				type: 'foreign key',
				name: 'fk_workflow_shared_bucket_limits_workflow_master_setting',
			},
		);
		await queryInterface.addConstraint(
			{ schema: env['DB_SCHEMA'], tableName: 'workflow_shared_bucket_limits' },
			{
				references: {
					table: 'workflow_master_steps',
					field: 'id',
				},
				onDelete: 'NO ACTION',
				onUpdate: 'CASCADE',
				fields: ['workflow_master_step_id'],
				type: 'foreign key',
				name: 'fk_workflow_shared_bucket_limits_workflow_master_step',
			},
		);
		await queryInterface.addConstraint(
			{ schema: env['DB_SCHEMA'], tableName: 'workflow_shared_child_limits' },
			{
				references: {
					table: 'workflow_master_steps',
					field: 'id',
				},
				onDelete: 'NO ACTION',
				onUpdate: 'CASCADE',
				fields: ['workflow_master_step_id'],
				type: 'foreign key',
				name: 'fk_workflow_shared_child_limits_workflow_master_steps',
			},
		);
		await queryInterface.addConstraint(
			{ schema: env['DB_SCHEMA'], tableName: 'cost_centers' },
			{
				references: {
					table: 'company_codes',
					field: 'id',
				},
				onDelete: 'NO ACTION',
				onUpdate: 'CASCADE',
				fields: ['company_code_id'],
				type: 'foreign key',
				name: 'fk_cost_centers_company_codes',
			},
		);
		await queryInterface.addConstraint(
			{ schema: env['DB_SCHEMA'], tableName: 'natural_account_numbers' },
			{
				references: {
					table: 'company_codes',
					field: 'id',
				},
				onDelete: 'NO ACTION',
				onUpdate: 'CASCADE',
				fields: ['company_code_id'],
				type: 'foreign key',
				name: 'fk_natural_account_numbers_company_codes',
			},
		);
		await queryInterface.addConstraint(
			{ schema: env['DB_SCHEMA'], tableName: 'analysis_codes' },
			{
				references: {
					table: 'company_codes',
					field: 'id',
				},
				onDelete: 'NO ACTION',
				onUpdate: 'CASCADE',
				fields: ['company_code_id'],
				type: 'foreign key',
				name: 'fk_analysis_codes_company_codes',
			},
		);
	},
	down: async (queryInterface, Sequelize) => {
		await queryInterface.removeConstraint(
			'afe_budget_type_mappings',
			'fk_afe_budget_type_mappings_afe_type',
		);
		await queryInterface.removeConstraint(
			'afe_budget_type_mappings',
			'fk_afe_budget_type_mappings_afe_request_type',
		);
		await queryInterface.removeConstraint(
			'afe_nature_type_mappings',
			'fk_afe_nature_type_mappings_afe_request_type',
		);
		await queryInterface.removeConstraint(
			'afe_nature_type_mappings',
			'fk_afe_nature_type_mappings_afe_nature_type',
		);
		await queryInterface.removeConstraint('afe_proposals', 'fk_afe_proposals_afe_request_type_id');
		await queryInterface.removeConstraint('afe_proposals', 'fk_afe_proposals_parent_afe');
		await queryInterface.removeConstraint(
			'afe_proposal_amount_splits',
			'fk_afe_proposal_amount_splits_afe_proposal',
		);
		await queryInterface.removeConstraint(
			'afe_proposal_approvers',
			'fk_afe_proposal_approvers_afe_proposal',
		);
		await queryInterface.removeConstraint(
			'afe_proposal_approvers',
			'fk_afe_proposal_approvers_afe_proposal_original_approver',
		);
		await queryInterface.removeConstraint(
			'afe_proposal_limit_deduction',
			'fk_afe_proposal_limit_deduction_afe_proposal',
		);
		await queryInterface.removeConstraint(
			'afe_proposal_limit_deduction',
			'fk_afe_proposal_limit_deduction_workflow_master_settings',
		);
		await queryInterface.removeConstraint(
			'afe_proposal_limit_deduction',
			'fk_afe_proposal_limit_deduction_parent',
		);
		await queryInterface.removeConstraint('questions', 'fk_questions_afe_request_type_id');
		await queryInterface.removeConstraint('entity_setups', 'fk_entity_setups_currency');
		await queryInterface.removeConstraint('project_components', 'fk_project_components_parent');
		await queryInterface.removeConstraint(
			'workflow_master_settings',
			'fk_workflow_master_settings_afe_request_type',
		);
		await queryInterface.removeConstraint(
			'workflow_master_settings',
			'fk_workflow_master_settings_project_component',
		);
		await queryInterface.removeConstraint(
			'workflow_master_settings',
			'fk_workflow_master_settings_parent',
		);
		await queryInterface.removeConstraint(
			'workflow_master_steps',
			'fk_workflow_master_steps_workflow_master_setting',
		);
		await queryInterface.removeConstraint(
			'workflow_master_steps',
			'fk_workflow_master_steps_shared_limit_master_step_child',
		);
		await queryInterface.removeConstraint(
			'workflow_master_steps',
			'fk_workflow_master_steps_workflow_rule',
		);
		await queryInterface.removeConstraint(
			'workflow_shared_bucket_limits',
			'fk_workflow_shared_bucket_limits_workflow_master_setting',
		);
		await queryInterface.removeConstraint(
			'workflow_shared_bucket_limits',
			'fk_workflow_shared_bucket_limits_workflow_master_step',
		);
		await queryInterface.removeConstraint(
			'workflow_shared_child_limits',
			'fk_workflow_shared_child_limits_workflow_master_steps',
		);
		await queryInterface.removeConstraint('cost_centers', 'fk_cost_centers_company_codes');
		await queryInterface.removeConstraint(
			'cost_centers',
			'fk_natural_account_numbers_company_codes',
		);
		await queryInterface.removeConstraint('cost_centers', 'fk_analysis_codes_company_codes');
		await queryInterface.dropTable('afe_budget_type_mappings');
		await queryInterface.dropTable('afe_budget_types');
		await queryInterface.dropTable('afe_commitment_length');
		await queryInterface.dropTable('afe_nature_type_mappings');
		await queryInterface.dropTable('afe_nature_types');
		await queryInterface.dropTable('afe_request_types');
		await queryInterface.dropTable('afe_types');
		await queryInterface.dropTable('afe_proposals');
		await queryInterface.dropTable('afe_proposal_amount_splits');
		await queryInterface.dropTable('afe_proposal_approvers');
		await queryInterface.dropTable('afe_proposal_limit_deduction');
		await queryInterface.dropTable('questions');
		await queryInterface.dropTable('company_codes');
		await queryInterface.dropTable('cost_centers');
		await queryInterface.dropTable('currency_types');
		await queryInterface.dropTable('natural_account_numbers');
		await queryInterface.dropTable('entity_setups');
		await queryInterface.dropTable('project_components');
		await queryInterface.dropTable('vaction_setup');
		await queryInterface.dropTable('workflow_master_settings');
		await queryInterface.dropTable('workflow_master_steps');
		await queryInterface.dropTable('workflow_shared_bucket_limits');
		await queryInterface.dropTable('workflow_rules');
		await queryInterface.dropTable('workflow_shared_child_limits');
		await queryInterface.dropTable('afe_drafts');
		await queryInterface.dropTable('analysis_codes');
		await queryInterface.dropTable('settings');
		await queryInterface.dropTable('notifications');
		await queryInterface.dropTable('afe_sub_types');
		await queryInterface.dropTable('schedulers');
		await queryInterface.dropTable('queue_logs');
	},
};
