'use strict';
const dotenv = require('dotenv');
const fs = require('fs');
const env = dotenv.parse(fs.readFileSync('.env'));

module.exports = {
	up: function (queryInterface, Sequelize) {
		return queryInterface.bulkInsert(
			{ schema: env['DB_SCHEMA'], tableName: 'afe_request_types' },
			[
				{
					title: 'Capex',
					supplimental_allowed: true,
					created_by: '<EMAIL>',
					updated_by: '<EMAIL>',
					active: true,
					deleted: false,
				},
				{
					title: 'Opex',
					supplimental_allowed: true,
					created_by: '<EMAIL>',
					updated_by: '<EMAIL>',
					active: true,
					deleted: false,
				},
				{
					title: 'Equity Infusion',
					supplimental_allowed: true,
					created_by: '<EMAIL>',
					updated_by: '<EMAIL>',
					active: true,
					deleted: false,
				},
				{
					title: 'Sale of Business',
					supplimental_allowed: true,
					created_by: '<EMAIL>',
					updated_by: '<EMAIL>',
					active: true,
					deleted: false,
				},
				{
					title: 'Receivable Write Offs / Waivers and Settlement of Claims',
					supplimental_allowed: true,
					created_by: '<EMAIL>',
					updated_by: '<EMAIL>',
					active: true,
					deleted: false,
				},
				{
					title: 'Sale/Write Off - Tangible Assets (Market Value <= Net Book Value)',
					supplimental_allowed: true,
					created_by: '<EMAIL>',
					updated_by: '<EMAIL>',
					active: true,
					deleted: false,
				},
				{
					title: 'Sale/Write Off - Tangible Assets (Market Value > Net Book Value)',
					supplimental_allowed: true,
					created_by: '<EMAIL>',
					updated_by: '<EMAIL>',
					active: true,
					deleted: false,
				},
			],
			{},
		);
	},

	down: function (queryInterface, Sequelize) {},
};
