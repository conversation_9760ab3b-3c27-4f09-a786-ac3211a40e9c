import { Controller, Get } from '@nestjs/common';
import { ApiResponse, ApiTags } from '@nestjs/swagger';
import { ConfigService } from './config.service';

@ApiTags('Config Client APIs')
@Controller('config')
export class ConfigController {
	constructor(private readonly configService: ConfigService) {}

	/**
	 * Get configuration of web client application.
	 * @returns
	 */
	@ApiResponse({
		status: 200,
		description: 'Get configuration of web client application',
	})
	@Get('/web-client')
	public getWebClientConfiguration() {
		const appConfig = this.configService.getAppConfig();
        return appConfig.webClientConfig;
	}
}
