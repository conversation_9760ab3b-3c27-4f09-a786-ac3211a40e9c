import { Injectable, LogLevel } from '@nestjs/common';
import { AppConfig } from 'src/shared/types';
import * as fs from 'fs';
import * as path from 'path';
@Injectable()
export class ConfigService {
	private readonly config: AppConfig;

	constructor() {
		if (process.env.USE_SECRET) {
			this.config = JSON.parse(process.env.APP_CONFIG);
		} else if (process.env.NODE_ENV) {
			this.config = JSON.parse(
				fs.readFileSync(path.resolve(__dirname, `../config.${process.env.NODE_ENV}.json`), 'utf8'
			));
		} else {
			this.config = JSON.parse(
				fs.readFileSync(path.resolve(__dirname, `../config.json`), 'utf8'
			));
		}
	}

	/**
	 * Get config variable value by its key
	 * @param key: JSON
	 */
	public get(key: string) {
		return this.config[key] ? this.config[key] : null;
	}

	/**
	 * Get log level for the logger by env.
	 * @returns
	 */
	public getLogLevel(): LogLevel[] {
		return this.config.logLevel;
	}

	/**
	 * Return application configurations
	 * @returns
	 */
	public getAppConfig(): AppConfig {
		return this.config;
	}

	get apiKey(): string {
		return this.config.apiKey;
	}

	get oneAppApiKey(): string { 
		return this.config.oneAppApiKey;
	}

	get thresholdDaysForReminder(): number {
		return this.config.thresholdDaysForReminder;
	}
}
