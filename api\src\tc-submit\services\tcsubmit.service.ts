import { Injectable } from '@nestjs/common';
import { Pagination } from 'src/core/pagination';
import { multiObjectToInstance, singleObjectToInstance } from 'src/shared/helpers';
import { CurrentContext, NotificationPayload } from 'src/shared/types';
import {
	ModelRepository,
	ModelScenarioDataRepository,
	ModelScenarioRepository,
} from '../repositories';
import { KpiConfigResponseDto } from 'src/tc-config/dtos';
import { ModelChartDto, ModelDto, ModelListingDto } from '../dtos';
import { Model, ModelScenario } from '../models';
import { TcConfigService } from 'src/tc-config/services';
import { KpiCalculatorService } from 'src/tc-config/services/kpi-calculator.service';
import { USER_STATUS, WORKFLOW_STATUS } from 'src/shared/constants';
import { Op } from 'sequelize';

@Injectable()
export class TcSubmitService {
	constructor(
		private readonly modelRepository: ModelRepository,
		private readonly modelScenarioRepository: ModelScenarioRepository,
		private readonly modelScenarioDataRepository: ModelScenarioDataRepository,
		private readonly tcConfigService: TcConfigService,
		private readonly kpiCalculatorService: KpiCalculatorService,
	) {}

	public async getModelById(modelId: number): Promise<any> {
		// First, check if a model already exists for this entity and user
		const model = await this.modelRepository.findById(modelId);
		return model;
	}

	/**
	 * Create a new model and then create a model scenario
	 * @param currentContext Current user context
	 * @param modelDto Model data
	 * @param scenarioDto Model scenario data
	 * @returns Created model and scenario
	 */
	public async createOrGetModelWithAllScenario(
		currentContext: CurrentContext,
		modelDto: ModelDto,
	): Promise<any> {
		// First, check if a model already exists for this entity and user
		const existingModel = await this.modelRepository.findOne({
			where: {
				entity_id: modelDto.business_entity_id,
				createdBy: currentContext.user.username.toLowerCase(),
				workflow_status: { [Op.ne]: WORKFLOW_STATUS.PUBLISHED },
			},
		});

		let newModel = new Model();
		if (existingModel) {
			// Find all scenarios for this model
			const scenarios = await this.modelScenarioRepository.findAll({
				where: { model_id: existingModel.id },
				order: [['scenario_number', 'ASC']],
			});

			if (scenarios && scenarios.length > 0) {
				// Map scenarios to the required format
				return scenarios.map(scenario => ({
					model_id: scenario.model_id,
					scenario_id: scenario.id,
					scenario_number: scenario.scenario_number,
					is_current_state: scenario.is_current_state,
					kpi_data: scenario.kpi_data,
				}));
			} else {
				return {
					message: 'Model exists but no scenarios found',
					model_id: existingModel.id,
				};
			}
		} else {
			var kpi_data = await this.tcConfigService.getAllKpisAndValues(modelDto.business_entity_id);

			// Create a new model
			newModel.entity_id = modelDto.business_entity_id;
			newModel.entity_code = modelDto.business_entity_code;
			newModel.title = modelDto.title;
			newModel.summary_tabular_kpis = null;
			newModel.summary_chart_kpis = null;
			newModel.user_status = USER_STATUS.DRAFT;
			newModel.workflow_status = WORKFLOW_STATUS.DRAFT;
			newModel.override_fetch_mechanism_type = 'default';
			newModel = await this.modelRepository.save(newModel, currentContext);
		}

		// Now create the scenario using the model id
		let newScenario = new ModelScenario();
		newScenario.model_id = newModel.id;
		newScenario.is_current_state = true;
		newScenario.scenario_number = 0;
		newScenario.kpi_data = kpi_data;
		newScenario = await this.modelScenarioRepository.save(newScenario, currentContext);

		return [
			{
				model_id: newScenario.model_id,
				scenario_id: newScenario.id,
				scenario_number: newScenario.scenario_number,
				is_current_state: true,
				kpi_data: newScenario.kpi_data,
			},
		];
	}

	public async getModelScenariosById(modelId: number): Promise<any> {
		// First, check if a model already exists for this entity and user
		const model = await this.modelRepository.findById(modelId);

		if (model) {
			// Find all scenarios for this model
			const scenarios = await this.modelScenarioRepository.findAll({
				where: { model_id: model.id },
				order: [['scenario_number', 'ASC']],
			});

			if (scenarios && scenarios.length > 0) {
				// Map scenarios to the required format
				return scenarios.map(scenario => ({
					model_id: scenario.model_id,
					scenario_id: scenario.id,
					scenario_number: scenario.scenario_number,
					is_current_state: scenario.is_current_state,
					kpi_data: scenario.kpi_data,
				}));
			} else {
				// This should rarely happen - model exists but no scenarios
				return {
					message: 'Model exists but no scenarios found',
					model_id: model.id,
				};
			}
		}
	}

	public async saveModelChartKpi(modelDto: ModelChartDto, currentContext: CurrentContext) {
		const model = await this.modelRepository.findById(modelDto.model_id);

		if (!model) {
			throw new Error(`Model with ID ${modelDto.model_id} not found`);
		}

		model.summary_chart_kpis = modelDto.kpi_codes;
		await this.modelRepository.updateModel(model.id, model, currentContext);
		return true;
	}

	public async saveModelTabularKpi(modelDto: ModelChartDto, currentContext: CurrentContext) {
		const model = await this.modelRepository.findOne({
			where: { id: modelDto.model_id },
		});

		if (!model) {
			throw new Error(`Model with ID ${modelDto.model_id} not found`);
		}

		model.summary_tabular_kpis = modelDto.kpi_codes;
		await this.modelRepository.updateModel(model.id, model, currentContext);
		return true;
	}

	/**
	 * Update KPI values and recalculate all dependent formulas
	 * @param currentContext Current user context
	 * @param modelId Model ID
	 * @param scenarioId Scenario ID
	 * @param kpiValues Object with KPI codes and their values
	 * @returns Updated KPI values
	 */
	public async updateKpiValues(
		currentContext: CurrentContext,
		modelId: number,
		scenarioId: number,
		kpiValues: Record<string, any>,
	): Promise<any> {
		try {
			// Get the model
			const model = await this.modelRepository.findOne({
				where: { id: modelId },
			});

			if (!model) {
				throw new Error(`Model with ID ${modelId} not found`);
			}

			// Get the scenario
			const scenario = await this.modelScenarioRepository.getById(scenarioId);

			if (!scenario) {
				throw new Error(`Scenario with ID ${scenarioId} for model ${modelId} not found`);
			}

			// Get the business entity ID from the model
			const businessEntityId = model.entity_id;

			// Get current KPI values from the scenario
			let currentKpiData = scenario.kpi_data || {};
			if (typeof currentKpiData === 'string') {
				currentKpiData = JSON.parse(currentKpiData);
			}

			// Merge input values with current values
			const mergedKpiValues = { ...currentKpiData, ...kpiValues };

			// Recalculate all KPI values including formulas
			const recalculatedValues = await this.tcConfigService.getAllKpisAndValues(
				businessEntityId,
				mergedKpiValues,
			);

			// Update the scenario with new KPI values
			scenario.kpi_data = recalculatedValues;
			await this.modelScenarioRepository.updateScenario(scenario.id, scenario, currentContext);

			return {
				model_id: modelId,
				scenario_id: scenarioId,
				kpi_values: recalculatedValues,
			};
		} catch (error) {
			console.error('Error updating KPI values:', error);
			throw error;
		}
	}

	/**
	 * Create a new scenario by copying data from the current state scenario
	 * @param currentContext Current user context
	 * @param modelId Model ID
	 * @returns Newly created scenario
	 */
	public async createNewScenario(currentContext: CurrentContext, modelId: number): Promise<any> {
		try {
			// Get the model
			const model = await this.modelRepository.findOne({
				where: { id: modelId },
			});

			if (!model) {
				throw new Error(`Model with ID ${modelId} not found`);
			}

			// Find the current state scenario
			const currentStateScenario = await this.modelScenarioRepository.findOne({
				where: {
					model_id: modelId,
					is_current_state: true,
				},
			});

			if (!currentStateScenario) {
				throw new Error(`Current state scenario for model ${modelId} not found`);
			}

			// Find the highest scenario number
			const scenarios = await this.modelScenarioRepository.findAll({
				where: { model_id: modelId },
				order: [['scenario_number', 'DESC']],
				limit: 1,
			});

			let highestScenarioNumber = 0;
			if (scenarios && scenarios.length > 0) {
				highestScenarioNumber = scenarios[0].scenario_number;
			}

			// Create a new scenario
			let newScenario = new ModelScenario();
			newScenario.model_id = modelId;
			newScenario.is_current_state = false; // Not the current state
			newScenario.scenario_number = highestScenarioNumber + 1;
			newScenario.kpi_data = currentStateScenario.kpi_data;
			newScenario = await this.modelScenarioRepository.save(newScenario, currentContext);

			return {
				model_id: newScenario.model_id,
				scenario_id: newScenario.id,
				scenario_number: newScenario.scenario_number,
				is_current_state: newScenario.is_current_state,
				kpi_data: newScenario.kpi_data,
			};
		} catch (error) {
			console.error('Error creating new scenario:', error);
			throw error;
		}
	}

	/**
	 * Publish a scenario after validating all KPI values
	 * @param currentContext Current user context
	 * @param modelId Model ID
	 * @returns Published scenario status
	 */
	public async publishModel(currentContext: CurrentContext, modelId: number): Promise<any> {
		try {
			// Get the model
			const model = await this.modelRepository.findOne({
				where: { id: modelId },
			});

			if (!model) {
				throw new Error(`Model with ID ${modelId} not found`);
			}

			if(model.workflow_status !== WORKFLOW_STATUS.DRAFT) {
				throw new Error(`Model with ID ${modelId} should be on draft status to publish the model`);
			}

			// Get the scenario
			const scenario = await this.modelScenarioRepository.findOne({
				where: { is_current_state: true, model_id: modelId },
			});

			if (!scenario) {
				throw new Error(`Current state Scenario for model ${modelId} not found`);
			}

			// Get KPI data
			let kpiData = scenario.kpi_data;
			if (typeof kpiData === 'string') {
				kpiData = JSON.parse(kpiData);
			}

			// Validate that no KPI values are -1
			const invalidKpis = [];
			for (const [kpiCode, value] of Object.entries(kpiData)) {
				if (value === -1) {
					invalidKpis.push(kpiCode);
				}
			}

			if (invalidKpis.length > 0) {
				return {
					success: false,
					message: 'Cannot publish model with invalid KPI values',
					invalidKpis: invalidKpis,
				};
			}

			// Update model status to PUBLISHED
			model.user_status = USER_STATUS.PUBLISHED;
			model.workflow_status = WORKFLOW_STATUS.PUBLISHED;
			await this.modelRepository.updateModel(model.id, model, currentContext);

			return {
				success: true,
				message: 'Model published successfully',
				model_id: modelId,
				workflow_status: WORKFLOW_STATUS.PUBLISHED,
			};
		} catch (error) {
			console.error('Error publishing model:', error);
			throw error;
		}
	}

	/**
	 * Refresh KPI values for one, multiple, or all scenarios under a model
	 * @param currentContext Current user context
	 * @param modelId Model ID
	 * @param scenarioIds Optional array of specific scenario IDs to refresh
	 * @returns Refreshed KPI values for each scenario
	 */
	public async refreshKpiValues(
		currentContext: CurrentContext,
		modelId: number,
		scenarioIds?: number[],
	): Promise<any> {
		try {
			// Get the model
			const model = await this.modelRepository.findOne({
				where: { id: modelId },
			});

			if (!model) {
				return {
					success: false,
					message: `Model with ID ${modelId} not found`,
				};
			}

			// If no specific scenario IDs provided, get all scenarios for this model
			let scenarios = [];
			if (!scenarioIds || scenarioIds.length === 0) {
				scenarios = await this.modelScenarioRepository.findAll({
					where: { model_id: modelId },
					order: [['scenario_number', 'ASC']],
				});

				if (!scenarios || scenarios.length === 0) {
					return {
						success: false,
						message: `No scenarios found for model ${modelId}`,
					};
				}
			} else {
				// Get only the specified scenarios that belong to this model
				scenarios = await this.modelScenarioRepository.findAll({
					where: {
						id: scenarioIds,
						model_id: modelId,
					},
				});
			}

			const results = [];
			const businessEntityId = model.entity_id;

			for (const scenario of scenarios) {
				try {
					// Get current KPI values from the scenario
					let currentKpiData = scenario.kpi_data || {};
					if (typeof currentKpiData === 'string') {
						currentKpiData = JSON.parse(currentKpiData);
					}

					// Recalculate all KPI values including formulas
					const recalculatedValues = await this.tcConfigService.getAllKpisAndValues(
						businessEntityId,
						currentKpiData,
					);

					// Update the scenario with new KPI values
					scenario.kpi_data = recalculatedValues;
					await this.modelScenarioRepository.updateScenario(scenario.id, scenario, currentContext);

					results.push({
						scenario_id: scenario.id,
						scenario_number: scenario.scenario_number,
						is_current_state: scenario.is_current_state,
						success: true,
						model_id: modelId,
						kpi_values: recalculatedValues,
					});
				} catch (error) {
					results.push({
						scenario_id: scenario.id,
						scenario_number: scenario.scenario_number,
						success: false,
						message: `Error refreshing scenario ${scenario.id}: ${error.message}`,
					});
				}
			}

			return {
				model_id: modelId,
				results: results,
			};
		} catch (error) {
			console.error('Error refreshing KPI values:', error);
			throw error;
		}
	}

	/**
	 * Calculate KPI values for specific KPI codes using current model scenario data
	 * @param currentContext Current user context
	 * @param modelId Model ID
	 * @param scenarioId Scenario ID
	 * @param kpiCodes Array of KPI codes to calculate
	 * @returns Object containing calculated KPI values
	 */
	public async calculateKpiValues(
		currentContext: CurrentContext,
		modelId: number,
		scenarioId: number,
		kpiCodes: string[],
	): Promise<any> {
		try {
			// Get the model

			const model = await this.modelRepository.findById(modelId);
			if (!model) {
				throw new Error(`Model with ID ${modelId} not found`);
			}

			// Get the scenario
			const scenario = await this.modelScenarioRepository.getById(scenarioId);
			if (!scenario) {
				throw new Error(`Scenario with ID ${scenarioId} for model ${modelId} not found`);
			}

			// Get the business entity ID from the model
			const businessEntityId = model.entity_id;

			// Get current KPI values from the scenario
			let currentKpiData = scenario.kpi_data || {};
			if (typeof currentKpiData === 'string') {
				currentKpiData = JSON.parse(currentKpiData);
			}

			// Get KPI data for the specified KPI codes
			const kpiData = await this.kpiCalculatorService.getKpiDataBySystemCodes(
				businessEntityId,
				kpiCodes,
				currentKpiData,
				model.override_fetch_mechanism_type,
				model.pull_from_date,
				model.pull_to_date,
			);

			// Extract just the current values for the response
			const result = {
				model_id: modelId,
				scenario_id: scenarioId,
				kpi_values: {},
			};

			// Add the calculated values to the result
			for (const kpiItem of kpiData) {
				result.kpi_values[kpiItem.kpi_code] = kpiItem.current_value;
			}

			// Also include dependency values
			for (const kpiItem of kpiData) {
				if (kpiItem.dependency_values) {
					Object.entries(kpiItem.dependency_values).forEach(([key, value]) => {
						if (!result.kpi_values[key]) {
							result.kpi_values[key] = value;
						}
					});
				}
			}

			return result;
		} catch (error) {
			throw new Error(`Failed to calculate KPI values: ${error.message}`);
		}
	}

	public async getLatestActiveModel(currentContext: CurrentContext): Promise<any> {
		const model = await this.modelRepository.findOne({
			where: {
				createdBy: currentContext.user.username.toLowerCase(),
				workflow_status: { [Op.ne]: WORKFLOW_STATUS.PUBLISHED },
			},
			order: [['id', 'DSC']],
		});

		if (!model) {
			return [];
		}

		// Find all scenarios for this model
		const scenarios = await this.modelScenarioRepository.findAll({
			where: { model_id: model.id },
			order: [['scenario_number', 'ASC']],
		});

		if (scenarios && scenarios.length > 0) {
			// Map scenarios to the required format
			return scenarios.map(scenario => ({
				model_id: scenario.model_id,
				scenario_id: scenario.id,
				scenario_number: scenario.scenario_number,
				is_current_state: scenario.is_current_state,
				kpi_data: scenario.kpi_data,
			}));
		} else {
			return [];
		}
	}

	/**
	 * Get all published models and draft models of the current user
	 * @param currentContext Current user context
	 * @returns Array of models with their scenarios
	 */
	public async getAllModels(
		currentContext: CurrentContext,
		modelDto: ModelListingDto,
	): Promise<any> {
		try {
			// Get all published models
			const allModels = await this.modelRepository.findAll({
				where: {
					entity_code: modelDto.business_entity_code,
				},
				order: [['id', 'DESC']],
			});

			// // Get all draft models of the current user
			// const draftModels = await this.modelRepository.findAll({
			// 	where: {
			// 		createdBy: currentContext.user.username.toLowerCase(),
			// 		workflow_status: { [Op.ne]: WORKFLOW_STATUS.PUBLISHED }
			// 	},
			// 	order: [['id', 'DESC']]
			// });

			// Combine both lists
			// const allModels = [...draftModels, ...publishedModels];

			if (!allModels || allModels.length === 0) {
				return [];
			}

			// Process each model to include its scenarios
			const result = [];

			for (const model of allModels) {
				result.push({
					model_id: model.id,
					entity_id: model.entity_id,
					entity_code: model.entity_code,
					title: model.title,
					workflow_status: model.workflow_status,
					created_by: model.createdBy,
					created_on: model.createdOn,
				});
			}

			return result;
		} catch (error) {
			console.error('Error getting all models:', error);
			throw error;
		}
	}

	/**
	 * Refresh model by updating KPI configurations based on latest excluded equipment
	 * while preserving existing KPI values
	 * @param currentContext Current user context
	 * @param modelId Model ID
	 * @returns Refreshed model status
	 */
	public async refreshModel(currentContext: CurrentContext, modelId: number): Promise<any> {
		try {
			// Get the model
			const model = await this.modelRepository.findOne({
				where: { id: modelId },
			});

			if (!model) {
				throw new Error(`Model with ID ${modelId} not found`);
			}

			// Get all scenarios for this model
			const scenarios = await this.modelScenarioRepository.findAll({
				where: { model_id: modelId },
				order: [['scenario_number', 'ASC']],
			});

			if (!scenarios || scenarios.length === 0) {
				throw new Error(`No scenarios found for model ${modelId}`);
			}

			const businessEntityId = model.entity_id;
			const results = [];

			for (const scenario of scenarios) {
				try {
					// Get current KPI values from the scenario
					let currentKpiData = scenario.kpi_data || {};
					if (typeof currentKpiData === 'string') {
						currentKpiData = JSON.parse(currentKpiData);
					}

					// Get fresh KPI configurations with latest excluded equipment filters
					const refreshedKpiData = await this.tcConfigService.getAllKpisAndValues(
						businessEntityId,
						currentKpiData, // Pass existing values to preserve them
					);

					// Update the scenario with refreshed KPI data
					scenario.kpi_data = refreshedKpiData;
					await this.modelScenarioRepository.updateScenario(scenario.id, scenario, currentContext);

					results.push({
						scenario_id: scenario.id,
						scenario_number: scenario.scenario_number,
						is_current_state: scenario.is_current_state,
						success: true,
						message: 'Scenario refreshed successfully',
					});
				} catch (error) {
					results.push({
						scenario_id: scenario.id,
						scenario_number: scenario.scenario_number,
						success: false,
						message: `Error refreshing scenario ${scenario.id}: ${error.message}`,
					});
				}
			}

			return {
				success: true,
				message: 'Model refreshed successfully',
				model_id: modelId,
				results: results,
			};
		} catch (error) {
			console.error('Error refreshing model:', error);
			throw error;
		}
	}

	public async updateModelFetchMechanism(
		currentContext: CurrentContext, 
		modelId: number,
		fetchMechanism: string,
		startDate?: Date,
		endDate?: Date
	): Promise<any> {
		try {
			// Get the model
			const model = await this.modelRepository.findOne({
				where: { id: modelId },
			});

			if (!model) {
				throw new Error(`Model with ID ${modelId} not found`);
			}

			// Get all scenarios for this model
			const scenarios = await this.modelScenarioRepository.findAll({
				where: { model_id: modelId },
				order: [['scenario_number', 'ASC']],
			});

			if (!scenarios || scenarios.length === 0) {
				throw new Error(`No scenarios found for model ${modelId}`);
			}

			const businessEntityId = model.entity_id;
			const results = [];

			for await (const scenario of scenarios) {
				try {
					// Get current KPI values from the scenario
					let currentKpiData = scenario.kpi_data || {};
					if (typeof currentKpiData === 'string') {
						currentKpiData = JSON.parse(currentKpiData);
					}

					// Get fresh KPI configurations with latest excluded equipment filters
					const refreshedKpiData = await this.tcConfigService.getAllKpisAndValues(
						businessEntityId,
						currentKpiData, // Pass existing values to preserve them
						fetchMechanism,
						startDate,
						endDate,
					);

					// Update the scenario with new KPI data
					scenario.kpi_data = refreshedKpiData;
					await this.modelScenarioRepository.updateScenario(scenario.id, scenario, currentContext);

					results.push({
						scenario_id: scenario.id,
						scenario_number: scenario.scenario_number,
						success: true,
						message: 'Scenario updated successfully',
					});
				} catch (error) {
					results.push({
						scenario_id: scenario.id,
						scenario_number: scenario.scenario_number,
						success: false,
						message: `Error updating scenario: ${error.message}`,
					});
				}
			}

			model.override_fetch_mechanism_type = fetchMechanism;

			if(fetchMechanism === 'custom'){
				if(!startDate || !endDate){
					throw new Error('Start date and end date are required for custom fetch mechanism');
				}
				model.pull_from_date = startDate;
				model.pull_to_date = endDate;
			} else {
				model.pull_from_date = null;
				model.pull_to_date = null;
			}

			await this.modelRepository.updateModel(model.id, model, currentContext);

			return {
				success: true,
				message: 'Model fetch mechanism updated successfully',
				model_id: modelId,
				fetch_mechanism: fetchMechanism,
				results: results,
			};
		} catch (error) {
			console.error('Error updating model fetch mechanism:', error);
			throw error;
		}
	}
}
