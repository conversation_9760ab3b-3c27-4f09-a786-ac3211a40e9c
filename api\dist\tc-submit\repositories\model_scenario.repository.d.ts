import { BaseRepository } from 'src/shared/repositories';
import { CurrentContext } from 'src/shared/types';
import { ModelScenario } from '../models';
export declare class ModelScenarioRepository extends BaseRepository<ModelScenario> {
    constructor();
    getAll(ids: number[]): Promise<ModelScenario[] | null>;
    getById(id: number): Promise<ModelScenario | null>;
    createScenario(request: any, currentContext: CurrentContext): Promise<ModelScenario | null>;
    updateScenario(id: any, request: ModelScenario, currentContext: CurrentContext): Promise<number | null>;
}
