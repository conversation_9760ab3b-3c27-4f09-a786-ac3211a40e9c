import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { <PERSON>Array, IsNotEmpty, IsOptional } from 'class-validator';

export class BuSetupResponseDto {
	@ApiProperty({ name: 'entity_id', type: Number })
	@Expose({ name: 'entity_id' })
	public entity_id: number;

	@ApiProperty({ name: 'entity_code', type: String })
	@Expose({ name: 'entity_code' })
	public entity_code: string | null;

	@ApiProperty({ name: 'excluded_equipment_type', type: Object })
	@Expose({ name: 'excluded_equipment_type' })
	public excluded_equipment_type: JSON;

	@ApiProperty({ name: 'excluded_kpi', type: Object })
	@Expose({ name: 'excluded_kpi' })
	public excluded_kpi: JSON | null;
}
