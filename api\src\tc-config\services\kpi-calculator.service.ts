import { Injectable } from '@nestjs/common';
import { KpiConfig } from '../models/kpi_config.model';
import { KpiData } from '../models/kpi_data.model';
import {
	evaluateKpiFormula,
	extractDependentKpisFromFormula,
} from '../helpers/kpi-formula-evaluator.helper';
import { KpiDataRepository, KpiConfigRepository, BuSetupRepository } from '../repositories';
import { SequlizeOperator } from '../../shared/helpers/sequlize-operator.helper';
import { Op, Sequelize } from 'sequelize';
import { multiObjectToInstance, singleObjectToInstance } from 'src/shared/helpers';
import { KpiConfigResponseDto } from '../dtos';

@Injectable()
export class KpiCalculatorService {
	constructor(
		private readonly kpiConfigRepository: KpiConfigRepository,
		private readonly kpiDataRepository: KpiDataRepository,
		private readonly buSetupRepository: BuSetupRepository,
	) {}

	/**
	 * Save calculated KPI values to the database
	 * @param entityId The entity ID
	 * @param year The year
	 * @param month The month
	 * @param kpiValues The calculated KPI values
	 * @param userId The user ID making the change
	 */
	public async saveKpiValues(
		entityId: number,
		month: number,
		kpiValues: Record<string, any>,
		userId: string,
	): Promise<void> {
		const kpiEntries = Object.entries(kpiValues);

		for (const [kpiCode, kpiValue] of kpiEntries) {
			if (kpiValue === null || kpiValue === undefined) continue;

			// Find existing KPI data or create new
			const existingData = await this.kpiDataRepository.findOne({
				where: {
					kpi_code: kpiCode,
					kpi_month: month,
				},
			});

			if (existingData) {
				// Update existing
				// await this.kpiDataRepository.update(
				//   { kpi_value: kpiValue, updated_by: userId },
				//   { where: { id: existingData.id } }
				// );
			} else {
				// Create new
				// await this.kpiDataRepository.create({
				//   kpi_code: kpiCode,
				//   kpi_year: year,
				//   kpi_month: month,
				//   kpi_value: kpiValue,
				//   created_by: userId,
				//   updated_by: userId
				// });
			}
		}
	}

	public async getAllKpisAndValues(
		business_entity_id: number,
		inputValues: Record<string, any> = {},
		overrideFetchMechanism?: string,
		startDate?: Date,
		endDate?: Date,
	): Promise<Record<string, any>> {
		// Get all KPI configurations
		let allKpiConfigs = await this.kpiConfigRepository.getAll();

		// Filter out excluded equipment types based on bu_setup
		allKpiConfigs = await this.filterKpisByExcludedEquipment(allKpiConfigs, business_entity_id);

		// Calculate all KPI values
		const kpiValues = await this.calculateKpiValues(
			allKpiConfigs,
			business_entity_id,
			inputValues,
			overrideFetchMechanism,
			startDate,
			endDate,
		);

		const allKpiCodes = allKpiConfigs.map(kpi => kpi.system_code);

		// Filter results to only include the originally requested KPIs
		const result: Record<string, any> = {};
		for (const kpiCode of allKpiCodes) {
			result[kpiCode] = kpiValues[kpiCode];
		}

		return result;
	}

	public async getKpiDataBySystemCodes(
		business_entity_id: number,
		systemCodes: string[],
		inputValues: Record<string, any> = {},
		overrideFetchMechanism?: string,
		startDate?: Date,
		endDate?: Date,
	): Promise<any[]> {
		// Get all KPI configurations
		const allKpiConfigs = await this.kpiConfigRepository.getAll();

		// Filter to get requested KPI configs
		const requestedKpiConfigs = allKpiConfigs.filter(kpi => systemCodes.includes(kpi.system_code));

		if (requestedKpiConfigs.length === 0) {
			throw new Error(`No KPIs found with the provided system codes`);
		}

		// Find all dependencies including the requested KPIs
		const requiredKpiCodes = await this.findAllDependencies(systemCodes);

		// Get KPI configs for all required KPIs
		const requiredKpiConfigs = allKpiConfigs.filter(kpi => requiredKpiCodes.has(kpi.system_code));

		// Calculate values for all required KPIs
		const kpiValues = await this.calculateKpiValues(
			requiredKpiConfigs,
			business_entity_id,
			inputValues,
			overrideFetchMechanism,
			startDate,
			endDate,
		);

		// Prepare response for each requested KPI
		const result: any[] = [];

		for (const systemCode of systemCodes) {
			const kpiConfig = requestedKpiConfigs.find(kpi => kpi.system_code === systemCode);

			if (!kpiConfig) continue;

			// Get dependencies for this specific KPI
			const kpiDependencies = new Set<string>([systemCode]);
			if (kpiConfig.kpi_type === 'formula') {
				const kpiConfigObj =
					typeof kpiConfig.kpi_config === 'string'
						? JSON.parse(kpiConfig.kpi_config as string)
						: kpiConfig.kpi_config;

				const formula = kpiConfigObj?.formula || '';
				const directDependencies = extractDependentKpisFromFormula(formula);

				directDependencies.forEach(depCode => kpiDependencies.add(depCode));
			}

			// Get historical data for the KPI
			// const historicalData = await this.kpiDataRepository.findAll({
			// 	where: { kpi_code: systemCode },
			// 	order: [
			// 		['kpi_year', 'DESC'],
			// 		['kpi_month', 'DESC'],
			// 	],
			// 	limit: 12,
			// });

			// Format the response for this KPI
			result.push({
				kpi_code: systemCode,
				kpi_config: kpiConfig,
				current_value: kpiValues[systemCode],
				// historical_data: multiObjectToInstance(KpiDataResponseDto, historicalData),
				dependencies: Array.from(kpiDependencies).filter(code => code !== systemCode),
				dependency_values: Object.fromEntries(
					Object.entries(kpiValues).filter(
						([key]) => kpiDependencies.has(key) && key !== systemCode,
					),
				),
			});
		}

		return result;
	}

	public async getKpisByTags(
		business_entity_id: number,
		tags: string[],
		year: number,
		inputValues: Record<string, any> = {},
	): Promise<Record<string, any>> {
		// Get all KPI configurations
		const allKpiConfigs = await this.kpiConfigRepository.getAll();

		// Filter KPIs by tags
		const filteredKpiConfigs = allKpiConfigs.filter(kpi => {
			const kpiTags = Array.isArray(kpi.tags)
				? kpi.tags
				: typeof kpi.tags === 'string'
				? JSON.parse(kpi.tags as string)
				: [];

			return tags.some(tag => kpiTags.includes(tag));
		});

		const filteredKpiCodes = filteredKpiConfigs.map(kpi => kpi.system_code);
		const dependentKpiCodes = new Set<string>();

		// Find dependencies for formula KPIs
		const formulaKpis = filteredKpiConfigs.filter(kpi => kpi.kpi_type === 'formula');
		for (const kpi of formulaKpis) {
			this.findFormulaDependencies(kpi, allKpiConfigs, dependentKpiCodes);
		}

		// Filter out dependencies that are already in the filtered list
		const additionalDependencies = Array.from(dependentKpiCodes).filter(
			code => !filteredKpiCodes.includes(code),
		);

		const dependentKpiConfigs = allKpiConfigs.filter(kpi =>
			additionalDependencies.includes(kpi.system_code),
		);

		const combinedKpiConfigs = [...filteredKpiConfigs, ...dependentKpiConfigs];

		// Calculate all KPI values
		const kpiValues = await this.calculateKpiValues(
			combinedKpiConfigs,
			business_entity_id,
			inputValues,
		);

		// Filter results to only include the originally requested KPIs
		const result: Record<string, any> = {};
		for (const kpiCode of filteredKpiCodes) {
			result[kpiCode] = kpiValues[kpiCode];
		}

		return result;
	}

	// Common helper methods
	private findFormulaDependencies(
		kpiConfig: KpiConfig,
		allKpiConfigs: KpiConfig[],
		dependencySet: Set<string>,
	): void {
		const kpiConfigObj =
			typeof kpiConfig.kpi_config === 'string'
				? JSON.parse(kpiConfig.kpi_config as string)
				: kpiConfig.kpi_config;

		const formula = kpiConfigObj?.formula || '';
		const dependencies = extractDependentKpisFromFormula(formula);

		// Add all dependencies to the required KPIs set
		dependencies.forEach(depCode => dependencySet.add(depCode));

		// Recursively find nested dependencies
		let newDependenciesFound = true;
		while (newDependenciesFound) {
			newDependenciesFound = false;

			for (const depCode of dependencySet) {
				const depConfig = allKpiConfigs.find(kpi => kpi.system_code === depCode);

				if (depConfig && depConfig.kpi_type === 'formula') {
					const depConfigObj =
						typeof depConfig.kpi_config === 'string'
							? JSON.parse(depConfig.kpi_config as string)
							: depConfig.kpi_config;

					const depFormula = depConfigObj?.formula || '';
					const nestedDeps = extractDependentKpisFromFormula(depFormula);

					for (const nestedDep of nestedDeps) {
						if (!dependencySet.has(nestedDep)) {
							dependencySet.add(nestedDep);
							newDependenciesFound = true;
						}
					}
				}
			}
		}
	}

	/**
	 * Calculate values for a set of KPIs
	 * @param kpiConfigs Array of KPI configurations
	 * @param year The year for data retrieval
	 * @param inputValues Input values provided by the user
	 * @returns Object with calculated KPI values
	 */
	private async calculateKpiValues(
		kpiConfigs: KpiConfig[],
		business_entity_id: number,
		inputValues: Record<string, any> = {},
		overrideFetchMechanism?: string,
		startDate?: Date,
		endDate?: Date,
	): Promise<Record<string, any>> {
		const kpiValues: Record<string, any> = {};

		// Process input type KPIs
		const inputKpis = kpiConfigs.filter(kpi => kpi.kpi_type === 'input');
		for (const kpi of inputKpis) {
			kpiValues[kpi.system_code] = await this.calculateInputKpiValue(kpi, inputValues);
		}

		// Process pull_data type KPIs
		const pullDataKpis = kpiConfigs.filter(kpi => kpi.kpi_type === 'pull_data');
		for (const kpi of pullDataKpis) {
			kpiValues[kpi.system_code] = await this.calculatePullDataKpiValue(
				kpi,
				business_entity_id,
				overrideFetchMechanism,
				startDate,
				endDate,
			);
		}

		// Process formula type KPIs in dependency order
		const formulaKpis = kpiConfigs.filter(kpi => kpi.kpi_type === 'formula');
		await this.calculateFormulaKpis(formulaKpis, business_entity_id, kpiValues);

		return kpiValues;
	}

	private async calculateInputKpiValue(
		kpi: KpiConfig,
		inputValues: Record<string, any>,
	): Promise<any> {
		let value = inputValues[kpi.system_code];

		if (value === undefined || value === null || isNaN(Number(value))) {
			console.warn(`Invalid value for KPI ${kpi.system_code}: ${value}`);
			return -1;
		}

		return value;
	}

	private async calculatePullDataKpiValue(
		kpi: KpiConfig,
		business_entity_id: number,
		overrideFetchMechanism?: string,
		startDate?: Date,
		endDate?: Date,
	): Promise<any> {
		// Get KPI configuration
		const kpiConfigObj =
			typeof kpi.kpi_config === 'string' ? JSON.parse(kpi.kpi_config as string) : kpi.kpi_config;

		// Get the aggregated value directly from the fetch mechanism
		const value = await this.getKpiDataForFetchMechanism(
			kpi.system_code,
			business_entity_id,
			kpiConfigObj,
			overrideFetchMechanism,
			startDate,
			endDate,
		);

		// Round the value according to decimal places in config
		return this.roundKpiValue(value, kpi);
	}

	/**
	 * Detect and resolve circular dependencies in KPI formulas
	 * @param kpiConfigs Array of KPI configurations
	 * @returns Map of KPI codes to their dependencies and a set of KPIs with circular dependencies
	 */
	private detectCircularDependencies(kpiConfigs: KpiConfig[]): {
		dependencyMap: Map<string, Set<string>>;
		circularDependencies: Set<string>;
	} {
		// Create a dependency map for all KPIs
		const dependencyMap = new Map<string, Set<string>>();

		// Initialize the map with direct dependencies
		for (const kpi of kpiConfigs) {
			if (kpi.kpi_type === 'formula') {
				const kpiConfigObj =
					typeof kpi.kpi_config === 'string'
						? JSON.parse(kpi.kpi_config as string)
						: kpi.kpi_config;

				const formula = kpiConfigObj?.formula || '';
				const dependencies = extractDependentKpisFromFormula(formula);

				dependencyMap.set(kpi.system_code, new Set(dependencies));
			} else {
				dependencyMap.set(kpi.system_code, new Set());
			}
		}

		// Detect circular dependencies using depth-first search
		const circularDependencies = new Set<string>();
		const visited = new Set<string>();
		const recursionStack = new Set<string>();

		const dfs = (kpiCode: string, path: string[] = []): boolean => {
			// If already processed and no circular dependency found, skip
			if (visited.has(kpiCode) && !recursionStack.has(kpiCode)) {
				return false;
			}

			// If already in recursion stack, we found a circular dependency
			if (recursionStack.has(kpiCode)) {
				// Log the circular dependency path
				const cycleStart = path.indexOf(kpiCode);
				const cycle = path.slice(cycleStart).concat(kpiCode);
				console.warn(`Circular dependency detected: ${cycle.join(' -> ')}`);

				// Mark all KPIs in the cycle as having circular dependencies
				cycle.forEach(code => circularDependencies.add(code));
				return true;
			}

			// Mark as visited and add to recursion stack
			visited.add(kpiCode);
			recursionStack.add(kpiCode);
			path.push(kpiCode);

			// Check all dependencies
			const dependencies = dependencyMap.get(kpiCode) || new Set();
			let hasCircular = false;

			for (const dep of dependencies) {
				if (dfs(dep, [...path])) {
					hasCircular = true;
					// Don't return early, continue to find all circular paths
				}
			}

			// Remove from recursion stack
			recursionStack.delete(kpiCode);

			return hasCircular;
		};

		// Run DFS for each KPI
		for (const kpi of kpiConfigs) {
			if (!visited.has(kpi.system_code)) {
				dfs(kpi.system_code);
			}
		}

		return { dependencyMap, circularDependencies };
	}

	/**
	 * Calculate values for formula type KPIs in dependency order
	 * @param formulaKpis Array of formula KPI configurations
	 * @param business_entity_id The business entity ID
	 * @param kpiValues Object to store calculated KPI values
	 */
	private async calculateFormulaKpis(
		formulaKpis: KpiConfig[],
		business_entity_id: number,
		kpiValues: Record<string, any>,
	): Promise<void> {
		const allKpiConfigs = await this.kpiConfigRepository.getAll();

		// Detect circular dependencies
		const { dependencyMap, circularDependencies } = this.detectCircularDependencies(allKpiConfigs);

		// Log circular dependencies if found
		if (circularDependencies.size > 0) {
			console.warn(
				`Found ${circularDependencies.size} KPIs with circular dependencies: ${Array.from(
					circularDependencies,
				).join(', ')}`,
			);
		}

		// Create a set of all KPIs that have already been calculated (from input and pull_data types)
		const calculatedKpis = new Set<string>(
			Object.keys(kpiValues).filter(key => kpiValues[key] !== undefined),
		);

		// Create a set of pending formula KPIs to calculate
		const pendingKpis = new Set<string>(formulaKpis.map(kpi => kpi.system_code));

		// Create a dependency graph for topological sorting
		const graph = new Map<string, string[]>();
		const inDegree = new Map<string, number>();

		// Initialize the graph and in-degree counts
		for (const kpiCode of pendingKpis) {
			const dependencies: string[] = Array.from(dependencyMap.get(kpiCode) || new Set());
			graph.set(kpiCode, dependencies);

			// Count only dependencies that are formula KPIs and not yet calculated
			const pendingDependencies = dependencies.filter(
				dep => pendingKpis.has(dep) && !calculatedKpis.has(dep),
			);

			inDegree.set(kpiCode, pendingDependencies.length);
		}

		// Process KPIs with circular dependencies first using default value -1
		for (const kpiCode of pendingKpis) {
			if (circularDependencies.has(kpiCode)) {
				const kpi = formulaKpis.find(k => k.system_code === kpiCode);
				if (!kpi) continue;

				console.warn(`Resolving circular dependency for KPI ${kpiCode} using default value -1`);

				// Use default value -1 for circular dependencies
				kpiValues[kpiCode] = -1;
				pendingKpis.delete(kpiCode);
				calculatedKpis.add(kpiCode);

				// Update in-degree counts for KPIs that depend on this one
				for (const [dependent, dependencies] of graph.entries()) {
					if (dependencies.includes(kpiCode) && inDegree.has(dependent)) {
						inDegree.set(dependent, inDegree.get(dependent) - 1);
					}
				}

				console.info(`Resolved circular dependency for KPI ${kpiCode} using default value: -1`);
			}
		}

		// Queue for topological sorting (start with nodes that have no dependencies)
		const queue: string[] = [];
		for (const [kpiCode, degree] of inDegree.entries()) {
			if (degree === 0 && pendingKpis.has(kpiCode)) {
				queue.push(kpiCode);
			}
		}

		// Process KPIs in topological order
		let iterations = 0;
		const maxIterations = pendingKpis.size * 2; // Set a reasonable limit

		while (queue.length > 0 && pendingKpis.size > 0 && iterations < maxIterations) {
			iterations++;

			const kpiCode = queue.shift();
			if (!kpiCode || !pendingKpis.has(kpiCode)) continue;

			const kpi = formulaKpis.find(k => k.system_code === kpiCode);
			if (!kpi) continue;

			// Check if all dependencies (including non-formula ones) are calculated
			const allDependencies = Array.from(dependencyMap.get(kpiCode) || new Set());
			const allDependenciesCalculated = allDependencies.every(
				(depCode: string) => calculatedKpis.has(depCode) || kpiValues[depCode] !== undefined,
			);

			if (allDependenciesCalculated) {
				// Get KPI configuration
				const kpiConfigObj =
					typeof kpi.kpi_config === 'string'
						? JSON.parse(kpi.kpi_config as string)
						: kpi.kpi_config;

				const formula = kpiConfigObj?.formula || '';

				// Log the formula and dependency values for debugging
				console.debug(`Calculating formula for KPI ${kpiCode}: ${formula}`);
				console.debug(
					`Dependencies for KPI ${kpiCode}:`,
					Object.fromEntries(allDependencies.map((dep: any) => [dep, kpiValues[dep]])),
				);

				// Calculate formula KPI
				let result = evaluateKpiFormula(formula, kpiValues);

				// If calculation failed, use default value -1
				if (result === -1 || isNaN(result)) {
					console.warn(`Formula evaluation failed for KPI ${kpiCode}. Using default value -1.`);
					result = -1;
				}

				// Round the result
				result = this.roundKpiValue(result, kpi);

				console.debug(`Calculated value for KPI ${kpiCode}: ${result}`);

				// Store the result
				kpiValues[kpiCode] = result;
				pendingKpis.delete(kpiCode);
				calculatedKpis.add(kpiCode);

				// Update in-degree counts for KPIs that depend on this one
				// and add to queue if all dependencies are now satisfied
				for (const [dependent, dependencies] of graph.entries()) {
					if (dependencies.includes(kpiCode) && inDegree.has(dependent)) {
						const newDegree = inDegree.get(dependent) - 1;
						inDegree.set(dependent, newDegree);

						if (newDegree === 0 && pendingKpis.has(dependent)) {
							queue.push(dependent);
						}
					}
				}
			} else {
				// If dependencies aren't calculated yet, check which ones are missing
				const missingDependencies = allDependencies.filter(
					(depCode: any) => !calculatedKpis.has(depCode) && kpiValues[depCode] === undefined,
				);

				console.debug(
					`KPI ${kpiCode} is waiting for dependencies: ${missingDependencies.join(', ')}`,
				);

				// Check if any of the missing dependencies are in the pending list
				const pendingDependencies = missingDependencies.filter((dep: any) => pendingKpis.has(dep));

				if (pendingDependencies.length > 0) {
					// If there are pending dependencies that might be calculated later,
					// put this KPI back at the end of the queue, but only if we haven't
					// gone through the entire queue without making progress
					queue.push(kpiCode);
				} else {
					// If none of the missing dependencies are in the pending list,
					// they're external dependencies that won't be calculated in this run
					console.warn(
						`KPI ${kpiCode} has missing external dependencies: ${missingDependencies.join(
							', ',
						)}. Using default value -1.`,
					);
					kpiValues[kpiCode] = -1;
					pendingKpis.delete(kpiCode);
					calculatedKpis.add(kpiCode);

					// Update in-degree counts for KPIs that depend on this one
					for (const [dependent, dependencies] of graph.entries()) {
						if (dependencies.includes(kpiCode) && inDegree.has(dependent)) {
							const newDegree = inDegree.get(dependent) - 1;
							inDegree.set(dependent, newDegree);

							if (newDegree === 0 && pendingKpis.has(dependent)) {
								queue.push(dependent);
							}
						}
					}
				}
			}
		}

		// If we hit the iteration limit, handle remaining KPIs
		if (iterations >= maxIterations && pendingKpis.size > 0) {
			console.warn(
				`Reached maximum iterations (${maxIterations}) while calculating KPIs. ` +
					`Unable to calculate ${pendingKpis.size} KPIs: ${Array.from(pendingKpis).join(', ')}`,
			);

			// Use default values for remaining KPIs
			for (const kpiCode of pendingKpis) {
				kpiValues[kpiCode] = -1;
			}
		}
	}

	/**
	 * Rounds a KPI value according to the decimal places specified in its configuration
	 * @param value The value to round
	 * @param kpi The KPI configuration
	 * @returns The rounded value
	 */
	private roundKpiValue(value: any, kpi: KpiConfig): number {
		// Handle invalid values
		if (value === undefined || value === null || isNaN(Number(value))) {
			console.warn(`Invalid value for KPI ${kpi.system_code}: ${value}`);
			return -1;
		}

		// Convert to number if it's not already
		const numValue = Number(value);

		try {
			// Get decimal places from KPI config
			const kpiConfig =
				typeof kpi.kpi_config === 'string' ? JSON.parse(kpi.kpi_config as string) : kpi.kpi_config;

			const decimalPlaces =
				kpiConfig?.decimal_places !== undefined ? parseInt(kpiConfig.decimal_places, 10) : 0;

			if (isNaN(decimalPlaces)) return numValue;

			// Round to specified decimal places
			const factor = Math.pow(10, decimalPlaces);
			return Math.round(numValue * factor) / factor;
		} catch (error) {
			console.error('Error rounding KPI value:', error);
			return numValue; // Return original value if rounding fails
		}
	}

	/**
	 * Aggregates KPI values based on the specified method
	 * @param values Array of values to aggregate
	 * @param method Aggregation method (SUM, AVG, MAX, MIN, etc.)
	 * @returns The aggregated value
	 */
	private aggregateKpiValues(values: (number | undefined)[], method: string): number {
		// Filter out undefined values
		const validValues = values.filter(v => v !== undefined) as number[];

		if (validValues.length === 0) return 0;

		switch (method.toUpperCase()) {
			case 'SUM':
				return validValues.reduce((sum, val) => sum + val, 0);

			case 'AVG':
				return validValues.reduce((sum, val) => sum + val, 0) / validValues.length;

			case 'MAX':
				return Math.max(...validValues);

			case 'MIN':
				return Math.min(...validValues);

			case 'COUNT':
				return validValues.length;

			case 'LAST':
				return validValues[validValues.length - 1];

			case 'FIRST':
				return validValues[0];

			default:
				return validValues.reduce((sum, val) => sum + val, 0); // Default to SUM
		}
	}

	/**
	 * Get KPI data based on the fetch mechanism with optimized database queries
	 * @param kpiCode The KPI code to filter by
	 * @param business_entity_id The business entity ID
	 * @param kpiConfig The KPI configuration object
	 * @returns Single aggregated value for the KPI
	 */
	private async getKpiDataForFetchMechanism(
		kpiCode: string,
		business_entity_id: number,
		kpiConfig: any,
		overrideFetchMechanism?: string,
		startDate?: Date,
		endDate?: Date,
	): Promise<number> {
		const currentYear = new Date().getFullYear();
		const currentMonth = new Date().getMonth() + 1;

		// Get aggregation methods and data type
		const aggregateMethod = kpiConfig?.aggregate_method || 'SUM';
		const monthlyAggregateMethod = kpiConfig?.monthly_aggregate_method || 'SUM';
		const dataType = kpiConfig?.data_type || 'Month';
		const source_kpi = kpiConfig?.source_kpi || kpiCode;

		let fetchMechanism =
			overrideFetchMechanism && overrideFetchMechanism !== 'default'
				? overrideFetchMechanism
				: kpiConfig?.fetch_mechanism || 'year_to_date';

		// Import Sequelize operators
		const sequlizeOperator = new SequlizeOperator();

		// Build the WHERE clause based on fetch mechanism
		let whereCondition: any = {
			kpi_code: source_kpi,
			business_entity_id: business_entity_id,
		};

		switch (fetchMechanism) {
			case 'last_12_month':
				// Get last 12 months from current date
				const last12MonthsStart = new Date();
				last12MonthsStart.setMonth(last12MonthsStart.getMonth() - 12);
				const startYear12 = last12MonthsStart.getFullYear();
				const startMonth12 = last12MonthsStart.getMonth() + 1;

				whereCondition = {
					kpi_code: source_kpi,
					business_entity_id: business_entity_id,
					...sequlizeOperator.orOperator([
						// Full years between start and current year
						{
							kpi_year: sequlizeOperator.betweenOperator(startYear12 + 1, currentYear - 1),
						},
						// Start year: from start month to December
						{
							kpi_year: startYear12,
							kpi_month: sequlizeOperator.greaterThanEqualOperator(startMonth12),
						},
						// Current year: from January to current month
						{
							kpi_year: currentYear,
							kpi_month: sequlizeOperator.lessThanEqualOperator(currentMonth),
						},
					]),
				};
				break;

			case 'year_to_date':
				whereCondition = {
					kpi_code: source_kpi,
					business_entity_id: business_entity_id,
					kpi_year: currentYear,
					kpi_month: sequlizeOperator.lessThanEqualOperator(currentMonth),
				};
				break;

			case 'last_year':
				whereCondition = {
					kpi_code: source_kpi,
					business_entity_id: business_entity_id,
					kpi_year: currentYear - 1,
				};
				break;

			case 'custom':
				if (!startDate || !endDate) {
					throw new Error('Start date and end date are required for custom fetch mechanism');
				}

				const startYear = new Date(startDate).getFullYear();
				const startMonth = new Date(startDate).getMonth() + 1;
				const endYear = new Date(endDate).getFullYear();
				const endMonth = new Date(endDate).getMonth() + 1;

				if (startYear === endYear) {
					// Same year: simple month range
					whereCondition = {
						kpi_code: source_kpi,
						business_entity_id: business_entity_id,
						kpi_year: startYear,
						kpi_month: sequlizeOperator.betweenOperator(startMonth, endMonth),
					};
				} else {
					// Cross-year range
					whereCondition = {
						kpi_code: source_kpi,
						business_entity_id: business_entity_id,
						...sequlizeOperator.orOperator([
							// Full years between start and end year
							{
								kpi_year: sequlizeOperator.betweenOperator(startYear + 1, endYear - 1),
							},
							// Start year: from start month to December
							{
								kpi_year: startYear,
								kpi_month: sequlizeOperator.greaterThanEqualOperator(startMonth),
							},
							// End year: from January to end month
							{
								kpi_year: endYear,
								kpi_month: sequlizeOperator.lessThanEqualOperator(endMonth),
							},
						]),
					};
				}
				break;

			case 'last_value':
				// For last_value, we'll use findOne with order
				const lastValue = await this.kpiDataRepository.findOne({
					where: {
						kpi_code: source_kpi,
						business_entity_id: business_entity_id,
					},
					order: [
						['kpi_year', 'DESC'],
						['kpi_month', 'DESC'],
						['kpi_day', 'DESC'],
					],
				});
				return lastValue?.kpi_value !== undefined ? Number(lastValue.kpi_value) : 0;

			default:
				whereCondition = {
					kpi_code: source_kpi,
					business_entity_id: business_entity_id,
					kpi_year: currentYear,
				};
		}

		// For Year and Day data types, we can use Sequelize's built-in aggregation
		if (dataType === 'Year' || dataType === 'Day') {
			const result = await this.kpiDataRepository.findOneRaw({
				attributes: [[Sequelize.fn(aggregateMethod, Sequelize.col('kpi_value')), 'kpi_value']],
				where: whereCondition,
			});

			return result?.kpi_value !== undefined ? Number(result.kpi_value) : 0;
		}
		// For Month data type, we need to first group by month and then aggregate
		else if (dataType === 'Month') {
			try {
				// First, get monthly aggregated values
				const monthlyData = await this.kpiDataRepository.findAllRaw({
					attributes: [
						'kpi_month',
						[Sequelize.fn(monthlyAggregateMethod, Sequelize.col('kpi_value')), 'monthly_value'],
					],
					where: whereCondition,
					group: ['kpi_month'],
				});

				// If no monthly data, return 0
				if (!monthlyData || monthlyData.length === 0) {
					return 0;
				}

				// Extract monthly values for final aggregation
				const monthlyValues = monthlyData.map(item => {
					const value = item.monthly_value;
					return value !== undefined && value !== null ? Number(value) : 0;
				});

				// Perform final aggregation based on the aggregateMethod
				switch (aggregateMethod.toUpperCase()) {
					case 'SUM':
						return monthlyValues.reduce((sum, val) => sum + val, 0);
					case 'AVG':
						return monthlyValues.reduce((sum, val) => sum + val, 0) / monthlyValues.length;
					case 'MAX':
						return Math.max(...monthlyValues);
					case 'MIN':
						return Math.min(...monthlyValues);
					case 'COUNT':
						return monthlyValues.length;
					case 'LAST':
						return monthlyValues[monthlyValues.length - 1];
					case 'FIRST':
						return monthlyValues[0];
					default:
						return monthlyValues.reduce((sum, val) => sum + val, 0); // Default to SUM
				}
			} catch (error) {
				console.error('Error fetching monthly data:', error);
				return 0;
			}
		} else {
			return -1;
		}
	}

	/**
	 * Find all dependencies (including nested) for a set of KPI codes
	 * @param kpiCodes Initial KPI codes to find dependencies for
	 * @returns Set of all required KPI codes including the initial ones
	 */
	public async findAllDependencies(kpiCodes: string[]): Promise<Set<string>> {
		// Get all KPI configurations
		const allKpiConfigs = await this.kpiConfigRepository.getAll();

		// Create a set to track all KPIs we need to calculate
		const requiredKpiCodes = new Set<string>(kpiCodes);

		// Create a dependency map for all KPIs
		const dependencyMap = new Map<string, string[]>();

		// Initialize the map with direct dependencies
		for (const kpi of allKpiConfigs) {
			if (kpi.kpi_type === 'formula') {
				const kpiConfig =
					typeof kpi.kpi_config === 'string'
						? JSON.parse(kpi.kpi_config as string)
						: kpi.kpi_config;

				const formula = kpiConfig?.formula || '';
				const dependencies = extractDependentKpisFromFormula(formula);

				dependencyMap.set(kpi.system_code, dependencies);
			} else {
				dependencyMap.set(kpi.system_code, []);
			}
		}

		// Track visited KPIs to avoid infinite recursion
		const visited = new Set<string>();

		// Recursive function to find all dependencies
		const findDependencies = (kpiCode: string) => {
			// Skip if already visited
			if (visited.has(kpiCode)) return;

			// Mark as visited
			visited.add(kpiCode);

			// Get dependencies
			const dependencies = dependencyMap.get(kpiCode) || [];

			// Add dependencies to required set
			for (const depCode of dependencies) {
				requiredKpiCodes.add(depCode);

				// Recursively find nested dependencies
				findDependencies(depCode);
			}
		};

		// Find dependencies for all initial KPI codes
		for (const kpiCode of kpiCodes) {
			findDependencies(kpiCode);
		}

		return requiredKpiCodes;
	}

	public async filterKpisByExcludedEquipment(
		kpiConfigs: KpiConfig[],
		business_entity_id: number,
	): Promise<KpiConfig[]> {
		// Check if there's a bu_setup entry for this entity
		const buSetup = await this.buSetupRepository.findOneRaw({
			where: { entity_id: business_entity_id },
		});

		if (!buSetup || !buSetup.excluded_equipment_type) {
			return kpiConfigs;
		}

		const excludedEquipmentTypes = Array.isArray(buSetup.excluded_equipment_type)
			? buSetup.excluded_equipment_type
			: JSON.parse(buSetup.excluded_equipment_type);

		// Filter out KPIs that match excluded equipment types
		return kpiConfigs.filter((kpi: KpiConfig) => {
			const kpiEquipmentTypes = Array.isArray(kpi.applicable_equipment_type)
				? kpi.applicable_equipment_type
				: JSON.parse((kpi.applicable_equipment_type as any) || '[]');
			
			// Return true if no overlap between KPI equipment types and excluded types
			return !kpiEquipmentTypes.some(equipmentType => 
				excludedEquipmentTypes.includes(equipmentType)
			);
		});
	}
}






