import * as msal from '@azure/msal-node';
import { ConfigService } from 'src/config/config.service';
import { HttpService } from '../services';
import { ADUserDetails } from '../types';
export declare class MSGraphApiClient {
    private readonly msGraphApiProvider;
    private readonly msGraphApiHttpService;
    private readonly configService;
    private tokenRequest;
    private azureADConfig;
    constructor(msGraphApiProvider: msal.ConfidentialClientApplication, msGraphApiHttpService: HttpService, configService: ConfigService);
    private getToken;
    private getHeaders;
    getUserDetails(userId: string): Promise<ADUserDetails>;
    getUserDetailsByEmail(email: string): Promise<ADUserDetails>;
    getUsersDetails(userIds: string[]): Promise<ADUserDetails[]>;
}
