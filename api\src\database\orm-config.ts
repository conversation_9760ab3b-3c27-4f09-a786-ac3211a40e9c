import { QueueLog } from 'src/queue/models';
import { SyncLog } from 'src/scheduler/models/sync_log.model';
import { BuSetup, KpiConfig, KpiData } from 'src/tc-config/models';
import { Model, ModelScenario, ModelScenarioData } from 'src/tc-submit/models';


const models = [
	SyncLog,
	QueueLog,
	BuSetup,
	KpiConfig,
	KpiData,
	Model,
	ModelScenario,
	ModelScenarioData
];

export const getSequelizeOrmConfig = (enableSSL) => {
	return {
		synchronize: false,
		autoLoadModels: true,
		models,
		...(enableSSL && {
			ssl: true,
			dialectOptions: {
				ssl: {
					require: true,
				},
			},
		}),
	}
}
