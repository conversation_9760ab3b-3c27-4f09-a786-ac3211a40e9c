import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsArray, IsNotEmpty, IsOptional } from 'class-validator';

export class ModelScenarioDataRequestDto {

	@ApiProperty({ name: 'scenario_id', type: Number })
	@Expose({ name: 'scenario_id' })
	@IsNotEmpty()
	public scenario_id: number;

	@ApiProperty({ name: 'kpi_code', type: String })
	@Expose({ name: 'kpi_code' })
	@IsNotEmpty()
	public kpi_code: string | null;

	@ApiProperty({ name: 'value', type: Number })
	@Expose({ name: 'value' })
	@IsNotEmpty()
	public value: number;
}
