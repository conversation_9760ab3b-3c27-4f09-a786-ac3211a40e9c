import { BaseRepository } from 'src/shared/repositories';
import { KpiData } from '../models';
export declare class KpiDataRepository extends BaseRepository<KpiData> {
    constructor();
    getAllByMonthYear(year: number, month: number): Promise<KpiData[] | null>;
    deleteAllKpiCodeByDateRange(kpiCode: String, startYear: number, endYear: number, startMonth: number, endMonth: number): Promise<any>;
}
