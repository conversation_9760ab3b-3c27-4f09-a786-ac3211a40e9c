import { AdminApiClient } from 'src/shared/clients';
import { CurrentContext } from 'src/shared/types';
export declare class BusinessEntityService {
    private readonly adminApiClient;
    constructor(adminApiClient: AdminApiClient);
    getBusinessEntitiesForGivenPermissionForUser(currentContext: CurrentContext, permission: string, parentId?: number, lastLevel?: string): Promise<Record<string, any>>;
    getAllBusinessEntityLevels(): Promise<Record<string, any>>;
    getAllBusinessEntityRoles(entityLevel: string): Promise<Record<string, any>>;
    hasAccessOnBusinessUnit(currentContext: CurrentContext, entityId: number): Promise<boolean>;
}
