{"version": 3, "file": "datalake-connection.helper.js", "sourceRoot": "", "sources": ["../../../src/shared/helpers/datalake-connection.helper.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,gEAA0D;AAC1D,kDAA6C;AAatC,IAAM,wBAAwB,gCAA9B,MAAM,wBAAwB;IAIpC,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QAHxC,WAAM,GAAG,IAAI,eAAM,CAAC,0BAAwB,CAAC,IAAI,CAAC,CAAC;QAC5D,gBAAW,GAAsC,IAAI,GAAG,EAAE,CAAC;QAGlE,IAAI,CAAC,qBAAqB,EAAE,CAAC;IAC9B,CAAC;IAKO,qBAAqB;QAC5B,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC;QAC3D,IAAI,CAAC,YAAY,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE;YAC9C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;YACtD,OAAO;SACP;QAED,IAAI;YACH,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,YAAY,CAAC,UAAU,CAAC;YAGhE,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,kDAAkD,CAAC,CAAC;YACpF,IAAI,CAAC,SAAS,EAAE;gBACf,MAAM,IAAI,KAAK,CAAC,0HAA0H,CAAC,CAAC;aAC5I;YAED,MAAM,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAC1B,MAAM,QAAQ,GAAG,kBAAkB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAGlD,MAAM,UAAU,GAAG,eAAK,CAAC,MAAM,CAAC;gBAC/B,OAAO,EAAE,WAAW,IAAI,EAAE;gBAC1B,OAAO,EAAE,MAAM;gBACf,OAAO,EAAE;oBACR,cAAc,EAAE,kBAAkB;oBAClC,eAAe,EAAE,SAAS,MAAM,CAAC,IAAI,CAAC,GAAG,QAAQ,IAAI,QAAQ,EAAE,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;iBACrF;aACD,CAAC,CAAC;YAEH,MAAM,UAAU,GAAyB;gBACxC,OAAO;gBACP,QAAQ;gBACR,QAAQ;gBACR,UAAU;gBACV,IAAI;gBACJ,QAAQ;gBACR,WAAW,EAAE,KAAK;aAClB,CAAC;YAGF,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC;iBAC7B,IAAI,CAAC,GAAG,EAAE;gBACV,UAAU,CAAC,WAAW,GAAG,IAAI,CAAC;gBAC9B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kDAAkD,IAAI,EAAE,CAAC,CAAC;gBAC1E,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;YAC7C,CAAC,CAAC;iBACD,KAAK,CAAC,CAAC,KAAU,EAAE,EAAE;gBACrB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACxE,CAAC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAU,EAAE;YACpB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6CAA6C,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;SAChF;IACF,CAAC;IAKa,cAAc,CAAC,UAAgC;;;YAC5D,IAAI;gBAEH,MAAM,WAAW,GAAG,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;gBACzD,IAAI,CAAC,WAAW,EAAE;oBACjB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;iBACtD;gBAGD,MAAM,QAAQ,GAAG,MAAM,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,yBAAyB,EAAE;oBAC5E,SAAS,EAAE,kBAAkB;oBAC7B,YAAY,EAAE,WAAW;oBACzB,YAAY,EAAE,KAAK;iBACnB,CAAC,CAAC;gBAEH,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE;oBAC5B,MAAM,IAAI,KAAK,CAAC,uCAAuC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;iBAC1E;gBAED,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC;gBAC7B,IAAI,CAAA,MAAA,MAAM,CAAC,MAAM,0CAAE,KAAK,MAAK,QAAQ,EAAE;oBACtC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAA,MAAA,MAAM,CAAC,MAAM,CAAC,KAAK,0CAAE,OAAO,KAAI,eAAe,EAAE,CAAC,CAAC;iBAC9F;aACD;YAAC,OAAO,KAAU,EAAE;gBACpB,MAAM,IAAI,KAAK,CAAC,2BAA2B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;aAC5D;;KACD;IAOM,aAAa,CAAC,OAAe,SAAS;QAC5C,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAChC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,IAAI,aAAa,CAAC,CAAC;YAC9D,OAAO,IAAI,CAAC;SACZ;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;IAC3C,CAAC;IAQY,YAAY,CACxB,KAAa,EACb,iBAAyB,SAAS;;;YAElC,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;YACtD,IAAI,CAAC,UAAU,EAAE;gBAChB,MAAM,IAAI,KAAK,CAAC,0BAA0B,cAAc,aAAa,CAAC,CAAC;aACvE;YAED,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE;gBAC5B,MAAM,IAAI,KAAK,CAAC,0BAA0B,cAAc,oBAAoB,CAAC,CAAC;aAC9E;YAED,IAAI;gBAEH,MAAM,WAAW,GAAG,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;gBACzD,IAAI,CAAC,WAAW,EAAE;oBACjB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;iBACtD;gBAGD,MAAM,QAAQ,GAAG,MAAM,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,yBAAyB,EAAE;oBAC5E,SAAS,EAAE,KAAK;oBAChB,YAAY,EAAE,WAAW;oBACzB,YAAY,EAAE,MAAM;oBACpB,eAAe,EAAE,QAAQ;iBACzB,CAAC,CAAC;gBAEH,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE;oBAC5B,MAAM,IAAI,KAAK,CAAC,uCAAuC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;iBAC1E;gBAGD,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC;gBAC7B,IAAI,CAAA,MAAA,MAAM,CAAC,MAAM,0CAAE,KAAK,MAAK,WAAW,EAAE;oBACzC,OAAO,CAAA,MAAA,MAAM,CAAC,MAAM,0CAAE,UAAU,KAAI,EAAE,CAAC;iBACvC;qBAAM,IAAI,CAAA,MAAA,MAAM,CAAC,MAAM,0CAAE,KAAK,MAAK,QAAQ,EAAE;oBAC7C,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAA,MAAA,MAAM,CAAC,MAAM,CAAC,KAAK,0CAAE,OAAO,KAAI,eAAe,EAAE,CAAC,CAAC;iBACpF;qBAAM;oBACN,MAAM,IAAI,KAAK,CAAC,8BAA8B,MAAA,MAAM,CAAC,MAAM,0CAAE,KAAK,EAAE,CAAC,CAAC;iBACtE;aACD;YAAC,OAAO,KAAU,EAAE;gBACpB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wCAAwC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBAC3E,MAAM,KAAK,CAAC;aACZ;;KACD;CACD,CAAA;AAjKY,wBAAwB;IADpC,IAAA,mBAAU,GAAE;qCAKgC,8BAAa;GAJ7C,wBAAwB,CAiKpC;AAjKY,4DAAwB"}