{"version": 3, "file": "datalake-connection.helper.js", "sourceRoot": "", "sources": ["../../../src/shared/helpers/datalake-connection.helper.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,gEAA0D;AAC1D,kDAA6C;AAatC,IAAM,wBAAwB,gCAA9B,MAAM,wBAAwB;IAIpC,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QAHxC,WAAM,GAAG,IAAI,eAAM,CAAC,0BAAwB,CAAC,IAAI,CAAC,CAAC;QAC5D,gBAAW,GAAsC,IAAI,GAAG,EAAE,CAAC;QAGlE,IAAI,CAAC,qBAAqB,EAAE,CAAC;IAC9B,CAAC;IAKO,qBAAqB;QAC5B,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC;QAC3D,IAAI,CAAC,YAAY,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE;YAC9C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;YACtD,OAAO;SACP;QAED,IAAI;YACH,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,YAAY,CAAC,UAAU,CAAC;YAGhE,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,kDAAkD,CAAC,CAAC;YACpF,IAAI,CAAC,SAAS,EAAE;gBACf,MAAM,IAAI,KAAK,CAAC,0HAA0H,CAAC,CAAC;aAC5I;YAED,MAAM,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAC1B,MAAM,QAAQ,GAAG,kBAAkB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAIlD,IAAI,UAAkB,CAAC;YACvB,IAAI,QAAQ,CAAC,WAAW,EAAE,KAAK,OAAO,EAAE;gBAEvC,UAAU,GAAG,UAAU,QAAQ,EAAE,CAAC;aAClC;iBAAM;gBAEN,UAAU,GAAG,SAAS,MAAM,CAAC,IAAI,CAAC,GAAG,QAAQ,IAAI,QAAQ,EAAE,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;aAClF;YAED,MAAM,UAAU,GAAG,eAAK,CAAC,MAAM,CAAC;gBAC/B,OAAO,EAAE,WAAW,IAAI,EAAE;gBAC1B,OAAO,EAAE,MAAM;gBACf,OAAO,EAAE;oBACR,cAAc,EAAE,kBAAkB;oBAClC,eAAe,EAAE,UAAU;iBAC3B;aACD,CAAC,CAAC;YAEH,MAAM,UAAU,GAAyB;gBACxC,OAAO;gBACP,QAAQ;gBACR,QAAQ;gBACR,UAAU;gBACV,IAAI;gBACJ,QAAQ;gBACR,WAAW,EAAE,KAAK;aAClB,CAAC;YAGF,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC;iBAC7B,IAAI,CAAC,GAAG,EAAE;gBACV,UAAU,CAAC,WAAW,GAAG,IAAI,CAAC;gBAC9B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kDAAkD,IAAI,EAAE,CAAC,CAAC;gBAC1E,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;YAC7C,CAAC,CAAC;iBACD,KAAK,CAAC,CAAC,KAAU,EAAE,EAAE;gBACrB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACxE,CAAC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAU,EAAE;YACpB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6CAA6C,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;SAChF;IACF,CAAC;IAKa,cAAc,CAAC,UAAgC;;;YAC5D,IAAI;gBAEH,MAAM,WAAW,GAAG,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;gBACzD,IAAI,CAAC,WAAW,EAAE;oBACjB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;iBACtD;gBAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,oCAAoC,WAAW,EAAE,CAAC,CAAC;gBAGnE,MAAM,WAAW,GAAG;oBACnB,SAAS,EAAE,kBAAkB;oBAC7B,YAAY,EAAE,WAAW;oBACzB,YAAY,EAAE,KAAK;oBACnB,MAAM,EAAE,YAAY;oBACpB,WAAW,EAAE,QAAQ;iBACrB,CAAC;gBAEF,MAAM,QAAQ,GAAG,MAAM,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,yBAAyB,EAAE,WAAW,CAAC,CAAC;gBAE1F,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE;oBAC5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;oBACrE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;oBACzE,MAAM,IAAI,KAAK,CAAC,uCAAuC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;iBAC1E;gBAED,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC;gBAC7B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,2BAA2B,MAAA,MAAM,CAAC,MAAM,0CAAE,KAAK,EAAE,CAAC,CAAC;gBAEnE,IAAI,CAAA,MAAA,MAAM,CAAC,MAAM,0CAAE,KAAK,MAAK,QAAQ,EAAE;oBACtC,MAAM,QAAQ,GAAG,CAAA,MAAA,MAAM,CAAC,MAAM,CAAC,KAAK,0CAAE,OAAO,KAAI,eAAe,CAAC;oBACjE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,QAAQ,EAAE,CAAC,CAAC;oBACzD,MAAM,IAAI,KAAK,CAAC,2BAA2B,QAAQ,EAAE,CAAC,CAAC;iBACvD;gBAED,IAAI,CAAA,MAAA,MAAM,CAAC,MAAM,0CAAE,KAAK,MAAK,WAAW,EAAE;oBACzC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;iBAC9C;aACD;YAAC,OAAO,KAAU,EAAE;gBACpB,IAAI,KAAK,CAAC,QAAQ,EAAE;oBACnB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,KAAK,CAAC,QAAQ,CAAC,MAAM,KAAK,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;oBACvF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;iBACpF;gBACD,MAAM,IAAI,KAAK,CAAC,2BAA2B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;aAC5D;;KACD;IAOM,aAAa,CAAC,OAAe,SAAS;QAC5C,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAChC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,IAAI,aAAa,CAAC,CAAC;YAC9D,OAAO,IAAI,CAAC;SACZ;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;IAC3C,CAAC;IAQY,YAAY,CACxB,KAAa,EACb,iBAAyB,SAAS;;;YAElC,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;YACtD,IAAI,CAAC,UAAU,EAAE;gBAChB,MAAM,IAAI,KAAK,CAAC,0BAA0B,cAAc,aAAa,CAAC,CAAC;aACvE;YAED,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE;gBAC5B,MAAM,IAAI,KAAK,CAAC,0BAA0B,cAAc,oBAAoB,CAAC,CAAC;aAC9E;YAED,IAAI;gBAEH,MAAM,WAAW,GAAG,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;gBACzD,IAAI,CAAC,WAAW,EAAE;oBACjB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;iBACtD;gBAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iCAAiC,WAAW,EAAE,CAAC,CAAC;gBAChE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;gBAGxD,MAAM,WAAW,GAAG;oBACnB,SAAS,EAAE,KAAK;oBAChB,YAAY,EAAE,WAAW;oBACzB,YAAY,EAAE,MAAM;oBACpB,eAAe,EAAE,QAAQ;oBACzB,MAAM,EAAE,YAAY;oBACpB,WAAW,EAAE,QAAQ;iBACrB,CAAC;gBAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iBAAiB,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;gBAEzE,MAAM,QAAQ,GAAG,MAAM,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,yBAAyB,EAAE,WAAW,CAAC,CAAC;gBAE1F,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE;oBAC5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;oBACrD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;oBACzE,MAAM,IAAI,KAAK,CAAC,uCAAuC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;iBAC1E;gBAGD,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC;gBAC7B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,MAAA,MAAM,CAAC,MAAM,0CAAE,KAAK,EAAE,CAAC,CAAC;gBAEhE,IAAI,CAAA,MAAA,MAAM,CAAC,MAAM,0CAAE,KAAK,MAAK,WAAW,EAAE;oBACzC,MAAM,IAAI,GAAG,CAAA,MAAA,MAAM,CAAC,MAAM,0CAAE,UAAU,KAAI,EAAE,CAAC;oBAC7C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAkB,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC;oBACtD,OAAO,IAAI,CAAC;iBACZ;qBAAM,IAAI,CAAA,MAAA,MAAM,CAAC,MAAM,0CAAE,KAAK,MAAK,QAAQ,EAAE;oBAC7C,MAAM,QAAQ,GAAG,CAAA,MAAA,MAAM,CAAC,MAAM,CAAC,KAAK,0CAAE,OAAO,KAAI,eAAe,CAAC;oBACjE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB,QAAQ,EAAE,CAAC,CAAC;oBAC/C,MAAM,IAAI,KAAK,CAAC,iBAAiB,QAAQ,EAAE,CAAC,CAAC;iBAC7C;qBAAM,IAAI,CAAA,MAAA,MAAM,CAAC,MAAM,0CAAE,KAAK,MAAK,SAAS,IAAI,CAAA,MAAA,MAAM,CAAC,MAAM,0CAAE,KAAK,MAAK,SAAS,EAAE;oBAEpF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,MAAM,CAAC,MAAM,CAAC,KAAK,uCAAuC,CAAC,CAAC;oBAC/F,MAAM,IAAI,KAAK,CAAC,mCAAmC,MAAA,MAAM,CAAC,MAAM,0CAAE,KAAK,EAAE,CAAC,CAAC;iBAC3E;qBAAM;oBACN,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,MAAA,MAAM,CAAC,MAAM,0CAAE,KAAK,EAAE,CAAC,CAAC;oBACrE,MAAM,IAAI,KAAK,CAAC,8BAA8B,MAAA,MAAM,CAAC,MAAM,0CAAE,KAAK,EAAE,CAAC,CAAC;iBACtE;aACD;YAAC,OAAO,KAAU,EAAE;gBACpB,IAAI,KAAK,CAAC,QAAQ,EAAE;oBACnB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,KAAK,CAAC,QAAQ,CAAC,MAAM,KAAK,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;oBACvF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;iBACpF;gBACD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wCAAwC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBAC3E,MAAM,KAAK,CAAC;aACZ;;KACD;CACD,CAAA;AAzNY,wBAAwB;IADpC,IAAA,mBAAU,GAAE;qCAKgC,8BAAa;GAJ7C,wBAAwB,CAyNpC;AAzNY,4DAAwB"}