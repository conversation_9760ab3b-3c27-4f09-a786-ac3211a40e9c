import { Column, DataType, Table } from 'sequelize-typescript';
import { QUEUE_LOG_ACTION } from 'src/shared/enums';
import { enumToArray } from 'src/shared/helpers';
import { BaseModel } from 'src/shared/models';

@Table({ tableName: 'queue_logs' })
export class QueueLog extends BaseModel<QueueLog> {
    @Column({ field: 'entity_id', type: DataType.INTEGER, allowNull: true })
	public entityId?: number | null;
    
    @Column({
        field: 'action',
        type: DataType.ENUM(...enumToArray(QUEUE_LOG_ACTION)),
        allowNull: true, 
    })
	public action?: QUEUE_LOG_ACTION | null;

    @Column({ field: 'final_approval', type: DataType.BOOLEAN, allowNull: false, defaultValue: false })
    public finalApproval: boolean;

    @Column({ field: 'data', type: DataType.JSONB, allowNull: false })
	public data: any;

    @Column({ field: 'processed', type: DataType.BOOLEAN, allowNull: false, defaultValue: false })
	public processed: boolean;
}
