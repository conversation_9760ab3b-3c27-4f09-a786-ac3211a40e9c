import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsBoolean, IsOptional } from 'class-validator';

export class BusinessEntityLevelResponseDto {
	@ApiProperty({ name: 'levelName' })
	@Expose({ name: 'levelName' })
	@IsOptional()
	public levelname?: string;

	@ApiProperty({ name: 'isNodeType' })
	@Expose({ name: 'isNodeType' })
	@IsOptional()
	@IsBoolean()
	public isnodetype?: boolean;

	@ApiProperty({ name: 'orderNumber' })
	@Expose({ name: 'orderNumber' })
	@IsOptional()
	public order_number?: number;

	constructor(partial: Partial<BusinessEntityLevelResponseDto> = {}) {
		Object.assign(this, partial);
	}
}
