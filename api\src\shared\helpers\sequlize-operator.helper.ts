import { Op, Sequelize } from "sequelize";

export class SequlizeOperator {
    constructor() {}

    public greaterThanOperator(value: any): any{
        return {
            [Op.gt]: value
        }
    }

    public lessThanOperator(value: any): any{
        return {
            [Op.lt]: value
        }
    }

    public orOperator(value: any): any{
        return {
            [Op.or]: value
        }
    }

    public andOperator(value: any): any{
        return {
            [Op.and]: value
        }
    }

    public notEqualOperator(value: any): any{
        return {
            [Op.ne]: value
        }
    }

    public inOperator(values: any[]): any{
        return {
            [Op.in]: values
        }
    }

    public columnOperator(values: any): any{
        return Sequelize.col(values)
    }

    public whereOperator(column: any, operator: any, values: any): any{
        return Sequelize.where(column, operator, values);
    }

    public betweenOperator(start: any, end: any): any {
        return {
            [Op.between]: [start, end]
        }
    }

    public greaterThanEqualOperator(value: any): any {
        return {
            [Op.gte]: value
        }
    }

    public lessThanEqualOperator(value: any): any {
        return {
            [Op.lte]: value
        }
    }

    /**
     * Create a column function expression (e.g., SUM(column))
     * @param func Function name (e.g., 'SUM', 'AVG', 'MAX')
     * @param column Column name
     * @returns Sequelize function expression
     */
    public columnFun(func: string, column: string): any {
        // Use Sequelize.col to reference the column properly in PostgreSQL
        return Sequelize.fn(func, Sequelize.col(column));
    }

    /**
     * Create a raw SQL literal
     * @param value SQL expression
     * @returns Sequelize literal
     */
    public literalOperator(value: string): any {
        return Sequelize.literal(value);
    }
}
