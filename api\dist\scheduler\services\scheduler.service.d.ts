import { SyncLogRepository } from '../repositories';
import { KpiDataRepository } from 'src/tc-config/repositories';
import { DatalakeConnectionHelper } from 'src/shared/helpers';
import { KpiQueryDto } from '../dtos/kpi-query.dto';
import { BuSetupRepository } from 'src/tc-config/repositories';
export declare class SchedulerService {
    private readonly kpiDataRepository;
    private readonly datalakeHelper;
    private readonly buSetupRepository;
    private readonly syncLogRepository;
    private readonly logger;
    private kpiQueries;
    private bu_list;
    constructor(kpiDataRepository: KpiDataRepository, datalakeHelper: DatalakeConnectionHelper, buSetupRepository: BuSetupRepository, syncLogRepository: SyncLogRepository);
    runScheduler(): Promise<void>;
    private loadKpiQueries;
    pullDataForAllKpis(): Promise<void>;
    pullDataForKpi(kpiQuery: KpiQueryDto, start_date: any, end_date: any): Promise<number>;
    private processAndSaveResults;
    private formatDateWithoutTimezoneShift;
    runRetryScheduler(): Promise<void>;
    retryFailedKpis(): Promise<void>;
}
