import { BusinessEntity, BusinessEntityWithChildren } from '../types';

export function findEntityDetailsById(businessEntityHierarchy: BusinessEntityWithChildren, id: number, foundEntity: BusinessEntity = null): BusinessEntity {
    if (businessEntityHierarchy.id === id) {
        foundEntity = businessEntityHierarchy;
    } else {
        for (const child of businessEntityHierarchy.children) {
            foundEntity = findEntityDetailsById(child, id, foundEntity);
        }
    }
    return foundEntity;
}