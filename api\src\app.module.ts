import { Logger, Modu<PERSON> } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { DatabaseModule } from './database/database.module';
import { ConfigModule } from './config/config.module';
import { CoreModule } from './core/core.module';
import { AuthModule } from './auth/auth.module';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { LoggingInterceptor } from './core/interceptors/logger.interceptor';
import { SharedModule } from './shared/shared.module';
import { SequelizeModule } from '@nestjs/sequelize';
import { getSequelizeOrmConfig } from './database/orm-config';
import { HttpRequestInterceptor } from './core/interceptors';
import { BusinessEntityModule } from './business-entity/business-entity.module';
import { PermissionModule } from './permission/permission.module';
import { ConfigService } from './config/config.service';
import { Dialect } from 'sequelize/types';
import { AttachmentModule } from './attachment/attachments.module';
import { DashboardModule } from './dashboard/dashboard.module';
import { PdfGeneratorModule } from './pdf-generator/pdf-generator.module';
import { SchedulerModule } from './scheduler/scheduler.module';
import { QueueModule } from './queue/queue.module';
import { TcSubmitModule } from './tc-submit/tcsubmit.module';
import { TcConfigModule } from './tc-config/tcconfig.module';
@Module({
	imports: [
		DatabaseModule,
		ConfigModule,
		CoreModule,
		AuthModule,
		SharedModule,
		SequelizeModule.forRootAsync({
			imports: [ConfigModule],
			useFactory: async (configService: ConfigService) => {
				const { database } = configService.getAppConfig();
				const { dialect, host, password, db, port, username, schema, enableSSL } = database;
				return {
					dialect: dialect as Dialect,
					host,
					port,
					database: db,
					username,
					password,
					schema,
					logging: false,
					...getSequelizeOrmConfig(enableSSL),
				};
			},
			inject: [ConfigService],
		}),
		BusinessEntityModule,
		PermissionModule,
		AttachmentModule,
		DashboardModule,
		SchedulerModule,
		PdfGeneratorModule,
		QueueModule,
		TcSubmitModule,
		TcConfigModule,
	],
	controllers: [AppController],
	providers: [
		AppService,
		Logger,
		{
			provide: APP_INTERCEPTOR,
			useClass: LoggingInterceptor,
		},
		{
			provide: APP_INTERCEPTOR,
			useClass: HttpRequestInterceptor,
		},
	],
})
export class AppModule {}
