import { MutationParameters, CurrentContext, FindFilters } from '../types';
export interface IBaseRepo<M> {
    save(model: M, currentContext: CurrentContext, params: MutationParameters): Promise<M | null>;
    findById(id: number, findFilters?: FindFilters, throwException?: boolean): Promise<M | null>;
    findAll(): Promise<M[] | null>;
    deleteById(id: number, currentContext: CurrentContext): Promise<boolean | null>;
    findByIds(ids: number[], findFilters?: FindFilters): Promise<M[] | null>;
    deleteByIds(ids: number[], currentContext: CurrentContext): Promise<boolean | null>;
}
