import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class BusinessEntityRoleResponseDto {
	@ApiProperty({ name: 'systemName' })
	@Expose({ name: 'systemName' })
	public system_name: string;

	@ApiProperty({ name: 'fullName' })
	@Expose({ name: 'fullName' })
	public full_name: string;

	constructor(partial: Partial<BusinessEntityRoleResponseDto> = {}) {
		Object.assign(this, partial);
	}
}
