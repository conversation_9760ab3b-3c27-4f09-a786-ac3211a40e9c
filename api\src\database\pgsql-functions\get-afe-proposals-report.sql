CREATE OR REPLACE FUNCTION dev.get_afe_proposals_report(
  p_conditions JSON,  
  login_id text,
  locations INTEGER[]
) RETURNS TABLE (
  "Business Unit" character varying, 
  "Proposal Type" character varying, 
  "AFE Number" character varying, 
  "AFE Name" character varying, 
  "Budget Type" text, 
  "Total Expenditure" text, 
  "Submitter" character varying, 
  "Submission Date" text, 
  "Expense Summary" text,
  "Budget Reference Number" text,
  "Status" text
) LANGUAGE plpgsql AS $function$ 
DECLARE 
  v_request_types INTEGER[];
  v_budget_types text[];
  v_statuses text[];
  v_business_entities INTEGER[];
  v_from_submission_date timestamp;
  v_to_submission_date timestamp;
  v_from_approval_date timestamp;
  v_to_approval_date timestamp;
  v_submitted_by text;
  v_afe_types text[];
  v_project_components INTEGER[];
  v_from_amount FLOAT;
  v_to_amount FLOAT;
  v_project_name text;
  v_afe_reference_number text;
BEGIN 
  v_request_types := (
    select array_agg(ele :: text :: int) 
    from json_array_elements(p_conditions -> 'requestTypes') ele
  );
  v_budget_types := (
    SELECT '{' || array_to_string(array_agg(ele :: text), ',')|| '}' 
    FROM json_array_elements(p_conditions -> 'budgetTypes') ele
  );
  v_statuses := (
    SELECT '{' || array_to_string(array_agg(ele :: text), ',')|| '}' 
    FROM json_array_elements(p_conditions -> 'statuses') ele
  );
  v_business_entities := (
    select array_agg(ele :: text :: int) 
    from json_array_elements(p_conditions -> 'businessEntities') ele
  );
  v_from_submission_date := (p_conditions ->> 'fromSubmissionDate'):: timestamp;
  v_to_submission_date := (p_conditions ->> 'toSubmissionDate'):: timestamp;
  v_from_approval_date := (p_conditions ->> 'fromApprovalDate'):: timestamp;
  v_to_approval_date := (p_conditions ->> 'toApprovalDate'):: timestamp;
  v_submitted_by := (p_conditions ->> 'submittedBy'):: text;
  v_afe_types := (
    SELECT '{' || array_to_string(array_agg(ele :: text), ',')|| '}' 
    FROM json_array_elements(p_conditions -> 'afeTypes') ele
  );
  v_project_components := (
    select array_agg(ele :: text :: int) 
    from json_array_elements(p_conditions -> 'projectComponents') ele
  );
  v_from_amount := (p_conditions ->> 'fromAmount'):: FLOAT;
  v_to_amount := (p_conditions ->> 'toAmount'):: FLOAT;
  v_project_name := (p_conditions ->> 'projectName'):: text;
  v_afe_reference_number := (p_conditions ->> 'afeReferenceNumber'):: text;
  RETURN QUERY SELECT 
    AfeProposal.entity_title as "Business Unit", 
    (
      select art.title 
      from dev.afe_request_types as art 
      where AfeProposal.afe_request_type_id = art.id
    ) as "Proposal Type", 
    AfeProposal.project_reference_number AS "AFE Number", 
    AfeProposal.name AS "AFE Name", 
  CASE 
    WHEN AfeProposal.budget_type = 'BUDGETED' THEN 'Budgeted' 
    WHEN AfeProposal.budget_type = 'UNBUDGETED' THEN 'Budgeted' 
    WHEN AfeProposal.budget_type = 'MIXED' THEN 'Budgeted & Unbudgeted' 
    ELSE 'N/A' 
  END AS "Budget Type", 
  TRIM(
    LEADING 
    FROM 
      TO_CHAR(AfeProposal.total_amount, '999,999,999,999,999.99')
  ) AS "Total Expenditure", 
  AfeProposal.submitter_id AS "Submitter", 
  TO_CHAR(AfeProposal.created_on, 'Mon DD YYYY HH:MIAM') AS "Submission Date", 
  (
    SELECT 
      STRING_AGG(
        afeProposalAmountSplits.object_title || ' - ' || TRIM(
          LEADING 
          FROM 
            TO_CHAR(afeProposalAmountSplits.amount, '999,999,999,999,999.99')
        ), 
        '; '
      ) 
    FROM 
      dev.afe_proposal_amount_splits as apms 
    WHERE 
      "type" = 'PROJECT_COMPONENT_SPLIT' 
      AND apms.afe_proposal_id = AfeProposal.id 
      AND apms.object_title IS NOT NULL 
      AND apms.amount IS NOT NULL 
    GROUP BY 
      AfeProposal.id
  ) AS "Expense Summary",
  (
    SELECT 
      STRING_AGG(apms.object_title, '; ') 
    FROM 
      dev.afe_proposal_amount_splits AS apms
    WHERE 
      "type" = 'BUDGET_REFERENCE_SPLIT' 
      AND apms.afe_proposal_id = AfeProposal.id 
    GROUP BY 
      AfeProposal.id
  ) AS "Budget Reference Number",
  (
    SELECT COALESCE(
      (
        AfeProposal.user_status || '(' || (
          SELECT 
            STRING_AGG((u.value->>'firstName') || ' ' || (u.value->>'lastName'), ', ') 
          FROM 
            dev.afe_proposal_approvers as apa, jsonb_array_elements(apa.other_info ->'usersDetail') u
          WHERE 
            apa.afe_proposal_id = AfeProposal.id 
            AND apa.action_status = 'IN_PROGRESS'
          GROUP BY 
            AfeProposal.id
        ) || ')'
      ),
      AfeProposal.user_status
    )
  ) AS "Status"
FROM 
  dev.afe_proposals AS AfeProposal 
  LEFT OUTER JOIN dev.afe_request_types AS afeRequestType ON AfeProposal.afe_request_type_id = afeRequestType.id 
  LEFT OUTER JOIN dev.afe_proposal_amount_splits AS afeProposalAmountSplits ON AfeProposal.id = afeProposalAmountSplits.afe_proposal_id 
  AND afeProposalAmountSplits.type = 'PROJECT_COMPONENT_SPLIT' 
WHERE
  (
    (
      (
        v_request_types IS NULL 
        OR AfeProposal.afe_request_type_id = ANY(v_request_types)
      ) 
      AND (
        v_budget_types IS NULL 
        OR AfeProposal.budget_type = ANY(array_append(cast(v_budget_types as dev.enum_afe_proposals_budget_type[]), 'MIXED'))
      ) 
      AND (
        v_statuses IS NULL 
        OR AfeProposal.internal_status = ANY(cast(v_statuses as dev.enum_afe_proposals_internal_status[]))
      ) 
      AND (
        v_business_entities IS NULL 
        OR AfeProposal.entity_id = ANY(v_business_entities)
      ) 
      AND (
        v_from_submission_date IS NULL 
        OR AfeProposal.created_on >= v_from_submission_date
      ) 
      AND (
        v_to_submission_date IS NULL 
        OR AfeProposal.created_on <= v_to_submission_date
      ) 
      AND (
        v_from_approval_date IS NULL 
        OR AfeProposal.updated_on >= v_from_approval_date
      ) 
      AND (
        v_to_approval_date IS NULL 
        OR AfeProposal.updated_on <= v_to_approval_date
      ) 
      AND (
        v_submitted_by IS NULL 
        OR AfeProposal.created_by = v_submitted_by
      ) 
      AND (
        v_afe_types IS NULL 
        OR AfeProposal.category = ANY(cast(v_afe_types as dev.enum_afe_proposals_category[]))
      ) 
      AND (
        v_from_amount IS NULL 
        OR AfeProposal.total_amount >= v_from_amount
      ) 
      AND (
        v_to_amount IS NULL 
        OR AfeProposal.total_amount <= v_to_amount
      ) 
      AND (
        v_project_name IS NULL 
        OR AfeProposal.name ilike '%' || v_project_name || '%'
      ) 
      AND (
        v_afe_reference_number IS NULL 
        OR AfeProposal.project_reference_number ilike '%' || v_afe_reference_number || '%'
      )
      AND (
        v_project_components IS NULL 
        OR (afeProposalAmountSplits.type = 'PROJECT_COMPONENT_SPLIT' AND afeProposalAmountSplits.object_id = ANY(v_project_components))
      )
    ) 
    AND (
      AfeProposal."readers" @> jsonb_build_array(jsonb_build_object('loginId', login_id))
      OR AfeProposal."created_by" = login_id 
      OR AfeProposal."data" @> jsonb_build_object('projectDetails', jsonb_build_object('projectLeader', jsonb_build_object('loginId', login_id)))
      OR AfeProposal."entity_id"= ANY(locations)
    )
   AND AfeProposal.deleted = false
   AND AfeProposal.active = true
  ) 
GROUP BY 
  AfeProposal.id 
ORDER BY 
  AfeProposal.created_on desc;
END;
$function$;