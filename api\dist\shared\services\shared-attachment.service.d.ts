import { AttachmentApiClient } from 'src/shared/clients/attachment-api.client';
import { AttachmentResponseDto } from '../dtos/attachment-response.dto';
import { ATTACHMENT_ENTITY_TYPE } from '../enums';
import { Attachment, BulkUploadResponse } from '../types/attachment.type';
export declare class SharedAttachmentService {
    private readonly attachmentApiClient;
    constructor(attachmentApiClient: AttachmentApiClient);
    addBulkAttachment(attachments: Attachment[], entityId: number, entityType: string, relPath: string, userId: string): Promise<BulkUploadResponse[]>;
    updateBulkAttachment(attachments: Attachment[]): Promise<null>;
    deleteBulkAttachment(attachments: AttachmentResponseDto[]): Promise<null>;
    supportingDocumentsActivity(supportingDocuments: Attachment[], entityId: number, entityType: ATTACHMENT_ENTITY_TYPE, relativePath: string, userId: string): Promise<any[]>;
    deleteSupportingDocumentsActivity(supportingDocuments: AttachmentResponseDto[]): Promise<any>;
}
