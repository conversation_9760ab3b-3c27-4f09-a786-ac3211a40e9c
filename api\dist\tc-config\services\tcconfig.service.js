"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TcConfigService = void 0;
const common_1 = require("@nestjs/common");
const helpers_1 = require("../../shared/helpers");
const repositories_1 = require("../repositories");
const kpi_calculator_service_1 = require("./kpi-calculator.service");
const dtos_1 = require("../dtos");
const repositories_2 = require("../../tc-submit/repositories");
let TcConfigService = class TcConfigService {
    constructor(buSetupRepository, kpiConfigRepository, kpiDataRepository, kpiCalculatorService, modelRepository) {
        this.buSetupRepository = buSetupRepository;
        this.kpiConfigRepository = kpiConfigRepository;
        this.kpiDataRepository = kpiDataRepository;
        this.kpiCalculatorService = kpiCalculatorService;
        this.modelRepository = modelRepository;
    }
    getAllKpiConfigs(model_id) {
        return __awaiter(this, void 0, void 0, function* () {
            let kpiConfigs = yield this.kpiConfigRepository.findAll();
            var model = yield this.modelRepository.findById(model_id);
            if (!model) {
                throw new Error(`Model with ID ${model_id} not found`);
            }
            kpiConfigs = yield this.kpiCalculatorService.filterKpisByExcludedEquipment(kpiConfigs, model.entity_id);
            return (0, helpers_1.multiObjectToInstance)(dtos_1.KpiConfigResponseDto, kpiConfigs);
        });
    }
    getKpiConfigByCode(business_entity_id, systemCodes, year, inputValues = {}) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.kpiCalculatorService.getKpiDataBySystemCodes(business_entity_id, systemCodes, inputValues);
        });
    }
    getAllKpisAndValues(business_entity_id, inputValues = {}, overrideFetchMechanism, startDate, endDate) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.kpiCalculatorService.getAllKpisAndValues(business_entity_id, inputValues, overrideFetchMechanism, startDate, endDate);
        });
    }
    getKpisByTags(entityId, tags, year, inputValues = {}) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.kpiCalculatorService.getKpisByTags(entityId, tags, year, inputValues);
        });
    }
    getKpiConfigsByTags(tags) {
        return __awaiter(this, void 0, void 0, function* () {
            const allKpiConfigs = yield this.kpiConfigRepository.findAll();
            const filteredKpiConfigs = allKpiConfigs.filter(kpi => {
                const kpiTags = Array.isArray(kpi.tags)
                    ? kpi.tags
                    : typeof kpi.tags === 'string'
                        ? JSON.parse(kpi.tags)
                        : [];
                return tags.some(tag => kpiTags.includes(tag));
            });
            return (0, helpers_1.multiObjectToInstance)(dtos_1.KpiConfigResponseDto, filteredKpiConfigs);
        });
    }
};
TcConfigService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [repositories_1.BuSetupRepository,
        repositories_1.KpiConfigRepository,
        repositories_1.KpiDataRepository,
        kpi_calculator_service_1.KpiCalculatorService,
        repositories_2.ModelRepository])
], TcConfigService);
exports.TcConfigService = TcConfigService;
//# sourceMappingURL=tcconfig.service.js.map