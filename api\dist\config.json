{"database": {"host": "psql-hoapps-nonprod.postgres.database.azure.com", "port": 5432, "username": "cadevuser", "password": "LG(h]8SV5X", "db": "appsgoe", "dialect": "postgres", "schema": "dev_cop", "enableSSL": true}, "azureAD": {"authority": "https://login.microsoftonline.com", "tennantId": "2bd16c9b-7e21-4274-9c06-7919f7647bbb", "clientId": "1adb1c92-7181-447b-ae47-d5d5bbab1b65", "clientSecret": "****************************************", "version": "v2.0", "discovery": ".well-known/openid-configuration", "audience": "api://1adb1c92-7181-447b-ae47-d5d5bbab1b65", "graphApiUrl": "https://graph.microsoft.com", "graphApiVersion": "v1.0", "scope": ["General"]}, "swagger": {"user": "user", "password": "password"}, "microservices": {"adminApi": {"url": "https://honodedev.dpworld.com/adminapi/api/", "tennantId": "56b27696-13a4-4b01-82ac-0001aeace8b6", "appId": "2b15b878-d172-4946-8531-3279cbeab37b"}, "requestApi": {"url": "https://honodedev.dpworld.com/requestapi/api/", "tennantId": "56b27696-13a4-4b01-82ac-0001aeace8b6", "appId": "2b15b878-d172-4946-8531-3279cbeab37b"}, "notificationApi": {"url": "https://honodedev.dpworld.com/notification/api/", "tennantId": "56b27696-13a4-4b01-82ac-0001aeace8b6", "appId": "2b15b878-d172-4946-8531-3279cbeab37b"}}, "uiClient": {"baseUrl": "http://localhost:4200", "task": {"relativeUrl": "/tasks/{{taskId}}"}}, "webClientConfig": {"apiBaseUrl": "http://localhost:5100/api", "msDetail": {"authority": "https://login.microsoftonline.com/2bd16c9b-7e21-4274-9c06-7919f7647bbb", "clientId": "e602d1a3-8381-4200-9e11-aedeeeac5b9e", "redirectUrl": "http://localhost:4200", "scope": ["api://1adb1c92-7181-447b-ae47-d5d5bbab1b65/General"], "graphUrl": "https://graph.microsoft.com/v1.0/users", "graphScope": ["User.Read.All"]}}, "sourceConfig": {"synapse": {"dialect": "mssql", "host": "cdpz-prod-sharing-synapse-ondemand.sql.azuresynapse.net", "port": 1433, "username": "cdp_terminalopstool_login", "password": "d0jy440h-A*L", "database": "cdp_prod_serving", "schema": "rpt_pt_tos"}, "baportal": {"dialect": "mssql", "host": "***********", "port": 1433, "username": "tpaportal_DashboardUser", "password": "UKLXM2w22030$4", "database": "GOPS_TPAPORTAL", "schema": "dbo"}, "databricks": {"jdbcUrl": "**************************************************************************************************************************", "username": "your-databricks-username", "password": "your-databricks-password", "httpPath": "/sql/1.0/warehouses/your-warehouse-id"}}, "logLevel": ["error"]}