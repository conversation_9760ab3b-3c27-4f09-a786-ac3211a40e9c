import { LoggerService } from 'src/core/services';
import { HISTORY_ENTITY_TYPE } from '../enums';
import { HttpService } from '../services/http.service';
import { AddHistoryRequest, CopyHistoryRequest, HistoryResponse } from '../types';
export declare class HistoryApiClient {
    private readonly requestApiProvider;
    private readonly loggerService;
    constructor(requestApiProvider: HttpService, loggerService: LoggerService);
    getRequestHistory(entityId: number, entityType: HISTORY_ENTITY_TYPE): Promise<HistoryResponse[]>;
    addRequestHistory(payload: AddHistoryRequest, throwError?: boolean): Promise<null>;
    addBulkRequestHistory(payload: AddHistoryRequest[], throwError?: boolean): Promise<null>;
    copyRequestHistory(payload: CopyHistoryRequest, throwError?: boolean): Promise<null>;
    copyBulkRequestHistory(payload: CopyHistoryRequest[], throwError?: boolean): Promise<null>;
}
