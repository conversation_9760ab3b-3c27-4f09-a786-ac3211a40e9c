'use strict';

const kpiQueries = [
  { kpi_code: 'total_move', query: "SELECT Location_Code, Year, Month, Day, SUM(MOVES) as kpi_value FROM quay GROUP BY Location_Code, Year, Month, Day", source_db: 'terminal_db' },
  { kpi_code: 'total_teu', query: "SELECT Location_Code, Year, Month, Day, SUM(TEU) as kpi_value FROM quay GROUP BY Location_Code, Year, Month, Day", source_db: 'terminal_db' },
  { kpi_code: 'total_reefer_move', query: "SELECT Location_Code, Year, Month, Day, SUM(MOVES) as kpi_value FROM quay where CATAGORY= 'IMPORT' and STATUS = 'FULL' and COMMODITY_TYPE = 'REEFER' GROUP BY Location_Code, Year, Month, Day", source_db: 'terminal_db' },
  // Add all the other queries here...
];

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const env = process.env;
    
    // Create the kpi_queries table
    await queryInterface.createTable(
      { schema: env['DB_SCHEMA'], tableName: 'kpi_queries' },
      {
        id: {
          type: Sequelize.DataTypes.INTEGER,
          autoIncrement: true,
          primaryKey: true,
        },
        kpi_code: {
          type: Sequelize.DataTypes.STRING,
          allowNull: false,
        },
        query: {
          type: Sequelize.DataTypes.TEXT,
          allowNull: false,
        },
        source_db: {
          type: Sequelize.DataTypes.STRING,
          allowNull: false,
        },
        description: {
          type: Sequelize.DataTypes.STRING,
          allowNull: true,
        },
        is_active: {
          type: Sequelize.DataTypes.BOOLEAN,
          allowNull: false,
          defaultValue: true,
        },
        last_run_at: {
          type: Sequelize.DataTypes.DATE,
          allowNull: true,
        },
        created_at: {
          type: Sequelize.DataTypes.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        },
        updated_at: {
          type: Sequelize.DataTypes.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        },
      }
    );

    // Seed the table with the provided KPI queries
    const records = kpiQueries.map(query => ({
      ...query,
      created_at: new Date(),
      updated_at: new Date(),
    }));

    await queryInterface.bulkInsert(
      { schema: env['DB_SCHEMA'], tableName: 'kpi_queries' },
      records
    );
  },

  down: async (queryInterface, Sequelize) => {
    const env = process.env;
    await queryInterface.dropTable({ schema: env['DB_SCHEMA'], tableName: 'kpi_queries' });
  }
};