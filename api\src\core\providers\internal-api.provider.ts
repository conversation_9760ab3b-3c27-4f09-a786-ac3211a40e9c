import { ConfigService } from 'src/config/config.service';
import { INTERNAL_API } from 'src/shared/constants';
import { HttpService } from 'src/shared/services/http.service';

export const InternalApiProviders = [
	{
		provide: INTERNAL_API.ADMIN_API_PROVIDER,
		useFactory: (configService: ConfigService) => {
			const { microservices } = configService.getAppConfig();
			const { adminApi } = microservices;
			const { url, tennantId, appId } = adminApi;
			return new HttpService().withBaseUrl(url).withHeaders({
				tennantId: tennantId,
				applicationid: appId,
			});
		},
		inject: [ConfigService],
	},
	{
		provide: INTERNAL_API.REQUEST_API_PROVIDER,
		useFactory: (configService: ConfigService) => {
			const { microservices } = configService.getAppConfig();
			const { requestApi } = microservices;
			const { url, tennantId, appId } = requestApi;
			return new HttpService().withBaseUrl(url).withHeaders({
				tennantId: tennantId,
				applicationid: appId,
			});
		},
		inject: [ConfigService],
	},
	{
		provide: INTERNAL_API.NOTIFICATION_API_PROVIDER,
		useFactory: (configService: ConfigService) => {
			const { microservices } = configService.getAppConfig();
			const { notificationApi } = microservices;
			const { url, tennantId, appId } = notificationApi;
			return new HttpService().withBaseUrl(url).withHeaders({
				tennantId: tennantId,
				applicationid: appId,
			});
		},
		inject: [ConfigService],
	},
];
