import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsArray, IsNotEmpty, IsOptional } from 'class-validator';

export class KpiConfigResponseDto {
	@ApiProperty({ type: String })
	@Expose()
	public system_code: string;

	@ApiProperty({ type: String })
	@Expose()
	public title: string;

	@ApiProperty({ type: String })
	@Expose()
	public detail : string;

	@ApiProperty({ type: Object })
    @Expose()
	public tags: JSON | null;

	@ApiProperty({ type: String })
	@Expose()
	public kpi_type : string;

	@ApiProperty({ type: Object })
    @Expose()
	public kpi_config: JSON | null;

	@ApiProperty({ type: Object })
    @Expose()
	public applicable_equipment_type: any | null;

	@ApiProperty({ type: Object })
    @Expose()
	public dependent_kpis: JSON | null;

	@ApiProperty({ type: <PERSON>olean })
	@Expose()
	public is_mandatory : Boolean;

	@ApiProperty({ type: Boolean })
	@Expose()
	public override_scenario : Boolean;
}
