import { Injectable, Logger } from '@nestjs/common';
import { SyncLogRepository } from '../repositories';
import { KpiDataRepository } from 'src/tc-config/repositories';
import { DatalakeConnectionHelper } from 'src/shared/helpers';
import * as fs from 'fs';
import * as path from 'path';
import { KpiQueryDto } from '../dtos/kpi-query.dto';
import { BuSetupRepository } from 'src/tc-config/repositories';
import * as _ from 'lodash';
import { SYSTEM_USER } from 'src/shared/constants';
import { SyncLog } from '../models';

@Injectable()
export class SchedulerService {
	private readonly logger = new Logger(SchedulerService.name);
	private kpiQueries: KpiQueryDto[] = [];
	// private start_date: any;
	// private end_date: any;
	private bu_list: any = [];

	constructor(
		private readonly kpiDataRepository: KpiDataRepository,
		private readonly datalakeHelper: DatalakeConnectionHelper,
		private readonly buSetupRepository: BuSetupRepository,
		private readonly syncLogRepository: SyncLogRepository,
	) {}

	public async runScheduler() {
		this.logger.log('Starting data pull scheduler');
		try {
			await this.loadKpiQueries();
			this.logger.log('Data pull completed successfully');
		} catch (error) {
			this.logger.error(`Error running data pull scheduler: ${error.message}`);
		}
	}

	private async loadKpiQueries(is_retry = false) {
		try {
			// Path to the JSON file containing KPI queries
			const filePath = path.resolve(__dirname, '../../shared/files/kpi_pull_queries.json');

			// Read and parse the JSON file
			const fileContent = fs.readFileSync(filePath, 'utf8');
			const data = JSON.parse(fileContent);

			if (Array.isArray(data.KPIs)) {
				this.kpiQueries = data.KPIs;
				this.logger.log(`Loaded ${this.kpiQueries.length} KPI queries from file`);

				// Automatically pull data for all KPIs after loading
                if(!is_retry) {
                    await this.pullDataForAllKpis();
                } else {
                    await this.retryFailedKpis();
                }
			} else {
				this.logger.error('Invalid KPI queries file format: KPIs array not found');
			}
		} catch (error) {
			this.logger.error(`Error loading KPI queries from file: ${error.message}`);
		}
	}

	public async pullDataForAllKpis(): Promise<void> {
		try {
			this.logger.log(`Found ${this.kpiQueries.length} KPI queries to process`);
			this.bu_list = await this.buSetupRepository.findAll();

			// Calculate start and end dates (previous month to current month)
			const today = new Date();

			// Start date: First day of previous month
			// When current month is January (0), previous month is December (11) of previous year
			const startYear = today.getMonth() === 0 ? today.getFullYear() - 1 : today.getFullYear();
			const startMonth = today.getMonth() === 0 ? 11 : today.getMonth() - 1;
			let start_date = this.formatDateWithoutTimezoneShift(new Date(startYear, startMonth, 1));
			let end_date = this.formatDateWithoutTimezoneShift(today);

			const successKpis: any = [];
			const errorKpis: any = [];
			let totalDataCount = 0;

			// Create a sync log entry to track this synchronization process

			var sunc_log = new SyncLog();
			sunc_log.start_time = new Date();
			sunc_log.sync_status = 'IN_PROGRESS';
			sunc_log.data_date_range = { start_date: start_date, end_date: end_date };
			sunc_log.total_kpis = this.kpiQueries.length;
			sunc_log.success_kpis = 0;
			sunc_log.error_kpis = 0;
			sunc_log.success_kpi_list = null;
			sunc_log.error_kpi_list = null;
			sunc_log.total_data = 0;
			sunc_log.retry_count = 0;

			let syncLog = await this.syncLogRepository.createSyncLog(sunc_log, SYSTEM_USER);

			for await (const kpiQuery of this.kpiQueries) {
				try {
					const dataCount = await this.pullDataForKpi(kpiQuery, start_date, end_date);
					totalDataCount += dataCount;
					successKpis.push(kpiQuery.kpi_code);
				} catch (error) {
					this.logger.error(`Error pulling data for KPI ${kpiQuery.kpi_code}: ${error.message}`);
					errorKpis.push({
						kpi_code: kpiQuery.kpi_code,
						error: error.message,
					});
				}
			}

			syncLog = await this.syncLogRepository.findOne({
				where: { id: syncLog.id },
			});

			syncLog.total_data = totalDataCount;
			syncLog.success_kpis = successKpis.length;
			syncLog.error_kpis = errorKpis.length;
			syncLog.success_kpi_list = successKpis;
			syncLog.error_kpi_list = errorKpis;
			syncLog.end_time = new Date();
			syncLog.sync_status = errorKpis.length > 0 ? 'COMPLETED_WITH_ERRORS' : 'COMPLETED';

			// Update the sync log with the results
			await this.syncLogRepository.updateSyncLog(syncLog.id, syncLog, SYSTEM_USER);

			this.logger.log(
				`Data pull completed. Success: ${successKpis.length}, Errors: ${errorKpis.length}, Total data: ${totalDataCount}`,
			);
		} catch (error) {
			this.logger.error(`Error pulling data for all KPIs: ${error.message}`);
		}
	}

	public async pullDataForKpi(
		kpiQuery: KpiQueryDto,
		start_date: any,
		end_date: any,
	): Promise<number> {
		let { kpi_code, query } = kpiQuery;
		this.logger.log(`Processing KPI: ${kpi_code}`);

		try {
			this.logger.log(`Date range for KPI ${kpi_code}: ${start_date} to ${end_date}`);

			query = query.replace(/@StartDate/g, "'" + start_date + "'");
			query = query.replace(/@EndDate/g, "'" + end_date + "'");

			const results = await this.datalakeHelper.executeQuery(query);
			this.logger.log(`Retrieved ${results.length} records for KPI: ${kpi_code}`);

			// Process and save the results
			await this.processAndSaveResults(kpi_code, results, start_date, end_date);

			// Return the count of processed records
			return results.length;
		} catch (error) {
			throw new Error(`Error executing query for KPI ${kpi_code}: ${error.message}`);
		}
	}

	private async processAndSaveResults(
		kpiCode: string,
		results: any[],
		start_date: any,
		end_date: any,
	): Promise<void> {
		// Skip processing if no results
		if (!results || results.length === 0) {
			this.logger.log(`No results to process for KPI: ${kpiCode}`);
			return;
		}

		// Delete existing data for this KPI code within the date range
		try {
			// Use the start_date and end_date properties for the date range
			const startYear = start_date.split('-')[0];
			const startMonth = start_date.split('-')[1]; // JavaScript months are 0-indexed
			const endYear = end_date.split('-')[0];
			const endMonth = end_date.split('-')[1]; // JavaScript months are 0-indexed

			this.logger.log(
				`Deleting existing data for KPI: ${kpiCode} from ${startYear}-${startMonth} to ${endYear}-${endMonth}`,
			);

			// var record_count = await this.kpiDataRepository.deleteAllKpiCodeByDateRange(
			// 	kpiCode,
			// 	startYear,
			// 	endYear,
			// 	startMonth,
			// 	endMonth,
			// );

			// this.logger.log(`Successfully deleted ${record_count} existing data for KPI: ${kpiCode}`);
		} catch (error) {
			this.logger.error(`Error deleting existing data for KPI ${kpiCode}: ${error.message}`);
		}

		// Group results by Location_Code using Lodash
		const groupedResults = _.groupBy(results, x => x.Location_Code);

		// Process each location group
		for (const [locationCode, locationResults] of Object.entries(groupedResults)) {
			// Skip if locationCode is null or undefined
			if (!locationCode) {
				this.logger.warn(`Skipping null or undefined Location_Code for KPI: ${kpiCode}`);
				continue;
			}

			// Find the business entity for this location
			const businessEntity = this.bu_list.find(bu => bu.entity_code === locationCode);

			if (!businessEntity || !businessEntity.entity_id || !businessEntity.entity_code) {
				this.logger.warn(`Business entity not found for Location_Code: ${locationCode}, skipping ${locationResults.length} records`);
				continue;
			}

			// Process all results for this location
			let kpi_data_list = [];

			for (const result of locationResults as any[]) {
				const { Day, Month, Year, kpi_value } = result;

				// Skip if any required field is missing
				if (Day === undefined || Month === undefined || Year === undefined || kpi_value === undefined) {
					this.logger.warn(`Skipping record with missing required fields for KPI: ${kpiCode}, Location: ${locationCode}`);
					continue;
				}

				kpi_data_list.push({
					business_entity_code: businessEntity.entity_code,
					business_entity_id: businessEntity.entity_id,
					kpi_code: kpiCode,
					kpi_day: Day,
					kpi_month: Month,
					kpi_year: Year,
					kpi_value: kpi_value,
					created_by: 'SYSTEM',
					updated_by: 'SYSTEM'
				});
			}

			if (kpi_data_list.length === 0) {
				this.logger.log(`No valid data to insert for KPI: ${kpiCode}, Location: ${locationCode}`);
				continue;
			}

			try {
				// Save the KPI values in batches to avoid overwhelming the database
				const batchSize = 100;
				for (let i = 0; i < kpi_data_list.length; i += batchSize) {
					const batch = kpi_data_list.slice(i, i + batchSize);
					await this.kpiDataRepository.insertMany(batch, SYSTEM_USER);
				}
				
				this.logger.log(`Successfully inserted ${kpi_data_list.length} records for KPI: ${kpiCode}, Location: ${locationCode}`);
			} catch (error) {
				this.logger.error(
					`Error saving KPI data for ${kpiCode}, Location: ${locationCode}: ${error.message}`,
				);
			}
		}
	}

	/**
	 * Format a date as YYYY-MM-DD without timezone shifts
	 * @param date The date to format
	 * @returns Formatted date string
	 */
	private formatDateWithoutTimezoneShift(date: Date): string {
		const year = date.getFullYear();
		// Add 1 to month since getMonth() returns 0-11
		const month = String(date.getMonth() + 1).padStart(2, '0');
		const day = String(date.getDate()).padStart(2, '0');
		return `${year}-${month}-${day}`;
	}

    public async runRetryScheduler() {
		this.logger.log('Starting data pull scheduler');
		try {
			await this.loadKpiQueries(true);
			this.logger.log('Data pull completed successfully');
		} catch (error) {
			this.logger.error(`Error running data pull scheduler: ${error.message}`);
		}
	}

	/**
	 * Retry failed KPIs from the last sync log
	 * @returns Promise<void>
	 */
	public async retryFailedKpis(): Promise<void> {
		try {
			// Get the latest sync log
			const latestSyncLog = await this.syncLogRepository.findOne({
				order: [['created_on', 'DESC']],
			});

			if (!latestSyncLog) {
				this.logger.log('No sync logs found to retry');
				return;
			}

			// Check if the latest sync log has errors and retry count is less than 5
			if (
				latestSyncLog.sync_status === 'COMPLETED_WITH_ERRORS' &&
				latestSyncLog.error_kpis > 0 &&
				(latestSyncLog.retry_count === null || latestSyncLog.retry_count <= 5)
			) {
				this.logger.log(`Retrying failed KPIs from sync log ID: ${latestSyncLog.id}`);

				// Load business entities
				this.bu_list = await this.buSetupRepository.findAll();

				// Filter KPI queries to only include failed ones
				const failedKpiCodes = latestSyncLog.error_kpi_list.map(error =>
					typeof error === 'string' ? error : error.kpi_code,
				);

				const kpiQueriesToRetry = this.kpiQueries.filter(query =>
					failedKpiCodes.includes(query.kpi_code),
				);

				if (kpiQueriesToRetry.length === 0) {
					this.logger.warn('No matching KPI queries found for retry');

					latestSyncLog.end_time = new Date();
					latestSyncLog.sync_status = 'COMPLETED';
					await this.syncLogRepository.updateSyncLog(latestSyncLog.id, latestSyncLog, SYSTEM_USER);
					return;
				}

				this.logger.log(`Found ${kpiQueriesToRetry.length} KPI queries to retry`);

				const successKpis = latestSyncLog.success_kpi_list ?? [];
				const errorKpis = [];
				let totalDataCount = latestSyncLog.total_data;

				// Process each KPI query
				for await (const kpiQuery of kpiQueriesToRetry) {
					try {
						const dataCount = await this.pullDataForKpi(
							kpiQuery,
							latestSyncLog.data_date_range.start_date,
							latestSyncLog.data_date_range.end_date,
						);
						totalDataCount += dataCount;
						successKpis.push(kpiQuery.kpi_code);
					} catch (error) {
						this.logger.error(`Error retrying KPI ${kpiQuery.kpi_code}: ${error.message}`);
						errorKpis.push({
							kpi_code: kpiQuery.kpi_code,
							error: error.message,
						});
					}
				}

				latestSyncLog.total_data = totalDataCount;
				latestSyncLog.success_kpis = successKpis.length;
				latestSyncLog.error_kpis = errorKpis.length;
				latestSyncLog.success_kpi_list = successKpis;
				latestSyncLog.error_kpi_list = errorKpis;
				latestSyncLog.end_time = new Date();
                latestSyncLog.retry_count = latestSyncLog.retry_count ? latestSyncLog.retry_count + 1 : 1;
				latestSyncLog.sync_status = errorKpis.length > 0 ? 'COMPLETED_WITH_ERRORS' : 'COMPLETED';

				// Update the sync log with the results
				await this.syncLogRepository.updateSyncLog(latestSyncLog.id, latestSyncLog, SYSTEM_USER);

				this.logger.log(
					`Retry completed. Success: ${successKpis.length}, Errors: ${errorKpis.length}, Total data: ${totalDataCount}`,
				);
			} else {
				if (latestSyncLog.sync_status !== 'COMPLETED_WITH_ERRORS') {
					this.logger.log(
						`Latest sync log (ID: ${latestSyncLog.id}) has status: ${latestSyncLog.sync_status}, no retry needed`,
					);
				} else if (latestSyncLog.retry_count > 5) {
					this.logger.log(
						`Latest sync log (ID: ${latestSyncLog.id}) has reached maximum retry count (${latestSyncLog.retry_count})`,
					);
				} else if (
					latestSyncLog.error_kpis === 0 ||
					!latestSyncLog.error_kpi_list ||
					latestSyncLog.error_kpi_list.length === 0
				) {
					this.logger.log(`Latest sync log (ID: ${latestSyncLog.id}) has no failed KPIs to retry`);
				}
			}
		} catch (error) {
			this.logger.error(`Error retrying failed KPIs: ${error.message}`);
		}
	}
}
